"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PacketCompression = void 0;
const buffer_1 = require("buffer");
class PacketCompression {
    // Comprime um estado do jogador
    static compressPlayerState(state) {
        const buffer = buffer_1.Buffer.alloc(32); // Tamanho fixo otimizado
        let offset = 0;
        // Comprime posição
        offset = this.writeVector3(buffer, offset, state.position);
        // Comprime velocidade
        offset = this.writeVector3(buffer, offset, state.velocity);
        // Comprime orientação (apenas yaw e pitch são necessários)
        offset = this.writeFloat16(buffer, offset, state.orientation.y); // yaw
        offset = this.writeFloat16(buffer, offset, state.orientation.x); // pitch
        // Comprime vida e armadura (0-255)
        buffer.writeUInt8(Math.round(state.health * 2.55), offset++);
        buffer.writeUInt8(Math.round(state.armor * 2.55), offset++);
        // Comprime último input processado
        buffer.writeUInt32LE(state.lastProcessedInput, offset);
        offset += 4;
        return buffer;
    }
    // Descomprime um estado do jogador
    static decompressPlayerState(buffer) {
        let offset = 0;
        // Descomprime posição
        const position = this.readVector3(buffer, offset);
        offset += 12;
        // Descomprime velocidade
        const velocity = this.readVector3(buffer, offset);
        offset += 12;
        // Descomprime orientação
        const yaw = this.readFloat16(buffer, offset);
        offset += 2;
        const pitch = this.readFloat16(buffer, offset);
        offset += 2;
        // Descomprime vida e armadura
        const health = buffer.readUInt8(offset++) / 2.55;
        const armor = buffer.readUInt8(offset++) / 2.55;
        // Descomprime último input processado
        const lastProcessedInput = buffer.readUInt32LE(offset);
        return {
            position,
            velocity,
            orientation: { x: pitch, y: yaw, z: 0 },
            health,
            armor,
            lastProcessedInput,
            id: '', // ID é gerenciado separadamente
            weapons: [] // Armas são enviadas em pacotes separados
        };
    }
    // Comprime um input do jogador
    static compressPlayerInput(input) {
        const buffer = buffer_1.Buffer.alloc(16); // Tamanho fixo otimizado
        let offset = 0;
        // Timestamp (4 bytes)
        buffer.writeUInt32LE(input.timestamp, offset);
        offset += 4;
        // Sequence number (2 bytes)
        buffer.writeUInt16LE(input.sequence, offset);
        offset += 2;
        // Movement (2 bytes)
        const movementByte = Math.round((input.movement.x + 1) * 127) |
            (Math.round((input.movement.y + 1) * 127) << 8);
        buffer.writeUInt16LE(movementByte, offset);
        offset += 2;
        // View angles (4 bytes)
        buffer.writeInt16LE(Math.round(input.view.pitch * 100), offset);
        offset += 2;
        buffer.writeUInt16LE(Math.round(input.view.yaw * 100), offset);
        offset += 2;
        // Actions (1 byte)
        let actionsByte = 0;
        actionsByte |= input.actions.primaryFire ? 1 : 0;
        actionsByte |= input.actions.secondaryFire ? 2 : 0;
        actionsByte |= input.actions.reload ? 4 : 0;
        actionsByte |= input.movement.jump ? 8 : 0;
        actionsByte |= input.movement.crouch ? 16 : 0;
        buffer.writeUInt8(actionsByte, offset);
        return buffer;
    }
    // Métodos auxiliares para compressão de vetores e números
    static writeVector3(buffer, offset, vector) {
        buffer.writeInt32LE(Math.round(vector.x * this.POSITION_PRECISION), offset);
        buffer.writeInt32LE(Math.round(vector.y * this.POSITION_PRECISION), offset + 4);
        buffer.writeInt32LE(Math.round(vector.z * this.POSITION_PRECISION), offset + 8);
        return offset + 12;
    }
    static readVector3(buffer, offset) {
        return {
            x: buffer.readInt32LE(offset) / this.POSITION_PRECISION,
            y: buffer.readInt32LE(offset + 4) / this.POSITION_PRECISION,
            z: buffer.readInt32LE(offset + 8) / this.POSITION_PRECISION
        };
    }
    static writeFloat16(buffer, offset, value) {
        // Implementação simplificada de float16
        const intValue = Math.round(value * this.ROTATION_PRECISION);
        buffer.writeInt16LE(intValue, offset);
        return offset + 2;
    }
    static readFloat16(buffer, offset) {
        return buffer.readInt16LE(offset) / this.ROTATION_PRECISION;
    }
}
exports.PacketCompression = PacketCompression;
// Constantes para quantização
PacketCompression.POSITION_PRECISION = 100; // 2 casas decimais
PacketCompression.ROTATION_PRECISION = 1000; // 3 casas decimais
PacketCompression.VELOCITY_PRECISION = 100; // 2 casas decimais
//# sourceMappingURL=PacketCompression.js.map