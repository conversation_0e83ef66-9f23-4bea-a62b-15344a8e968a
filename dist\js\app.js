// Tactical Nexus - Main Application JavaScript

class TacticalNexusApp {
    constructor() {
        this.currentSection = 'home';
        this.isSearchingMatch = false;
        this.languageManager = window.languageManager;
        this.gameStats = {
            fps: 999,
            ping: 15,
            players: 1337
        };

        this.init();
    }

    async init() {
        console.log('🎮 Inicializando Tactical Nexus...');
        
        // Simula carregamento
        await this.simulateLoading();
        
        // Inicializa interface
        this.setupLanguageSystem();
        this.setupEventListeners();
        this.setupWindowControls();
        this.startPerformanceMonitoring();
        this.loadPlayerData();
        this.loadStoreItems();
        this.setupArsenalInteractions();

        // Mostra interface principal
        this.showMainInterface();
        
        console.log('✅ Tactical Nexus inicializado com sucesso!');
    }

    async simulateLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const loadingProgress = document.getElementById('loading-progress');
        const loadingText = document.getElementById('loading-text');
        
        const loadingSteps = [
            { progress: 10, text: 'Inicializando sistemas...' },
            { progress: 25, text: 'Carregando assets...' },
            { progress: 40, text: 'Conectando aos servidores...' },
            { progress: 60, text: 'Configurando performance...' },
            { progress: 80, text: 'Otimizando para 999 FPS...' },
            { progress: 95, text: 'Finalizando...' },
            { progress: 100, text: 'Pronto!' }
        ];

        for (const step of loadingSteps) {
            loadingProgress.style.width = `${step.progress}%`;
            loadingText.textContent = step.text;
            await this.delay(300 + Math.random() * 200);
        }

        await this.delay(500);
    }

    showMainInterface() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainInterface = document.getElementById('main-interface');
        
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.classList.add('hidden');
            mainInterface.classList.remove('hidden');
        }, 300);
    }

    setupEventListeners() {
        // Navegação
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.navigateToSection(section);
            });
        });

        // Botões principais
        document.getElementById('quick-play-btn')?.addEventListener('click', () => {
            this.startQuickPlay();
        });

        document.getElementById('training-btn')?.addEventListener('click', () => {
            this.showNotification('🎯 Modo de treinamento em breve!', 'info');
        });

        // Cancelar busca
        document.getElementById('cancel-search')?.addEventListener('click', () => {
            this.cancelMatchSearch();
        });

        // Categorias da loja
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.currentTarget.dataset.category;
                this.loadStoreCategory(category);
                
                // Atualiza botão ativo
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Tabs de configurações
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchSettingsTab(tab);
            });
        });

        // Configurações de performance
        document.getElementById('fps-limit')?.addEventListener('change', (e) => {
            const fpsLimit = e.target.value;
            this.updatePerformanceSetting('fpsLimit', fpsLimit);
            this.showNotification(`🎯 FPS limitado a ${fpsLimit}`, 'success');
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') {
                e.preventDefault();
                this.startQuickPlay();
            } else if (e.key === 'F2') {
                e.preventDefault();
                this.navigateToSection('play');
            } else if (e.key === 'F3') {
                e.preventDefault();
                this.navigateToSection('profile');
            } else if (e.key === 'Escape' && this.isSearchingMatch) {
                this.cancelMatchSearch();
            }
        });
    }

    setupWindowControls() {
        if (window.electronAPI) {
            document.getElementById('minimize-btn')?.addEventListener('click', () => {
                window.electronAPI.minimizeWindow();
            });

            document.getElementById('maximize-btn')?.addEventListener('click', () => {
                window.electronAPI.maximizeWindow();
            });

            document.getElementById('close-btn')?.addEventListener('click', () => {
                window.electronAPI.closeWindow();
            });
        }
    }

    navigateToSection(sectionName) {
        // Remove active das seções
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Remove active dos botões de navegação
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Ativa nova seção
        document.getElementById(`${sectionName}-section`)?.classList.add('active');
        document.querySelector(`[data-section="${sectionName}"]`)?.classList.add('active');

        this.currentSection = sectionName;
        console.log(`📍 Navegando para: ${sectionName}`);
    }

    async startQuickPlay() {
        console.log('🎮 Iniciando jogo rápido...');
        this.navigateToSection('play');
        
        // Simula busca por partida
        this.showMatchmakingStatus();
        
        try {
            const match = await this.searchForMatch('team_deathmatch', 'auto');
            this.showNotification(`🎮 Partida encontrada! ID: ${match.matchId}`, 'success');
            
            // Simula início da partida
            setTimeout(() => {
                this.showNotification('🚀 Iniciando partida...', 'info');
                this.hideMatchmakingStatus();
            }, 2000);
            
        } catch (error) {
            this.showNotification('❌ Erro ao buscar partida', 'error');
            this.hideMatchmakingStatus();
        }
    }

    async searchForMatch(gameMode, region) {
        this.isSearchingMatch = true;
        
        const searchSteps = [
            'Analisando jogadores disponíveis...',
            'Verificando latência...',
            'Balanceando equipes...',
            'Selecionando servidor...',
            'Criando partida...'
        ];

        const statusElement = document.getElementById('search-status');
        
        for (let i = 0; i < searchSteps.length; i++) {
            if (!this.isSearchingMatch) throw new Error('Busca cancelada');
            
            statusElement.textContent = searchSteps[i];
            await this.delay(1000 + Math.random() * 1000);
        }

        this.isSearchingMatch = false;
        
        return {
            matchId: `match_${Date.now()}`,
            gameMode,
            region,
            estimatedTime: Math.floor(Math.random() * 30) + 10
        };
    }

    showMatchmakingStatus() {
        document.getElementById('game-modes-grid').classList.add('hidden');
        document.getElementById('matchmaking-status').classList.remove('hidden');
    }

    hideMatchmakingStatus() {
        document.getElementById('matchmaking-status').classList.add('hidden');
        document.getElementById('game-modes-grid').classList.remove('hidden');
    }

    cancelMatchSearch() {
        this.isSearchingMatch = false;
        this.hideMatchmakingStatus();
        this.showNotification('🚫 Busca por partida cancelada', 'info');
    }

    startPerformanceMonitoring() {
        setInterval(() => {
            // Simula métricas de performance
            this.gameStats.fps = Math.floor(Math.random() * 50) + 950; // 950-999 FPS
            this.gameStats.ping = Math.floor(Math.random() * 20) + 10;  // 10-30ms
            this.gameStats.players = Math.floor(Math.random() * 500) + 1000; // 1000-1500 players

            // Atualiza interface
            document.getElementById('fps-counter').textContent = this.gameStats.fps;
            document.getElementById('ping-counter').textContent = this.gameStats.ping;
            document.getElementById('player-counter').textContent = this.gameStats.players.toLocaleString();
        }, 1000);
    }

    async loadPlayerData() {
        try {
            let playerStats, playerEconomy;
            
            if (window.electronAPI) {
                playerStats = await window.electronAPI.game.getPlayerStats();
                playerEconomy = await window.electronAPI.game.getPlayerEconomy();
            } else {
                // Dados simulados para desenvolvimento
                playerStats = {
                    level: 25,
                    experience: 15750,
                    experienceToNext: 2250,
                    kills: 1337,
                    deaths: 420,
                    wins: 89,
                    losses: 31,
                    kdr: 3.18,
                    accuracy: 0.72
                };
                
                playerEconomy = {
                    coins: 15420,
                    premiumCurrency: 250
                };
            }

            // Atualiza interface
            document.getElementById('player-level').textContent = `Nível ${playerStats.level}`;
            document.getElementById('player-coins').textContent = `💰 ${playerEconomy.coins.toLocaleString()}`;
            document.getElementById('player-premium').textContent = `💎 ${playerEconomy.premiumCurrency}`;
            
            // Atualiza estatísticas do perfil
            document.getElementById('kills-stat').textContent = playerStats.kills.toLocaleString();
            document.getElementById('deaths-stat').textContent = playerStats.deaths.toLocaleString();
            document.getElementById('kdr-stat').textContent = playerStats.kdr.toFixed(2);
            document.getElementById('accuracy-stat').textContent = `${Math.round(playerStats.accuracy * 100)}%`;
            
        } catch (error) {
            console.error('Erro ao carregar dados do jogador:', error);
        }
    }

    async loadStoreItems(category = 'weapons') {
        try {
            let items;
            
            if (window.electronAPI) {
                items = await window.electronAPI.game.getStoreItems(category);
            } else {
                // Itens simulados
                items = [
                    {
                        id: 'weapon_ak47',
                        name: 'AK-47',
                        description: 'Rifle de assalto poderoso',
                        category: 'weapon',
                        rarity: 'rare',
                        price: { coins: 5000 },
                        owned: false
                    },
                    {
                        id: 'weapon_awp',
                        name: 'AWP Sniper',
                        description: 'Rifle de precisão letal',
                        category: 'weapon',
                        rarity: 'epic',
                        price: { coins: 12000 },
                        owned: false
                    }
                ];
            }

            this.renderStoreItems(items);
            
        } catch (error) {
            console.error('Erro ao carregar itens da loja:', error);
        }
    }

    loadStoreCategory(category) {
        this.loadStoreItems(category);
    }

    renderStoreItems(items) {
        const container = document.getElementById('store-items');
        container.innerHTML = '';

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'store-item';
            itemElement.innerHTML = `
                <div class="item-header">
                    <h4>${item.name}</h4>
                    <span class="rarity ${item.rarity}">${item.rarity.toUpperCase()}</span>
                </div>
                <p class="item-description">${item.description}</p>
                <div class="item-price">
                    ${item.price.coins ? `💰 ${item.price.coins.toLocaleString()}` : ''}
                    ${item.price.premiumCurrency ? `💎 ${item.price.premiumCurrency}` : ''}
                </div>
                <button class="btn-primary" onclick="app.purchaseItem('${item.id}')">
                    ${item.owned ? 'EQUIPADO' : 'COMPRAR'}
                </button>
            `;
            container.appendChild(itemElement);
        });
    }

    async purchaseItem(itemId) {
        try {
            let result;
            
            if (window.electronAPI) {
                result = await window.electronAPI.game.purchaseItem(itemId, 'coins');
            } else {
                result = { success: true, message: 'Item comprado com sucesso!' };
            }

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.loadPlayerData(); // Atualiza saldo
                this.loadStoreItems(); // Atualiza loja
            } else {
                this.showNotification(result.message, 'error');
            }
            
        } catch (error) {
            this.showNotification('Erro ao comprar item', 'error');
        }
    }

    switchSettingsTab(tabName) {
        // Remove active das tabs
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Ativa nova tab
        document.getElementById(`${tabName}-settings`)?.classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');
    }

    updatePerformanceSetting(setting, value) {
        console.log(`⚙️ Configuração atualizada: ${setting} = ${value}`);
        
        if (window.electronAPI) {
            // Salva configuração via Electron
            window.electronAPI.game.saveGameSettings({
                [setting]: value
            });
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notifications');
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <p>${message}</p>
            </div>
        `;
        
        container.appendChild(notification);
        
        // Remove após 5 segundos
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    setupLanguageSystem() {
        // Configura seletor de idioma
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.value = this.languageManager.getCurrentLanguage();

            languageSelect.addEventListener('change', (e) => {
                this.languageManager.setLanguage(e.target.value);
                this.showNotification('🌐 Idioma alterado com sucesso!', 'success');
            });
        }

        // Aplica traduções iniciais
        this.languageManager.updateInterface();
    }

    setupArsenalInteractions() {
        // Weapon selection
        document.querySelectorAll('.weapon-card').forEach(card => {
            card.addEventListener('click', () => {
                // Remove active from all cards in the same category
                const category = card.closest('.weapon-category');
                category.querySelectorAll('.weapon-card').forEach(c => c.classList.remove('active'));

                // Add active to clicked card
                card.classList.add('active');

                const weaponName = card.querySelector('h4').textContent;
                this.showNotification(`🔫 ${weaponName} selecionada`, 'success');
            });
        });

        // Equipment selection
        document.querySelectorAll('.equipment-card').forEach(card => {
            card.addEventListener('click', () => {
                // Toggle active state
                card.classList.toggle('active');

                const equipmentName = card.querySelector('h4').textContent;
                const isActive = card.classList.contains('active');
                this.showNotification(`${isActive ? '✅' : '❌'} ${equipmentName} ${isActive ? 'equipado' : 'removido'}`, 'info');
            });
        });

        // Range controls
        document.getElementById('test-fire')?.addEventListener('click', () => {
            this.simulateTestFire();
        });

        document.getElementById('zero-weapon')?.addEventListener('click', () => {
            this.simulateWeaponZeroing();
        });

        document.getElementById('full-auto')?.addEventListener('click', () => {
            this.simulateFullAuto();
        });
    }

    simulateTestFire() {
        this.showNotification('🎯 Executando teste de tiro...', 'info');

        // Simulate shot animation
        const target = document.getElementById('target-50m');
        target.style.transform = 'scale(1.05)';

        setTimeout(() => {
            target.style.transform = 'scale(1)';

            // Update accuracy display
            const accuracy = (Math.random() * 10 + 90).toFixed(1);
            document.getElementById('accuracy-display').textContent = `${accuracy}%`;

            this.showNotification(`🎯 Tiro executado! Precisão: ${accuracy}%`, 'success');
        }, 200);
    }

    simulateWeaponZeroing() {
        this.showNotification('🔧 Calibrando mira...', 'info');

        setTimeout(() => {
            const grouping = (Math.random() * 2 + 1).toFixed(1);
            document.getElementById('grouping-display').textContent = `${grouping}cm`;

            this.showNotification('✅ Mira calibrada com sucesso!', 'success');
        }, 1500);
    }

    simulateFullAuto() {
        this.showNotification('🔥 Iniciando rajada completa...', 'info');

        let shots = 0;
        const maxShots = 30;
        const interval = setInterval(() => {
            shots++;
            document.getElementById('shots-display').textContent = `${shots}/${maxShots}`;

            if (shots >= maxShots) {
                clearInterval(interval);
                this.showNotification('🔥 Rajada completa executada!', 'success');

                // Reset after a moment
                setTimeout(() => {
                    document.getElementById('shots-display').textContent = '30/30';
                }, 2000);
            }
        }, 100);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Inicializa aplicação quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TacticalNexusApp();
});

// Listeners para eventos do Electron
if (window.electronAPI) {
    window.electronAPI.onQuickPlay(() => {
        window.app?.startQuickPlay();
    });

    window.electronAPI.onFindMatch(() => {
        window.app?.navigateToSection('play');
    });

    window.electronAPI.onShowStats(() => {
        window.app?.navigateToSection('profile');
    });

    window.electronAPI.onOpenSettings(() => {
        window.app?.navigateToSection('settings');
    });
}
