{"version": 3, "file": "AudioManager.js", "sourceRoot": "", "sources": ["../../src/audio/AudioManager.ts"], "names": [], "mappings": ";;;AA0EA,MAAa,YAAY;IAWrB;QAPQ,WAAM,GAA4B,IAAI,GAAG,EAAE,CAAC;QAC5C,iBAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;QACnD,mBAAc,GAA6B,IAAI,GAAG,EAAE,CAAC;QACrD,kBAAa,GAA4B,IAAI,CAAC;QAC9C,gBAAW,GAAG,GAAG,CAAC;QAClB,mBAAc,GAAG,CAAC,CAAC;QAGvB,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,EAAE,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAEhD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAExC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEnF,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,0BAA0B,IAAI,MAAM,CAAC,CAAC;gBACnE,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACpE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,4CAA4C,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;IACL,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,GAAW;QACnC,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAqB;QACjC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACnE,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACjD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;YACvB,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YAE3B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,KAAK,GAAe,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;YAEnD,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC3C,MAAM,CAAC,WAAW,CACd,OAAO,CAAC,QAAQ,CAAC,CAAC,EAClB,OAAO,CAAC,QAAQ,CAAC,CAAC,EAClB,OAAO,CAAC,QAAQ,CAAC,CAAC,CACrB,CAAC;gBAEF,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACzB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC;YAED,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAElC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,EAAE,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,SAAS,CAAC,EAAU;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC;gBACD,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBACf,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC9B,CAAC;gBACD,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACvB,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,MAAwB;QACxC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAErD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC;YAEhC,kCAAkC;YAClC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAE5B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YAC7C,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;YAE3D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC7C,SAAS,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;YAE/D,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAClC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAwB;QAC9C,IAAI,MAAM,CAAC,QAAQ,GAAG,GAAG;YAAE,OAAO,YAAY,CAAC;QAC/C,IAAI,MAAM,CAAC,QAAQ,GAAG,GAAG;YAAE,OAAO,aAAa,CAAC;QAChD,IAAI,MAAM,CAAC,QAAQ,GAAG,GAAG;YAAE,OAAO,YAAY,CAAC;QAC/C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,mBAAmB,CAAC,QAA6C,EAC9C,WAAgD;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEvC,wBAAwB;QACxB,IAAI,WAAW,IAAI,QAAQ,EAAE,CAAC;YAC1B,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;YACtC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;YACtC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;YACtC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;YACxC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;YACxC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;YACxC,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,OAAO;QACH,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;IAC5B,CAAC;CACJ;AA5LD,oCA4LC"}