{"version": 3, "file": "GeometryOptimizer.js", "sourceRoot": "", "sources": ["../../src/rendering/GeometryOptimizer.ts"], "names": [], "mappings": ";;;AAAA,+DAAqE;AAkCrE,MAAa,iBAAiB;IAK1B;QAFQ,SAAI,GAA0C,IAAI,CAAC;QAGvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,kDAA4B,EAAE,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;gBACpE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,iDAAiD;YACvE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAClB,QAAsB,EACtB,OAAkC,EAClC,SAA8C,EAAE;QAEhD,MAAM,WAAW,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAElD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,uDAAuD;QAEvD,MAAM,aAAa,GAA+B;YAC9C,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,CAAC;gBACT,EAAE,EAAE,EAAE;aACT;YACD,cAAc,EAAE;gBACZ,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,GAAG;aACpB;YACD,WAAW,EAAE;gBACT,KAAK,EAAE,CAAC;aACX;SACJ,CAAC;QAEF,MAAM,WAAW,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QAEpD,IAAI,CAAC;YACD,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YAC9D,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAEpC,uDAAuD;YACvD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;YAElC,2BAA2B;YAC3B,MAAM,eAAe,GAAG,CAAC,GAAG,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC;YACnE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC;YAE/D,MAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YAEzD,MAAM,aAAa,GAAG,iBAAiB,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;YACjF,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAErD,gCAAgC;YAChC,MAAM,gBAAgB,GAAG,YAAY,GAAG,aAAa,CAAC;YACtD,MAAM,iBAAiB,GAAG,CAAC,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,SAAS;YAExD,oBAAoB;YACpB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,WAAW,EAAE;gBAC5C,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,eAAe,EAAE,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG;gBACtE,OAAO,EAAE,GAAG,GAAG,WAAW,CAAC,cAAc,CAAC,WAAW;gBACrD,gBAAgB,EAAE,cAAc;aACnC,CAAC,CAAC;YAEH,OAAO;gBACH,QAAQ,EAAE,iBAAiB;gBAC3B,OAAO,EAAE,gBAAgB;gBACzB,WAAW,EAAE,cAAc;gBAC3B,UAAU,EAAE,aAAa;gBACzB,YAAY;gBACZ,aAAa;gBACb,OAAO,EAAE;oBACL,gBAAgB;oBAChB,gBAAgB,EAAE,cAAc;oBAChC,iBAAiB;oBACjB,gBAAgB,EAAE,UAAU;oBAC5B,aAAa,EAAE,IAAI;iBACtB;aACJ,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;CACJ;AA3GD,8CA2GC"}