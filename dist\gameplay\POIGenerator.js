"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POIGenerator = void 0;
const ContentProductionSystem_1 = require("../content/ContentProductionSystem");
class POIGenerator {
    constructor() {
        this.contentSystem = new ContentProductionSystem_1.ContentProductionSystem();
    }
    async generatePOI(type, position, template) {
        // Define características específicas do POI baseado no tipo
        const poiCharacteristics = this.getPOICharacteristics(type);
        // Gera o layout base do POI
        const layout = this.generatePOILayout(template);
        // Gera os assets necessários
        const assets = await this.generatePOIAssets(type, template);
        // Aplica otimizações específicas do POI
        await this.optimizePOIAssets(assets);
        // Configura o áudio ambiental
        await this.setupPOIAudio(type, position, template.size);
        return {
            layout,
            assets,
            characteristics: poiCharacteristics
        };
    }
    getPOICharacteristics(type) {
        const characteristics = {
            urban: {
                buildingTypes: ['apartment', 'office', 'store', 'parking'],
                propTypes: ['cars', 'dumpsters', 'benches', 'signs'],
                coverTypes: ['walls', 'vehicles', 'containers'],
                soundscape: ['traffic', 'people', 'ambient_city']
            },
            military: {
                buildingTypes: ['barracks', 'hangar', 'warehouse', 'bunker'],
                propTypes: ['barriers', 'vehicles', 'crates', 'sandbags'],
                coverTypes: ['barriers', 'vehicles', 'containers'],
                soundscape: ['radio_static', 'generators', 'distant_gunfire']
            },
            industrial: {
                buildingTypes: ['factory', 'warehouse', 'storage', 'office'],
                propTypes: ['containers', 'machinery', 'pallets', 'drums'],
                coverTypes: ['containers', 'machinery', 'structures'],
                soundscape: ['machinery', 'factory', 'industrial']
            },
            forest: {
                buildingTypes: ['cabin', 'outpost', 'tower', 'ruins'],
                propTypes: ['rocks', 'logs', 'foliage', 'debris'],
                coverTypes: ['trees', 'rocks', 'terrain'],
                soundscape: ['wind', 'leaves', 'birds', 'water']
            }
        };
        return characteristics[type] ?? characteristics.urban;
    }
    generatePOILayout(template) {
        const layout = {
            grid: [],
            spawnPoints: [],
            coverPoints: [],
            routes: []
        };
        // Calcula o número de células baseado no tamanho do template
        const gridSizeX = Math.ceil(template.size.width / 10); // 10m por célula
        const gridSizeZ = Math.ceil(template.size.length / 10);
        // Inicializa o grid
        for (let z = 0; z < gridSizeZ; z++) {
            layout.grid[z] = [];
            for (let x = 0; x < gridSizeX; x++) {
                layout.grid[z][x] = this.generateGridCell(template, x, z);
            }
        }
        // Gera pontos de spawn estratégicos
        layout.spawnPoints = this.generateSpawnPoints(layout.grid, template);
        // Gera pontos de cobertura
        layout.coverPoints = this.generateCoverPoints(layout.grid, template);
        // Gera rotas táticas
        layout.routes = this.generateTacticalRoutes(layout.grid, layout.coverPoints);
        return layout;
    }
    generateGridCell(template, _x, _z) {
        const cell = {
            type: 'empty',
            height: 0,
            walkable: true,
            cover: false
        };
        // Determina o tipo de célula baseado nas densidades do template
        const random = Math.random();
        if (random < template.buildingDensity) {
            cell.type = 'building';
            cell.walkable = false;
            cell.height = Math.random() * template.size.height;
        }
        else if (random < template.buildingDensity + template.propDensity) {
            cell.type = 'prop';
            cell.walkable = true;
            cell.cover = Math.random() < template.coverPercentage;
        }
        return cell;
    }
    generateSpawnPoints(grid, _template) {
        const spawnPoints = [];
        const gridSizeZ = grid.length;
        const gridSizeX = grid[0].length;
        // Gera pontos de spawn nas bordas e em locais estratégicos
        for (let z = 0; z < gridSizeZ; z++) {
            for (let x = 0; x < gridSizeX; x++) {
                if (grid[z][x].walkable && this.isStrategicPosition(x, z, grid)) {
                    spawnPoints.push({ x: x * 10, z: z * 10 });
                }
            }
        }
        return spawnPoints;
    }
    isStrategicPosition(x, z, grid) {
        // Verifica se a posição é estratégica (próxima a cobertura, com boa visibilidade)
        let coverNearby = false;
        let openSightlines = 0;
        for (let dz = -2; dz <= 2; dz++) {
            for (let dx = -2; dx <= 2; dx++) {
                if (grid[z + dz]?.[x + dx]) {
                    if (grid[z + dz][x + dx].cover)
                        coverNearby = true;
                    if (grid[z + dz][x + dx].walkable)
                        openSightlines++;
                }
            }
        }
        return coverNearby && openSightlines >= 12; // Pelo menos 50% de área aberta
    }
    generateCoverPoints(grid, _template) {
        const coverPoints = [];
        const gridSizeZ = grid.length;
        const gridSizeX = grid[0].length;
        // Distribui pontos de cobertura pelo POI
        for (let z = 0; z < gridSizeZ; z++) {
            for (let x = 0; x < gridSizeX; x++) {
                if (grid[z][x].cover) {
                    coverPoints.push({
                        position: { x: x * 10, z: z * 10 },
                        type: grid[z][x].type,
                        height: grid[z][x].height
                    });
                }
            }
        }
        return coverPoints;
    }
    generateTacticalRoutes(grid, coverPoints) {
        const routes = [];
        // Gera rotas entre pontos de cobertura
        for (let i = 0; i < coverPoints.length; i++) {
            for (let j = i + 1; j < coverPoints.length; j++) {
                const route = this.findTacticalRoute(coverPoints[i].position, coverPoints[j].position, grid);
                if (route)
                    routes.push(route);
            }
        }
        return routes;
    }
    findTacticalRoute(_start, _end, _grid) {
        // Implementa A* pathfinding com preferência por rotas com cobertura
        // Retorna array de pontos formando a rota
        return [];
    }
    async generatePOIAssets(type, template) {
        const characteristics = this.getPOICharacteristics(type);
        // Gera os assets necessários baseado nas características do POI
        const buildings = characteristics.buildingTypes.map((buildingType) => ({
            id: `${buildingType}_${Date.now()}`,
            type: buildingType,
            size: template.size,
            processed: false
        }));
        const props = characteristics.propTypes.map((propType) => ({
            id: `${propType}_${Date.now()}`,
            type: propType,
            size: { width: 5, length: 5, height: 3 },
            processed: false
        }));
        return {
            buildings,
            props,
            textures: [],
            ambientSounds: characteristics.soundscape
        };
    }
    async optimizePOIAssets(assets) {
        // Otimiza os assets do POI para garantir performance
        await Promise.all([
            this.contentSystem.queueEnvironmentProduction({
                models: [...assets.buildings, ...assets.props],
                textures: assets.textures,
                ambientSounds: assets.ambientSounds
            })
        ]);
    }
    async setupPOIAudio(type, position, size) {
        const characteristics = this.getPOICharacteristics(type);
        // Configura o sistema de áudio ambiental para o POI
        console.log(`Configurando áudio ambiental para POI ${type} em`, position);
        console.log(`Sons: ${characteristics.soundscape.join(', ')}`);
        // Simula a configuração de áudio (em implementação real seria conectado ao GameAudioSystem)
        const audioConfig = {
            position,
            size,
            sounds: characteristics.soundscape,
            reverb: `${type}_reverb`,
            transitionDistance: 50
        };
        console.log(`Configuração de áudio criada para POI ${type}:`, audioConfig);
    }
}
exports.POIGenerator = POIGenerator;
//# sourceMappingURL=POIGenerator.js.map