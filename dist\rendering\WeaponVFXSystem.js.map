{"version": 3, "file": "WeaponVFXSystem.js", "sourceRoot": "", "sources": ["../../src/rendering/WeaponVFXSystem.ts"], "names": [], "mappings": ";;;AAoCA,MAAa,eAAe;IAuBxB,YAAY,cAAiC,EAAE,MAAuB;QAH9D,iBAAY,GAAW,CAAC,CAAC;QACzB,iBAAY,GAAW,CAAC,CAAC;QAG7B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEO,gBAAgB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,SAAkB,EAAE,MAAwC;QACvF,OAAO;YACH,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;YAChE,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YACxD,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;SACnE,CAAC;IACN,CAAC;IAEO,cAAc,CAAC,QAAiB,EAAE,MAAsC;QAC5E,MAAM,KAAK,GAAG;YACV,QAAQ;YACR,KAAK,EAAE;gBACH,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;gBACvB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;gBACvB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aACpB;YACD,SAAS,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC;YAC/B,MAAM,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC;YACxB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC5B,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEM,gBAAgB,CAAC,QAAiB,EAAE,SAAkB,EAAE,UAAsB;QACjF,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAErD,kEAAkE;QAClE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS;YACT,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,0BAA0B,CAAC;YAChF,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,MAAM,CAAC,KAAK;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM,EAAE,aAAa;YACrB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,UAAU,CAAC,QAAiB,EAAE,SAAkB,EAAE,MAAmB;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtC,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE;YAC5D,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE;YAC/D,eAAe,EAAE,MAAM,CAAC,IAAI;YAC5B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,KAAK,EAAE,GAAG;YACV,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS;SACjD,CAAC,CAAC;QACH,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACZ,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACxB,CAAC,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEM,kBAAkB,CAAC,QAAiB,EAAE,MAAe,EAAE,WAAmB;QAC7E,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAEzC,8BAA8B;QAC9B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAEnD,mDAAmD;QACnD,QAAQ,WAAW,EAAE,CAAC;YAClB,KAAK,OAAO;gBACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACxC,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACxC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACzC,MAAM;YACV;gBACI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,QAAiB,EAAE,MAAe;QACxD,4BAA4B;QAC5B,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;YACrC,MAAM,EAAE,OAAO;YACf,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB,CAAC,QAAiB,EAAE,MAAe;QAC3D,sBAAsB;QACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,GAAG;YACT,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAiB,EAAE,MAAe;QACvD,oBAAoB;QACpB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;YACvC,MAAM,EAAE,UAAU;YAClB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB,CAAC,QAAiB,EAAE,MAAe;QACxD,oBAAoB;QACpB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB,CAAC,QAAiB,EAAE,MAAe;QACxD,sBAAsB;QACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,aAAa;YACrB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAiB,EAAE,MAAe;QACvD,sBAAsB;QACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,QAAiB,EAAE,MAAe;QAC1D,6BAA6B;QAC7B,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1B,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;IACP,CAAC;IAEO,eAAe;QACnB,oCAAoC;QACpC,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,kBAAkB;QACtB,OAAO;YACH,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACrC,IAAI,EAAE,GAAG;SACZ,CAAC;IACN,CAAC;IAEO,mBAAmB;QACvB,gDAAgD;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;YACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAChB,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE;gBAC5B,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAClC,KAAK,EAAE,KAAK;aACf,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,mBAAmB;QACvB,2BAA2B;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAChB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAC9B,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAC5B,GAAG,EAAE,QAAQ;aAChB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAiB,EAAE,MAAe,EAAE,WAAmB;QAC1E,8CAA8C;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,uDAAuD;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEnD,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,MAAM;YACN,GAAG,EAAE,CAAC;SACT,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB;QACpB,uCAAuC;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;IAC9F,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAExC,6BAA6B;QAC7B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,CAAC,GAAG,GAAG,SAAS,EAAE,CAAC;gBACxB,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC;gBACtB,WAAW,GAAG,KAAK,CAAC;YACxB,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,gBAAgB,CAAC,WAAmB;QACxC,uDAAuD;QACvD,MAAM,UAAU,GAAG,SAAS,WAAW,EAAE,CAAC;QAC1C,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACpF,CAAC;IAEO,mBAAmB,CAAC,GAAW;QACnC,qCAAqC;QACrC,OAAO,IAAI,CAAC,CAAC,sCAAsC;IACvD,CAAC;IAEO,yBAAyB;QAC7B,kCAAkC;QAClC,OAAO;QACH,kCAAkC;SACrC,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,SAAiB;QACxC,IAAI,CAAC,SAAS;aACT,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;aAC5B,OAAO,CAAC,KAAK,CAAC,EAAE;YACb,uCAAuC;YACvC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,GAAG,SAAS,CAAC,CAAC,YAAY;YACpD,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YACrD,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAErD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;YAExD,sBAAsB;YACtB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,SAAS,CAAC;YAC/D,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,SAAS,CAAC;YAC/D,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,SAAS,CAAC;QACnE,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,oBAAoB,CAAC,UAAsB;QAC/C,6CAA6C;QAC7C,MAAM,OAAO,GAAG;YACZ,KAAK,EAAE;gBACH,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,GAAG;gBACb,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;aAC5C;YACD,MAAM,EAAE;gBACJ,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;aAC5C;YACD,OAAO,EAAE;gBACL,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;aAC5C;SACJ,CAAC;QAEF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;IAChD,CAAC;IAEM,MAAM,CAAC,SAAiB;QAC3B,gCAAgC;QAChC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAEnC,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC3B,KAAK,CAAC,GAAG,IAAI,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAClC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAC/D,CAAC;IACN,CAAC;;AAxYL,0CAyYC;AAxY2B,0CAA0B,GAAG,EAAE,AAAL,CAAM;AAChC,iCAAiB,GAAG,EAAE,AAAL,CAAM;AACvB,oCAAoB,GAAG,EAAE,AAAL,CAAM;AAC1B,0BAAU,GAAG,GAAG,AAAN,CAAO"}