/* Tactical Nexus - Advanced Military Interface */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 0, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 120, 0, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #0d1421 50%, #1a0f0f 75%, #0a0a0a 100%);
    color: #e0e0e0;
    overflow: hidden;
    user-select: none;
    letter-spacing: 0.5px;
    text-shadow: 0 0 2px rgba(0, 255, 0, 0.1);
}

.tactical-nexus-app {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    max-width: 600px;
}

.game-title {
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #ff4444, #ff8800, #ffff00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 30px rgba(255, 68, 68, 0.5);
    letter-spacing: 3px;
    text-transform: uppercase;
}

.game-subtitle {
    font-size: 1.2rem;
    color: #ff4444;
    margin-bottom: 3rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
}

.loading-bar-container {
    margin: 2rem 0;
}

.loading-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff4444, #ff8800, #ffff00);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

.loading-text {
    margin-top: 1rem;
    color: #aaa;
}

.system-info {
    margin-top: 2rem;
    color: #666;
    font-size: 0.9rem;
}

/* Main Interface */
.main-interface {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.hidden {
    display: none !important;
}

/* Header */
.game-header {
    height: 60px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.95), rgba(20, 0, 0, 0.9), rgba(0, 0, 0, 0.95));
    border-bottom: 2px solid rgba(255, 68, 68, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    -webkit-app-region: drag;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.header-left, .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, #ff4444, #ff8800);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.server-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4444;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.performance-stats {
    display: flex;
    gap: 30px;
}

.stat {
    text-align: center;
}

.stat .label {
    display: block;
    font-size: 0.8rem;
    color: #888;
}

.stat .value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #ff4444;
    text-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff4444, #ff8800);
    border: 2px solid rgba(255, 68, 68, 0.5);
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.player-details {
    display: flex;
    flex-direction: column;
}

.username {
    font-weight: bold;
    font-size: 0.9rem;
}

.level {
    font-size: 0.8rem;
    color: #888;
}

.currency {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 0.8rem;
}

.window-controls {
    display: flex;
    gap: 5px;
    -webkit-app-region: no-drag;
}

.window-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    border-radius: 3px;
    transition: background 0.2s;
}

.window-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.window-btn.close:hover {
    background: #ff4444;
}

/* Navigation */
.main-navigation {
    height: 80px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.9), rgba(20, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
    border-bottom: 2px solid rgba(255, 68, 68, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 68, 68, 0.2);
    color: #888;
    cursor: pointer;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 100px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-btn:hover {
    color: #fff;
    background: rgba(255, 68, 68, 0.2);
    border-color: rgba(255, 68, 68, 0.5);
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.nav-btn.active {
    color: #ff4444;
    background: rgba(255, 68, 68, 0.2);
    border-color: #ff4444;
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.4);
}

.nav-btn .icon {
    font-size: 1.5rem;
}

.nav-btn .label {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 40px;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Home Section */
.welcome-area {
    text-align: center;
    margin-bottom: 60px;
}

.welcome-area h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff4444, #ff8800);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
}

.welcome-area p {
    font-size: 1.2rem;
    color: #ccc;
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.quick-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn-primary, .btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.btn-primary {
    background: linear-gradient(45deg, #ff4444, #ff8800);
    color: white;
    border: 1px solid rgba(255, 68, 68, 0.5);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 68, 68, 0.4);
    background: linear-gradient(45deg, #ff5555, #ff9900);
}

.btn-secondary {
    background: rgba(0, 0, 0, 0.5);
    color: #ff4444;
    border: 1px solid rgba(255, 68, 68, 0.3);
    text-shadow: 0 0 5px rgba(255, 68, 68, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 68, 68, 0.1);
    border-color: rgba(255, 68, 68, 0.5);
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.2);
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.3rem;
}

.featured-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.featured-modes h3, .news-feed h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #ff4444;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.mode-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.mode-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

.mode-card.featured {
    border-color: #ff4444;
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
    background: rgba(255, 68, 68, 0.05);
}

.mode-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.mode-card h4 {
    font-size: 1.2rem;
    margin-bottom: 8px;
}

.mode-card p {
    color: #aaa;
    margin-bottom: 10px;
}

.mode-time {
    font-size: 0.9rem;
    color: #ff4444;
    text-transform: uppercase;
    font-weight: 600;
}

.news-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.news-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
}

.news-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.news-content {
    flex: 1;
}

.news-content h4 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.news-content p {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.news-time {
    font-size: 0.8rem;
    color: #666;
}

/* Game Modes Grid */
.game-modes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

/* Matchmaking Status */
.matchmaking-status {
    text-align: center;
    padding: 60px 20px;
}

.searching-animation {
    max-width: 400px;
    margin: 0 auto;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid #ff4444;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .featured-content {
        grid-template-columns: 1fr;
    }
    
    .mode-grid {
        grid-template-columns: 1fr;
    }
}

/* Store Section */
.store-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

.category-btn {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-btn.active {
    background: #00ff88;
    color: black;
    border-color: #00ff88;
}

.store-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.store-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.store-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Profile Section */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
}

.stat-card h3 {
    color: #00ff88;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.stats-grid .stat {
    text-align: left;
}

.stats-grid .stat .label {
    color: #aaa;
    font-size: 0.9rem;
}

.stats-grid .stat .value {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Settings Section */
.settings-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

.tab-btn {
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #00ff88;
    color: black;
    border-color: #00ff88;
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.setting-group {
    margin-bottom: 30px;
}

.setting-group h3 {
    color: #00ff88;
    margin-bottom: 15px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item label {
    font-weight: 500;
}

.setting-item select,
.setting-item input[type="range"] {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    min-width: 150px;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    max-width: 350px;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-color: #00ff88;
}

.notification.error {
    border-color: #ff4444;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .game-header {
        padding: 0 10px;
    }

    .main-navigation {
        gap: 20px;
    }

    .nav-btn {
        min-width: 80px;
        padding: 8px 12px;
    }

    .main-content {
        padding: 20px;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .featured-content {
        grid-template-columns: 1fr;
    }

    .profile-stats {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
