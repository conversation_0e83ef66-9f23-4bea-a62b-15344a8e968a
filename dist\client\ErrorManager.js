"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorManager = void 0;
class ErrorManager {
    constructor() {
        this.performanceMonitorInterval = null;
        this.reports = new Map();
        this.subscribers = new Set();
        this.initializeGlobalHandlers();
        this.startPerformanceMonitoring();
    }
    initializeGlobalHandlers() {
        // Handler de erros não capturados
        window.onerror = (message, source, lineno, colno, error) => {
            this.handleError('error', {
                message: String(message),
                source,
                lineno,
                colno,
                stack: error?.stack
            });
        };
        // Handler de rejeições de promessas não tratadas
        window.onunhandledrejection = (event) => {
            this.handleError('error', {
                message: `Unhandled Promise Rejection: ${event.reason}`,
                stack: event.reason?.stack
            });
        };
        // Handler de erros de recursos (imagens, scripts, etc)
        document.addEventListener('error', (event) => {
            const target = event.target;
            if (target.tagName) {
                this.handleError('error', {
                    message: `Resource Error: Failed to load ${target.tagName.toLowerCase()}`,
                    source: target.src
                });
            }
        }, true);
    }
    startPerformanceMonitoring() {
        this.performanceMonitorInterval = setInterval(async () => {
            const metrics = await this.collectPerformanceMetrics();
            this.analyzePerformance(metrics);
        }, 1000);
    }
    async collectPerformanceMetrics() {
        const metrics = {
            fps: await this.measureFPS(),
            memory: await this.getMemoryUsage(),
            cpu: await this.getCPUMetrics(),
            gpu: await this.getGPUMetrics(),
            network: await this.getNetworkMetrics()
        };
        return metrics;
    }
    analyzePerformance(metrics) {
        // Verifica FPS
        if (metrics.fps < ErrorManager.FPS_THRESHOLD) {
            this.handlePerformanceIssue('fps_drop', {
                message: `Low FPS detected: ${metrics.fps}`,
                metrics
            });
        }
        // Verifica uso de memória
        if (metrics.memory.used / metrics.memory.total > ErrorManager.MEMORY_THRESHOLD) {
            this.handlePerformanceIssue('high_memory', {
                message: `High memory usage: ${Math.round(metrics.memory.used / metrics.memory.total * 100)}%`,
                metrics
            });
        }
        // Verifica temperatura
        if (metrics.cpu.temperature > ErrorManager.TEMPERATURE_THRESHOLD ||
            metrics.gpu.temperature > ErrorManager.TEMPERATURE_THRESHOLD) {
            this.handlePerformanceIssue('high_temperature', {
                message: 'High temperature detected',
                metrics
            });
        }
        // Verifica rede
        if (metrics.network.packetLoss > ErrorManager.PACKET_LOSS_THRESHOLD) {
            this.handlePerformanceIssue('network_issues', {
                message: `High packet loss: ${Math.round(metrics.network.packetLoss * 100)}%`,
                metrics
            });
        }
    }
    handleError(type, data) {
        const report = this.createReport(type, {
            message: data.message,
            stack: data.stack,
            severity: this.calculateSeverity(data)
        });
        this.processReport(report);
    }
    handlePerformanceIssue(issue, data) {
        const report = this.createReport('performance_issue', {
            message: `Performance Issue: ${issue}`,
            context: data.metrics,
            severity: this.calculatePerformanceSeverity(data.metrics)
        });
        this.processReport(report);
    }
    createReport(type, data) {
        return {
            id: this.generateId(),
            timestamp: Date.now(),
            type,
            severity: data.severity || 'medium',
            message: data.message || 'Unknown error',
            stack: data.stack,
            context: {
                ...data.context,
                gameState: this.getCurrentGameState()
            },
            clientInfo: this.getClientInfo()
        };
    }
    processReport(report) {
        // Armazena o relatório
        this.reports.set(report.id, report);
        // Notifica subscribers
        this.subscribers.forEach(subscriber => {
            try {
                subscriber(report);
            }
            catch (error) {
                console.error('Error in crash report subscriber:', error);
            }
        });
        // Envia para o servidor se for crítico
        if (report.severity === 'critical') {
            this.sendToServer(report);
        }
        // Log local para debug
        this.logReport(report);
    }
    calculateSeverity(data) {
        if (data.message?.includes('crash') || data.stack?.includes('fatal')) {
            return 'critical';
        }
        if (data.message?.includes('memory') || data.message?.includes('gpu')) {
            return 'high';
        }
        if (data.message?.includes('network') || data.message?.includes('timeout')) {
            return 'medium';
        }
        return 'low';
    }
    calculatePerformanceSeverity(metrics) {
        if (metrics.fps < 30 || metrics.memory.used / metrics.memory.total > 0.95) {
            return 'critical';
        }
        if (metrics.fps < 60 || metrics.memory.used / metrics.memory.total > 0.9) {
            return 'high';
        }
        if (metrics.fps < 120 || metrics.memory.used / metrics.memory.total > 0.8) {
            return 'medium';
        }
        return 'low';
    }
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }
    async sendToServer(report) {
        try {
            const response = await fetch('/api/crash-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            });
            if (!response.ok) {
                console.error('Failed to send crash report:', await response.text());
            }
        }
        catch (error) {
            console.error('Error sending crash report:', error);
        }
    }
    logReport(report) {
        console.group(`Crash Report: ${report.id}`);
        console.log('Type:', report.type);
        console.log('Severity:', report.severity);
        console.log('Message:', report.message);
        if (report.stack) {
            console.log('Stack:', report.stack);
        }
        console.log('Context:', report.context);
        console.log('Client Info:', report.clientInfo);
        console.groupEnd();
    }
    generateId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    getCurrentGameState() {
        // Implementação dependerá do gerenciador de estado do jogo
        return 'unknown';
    }
    getClientInfo() {
        return {
            version: '1.0.0', // Versão do jogo
            os: navigator.platform,
            cpu: navigator.hardwareConcurrency.toString() + ' cores',
            gpu: this.getGPUInfo(),
            ram: this.getSystemMemory(),
            drivers: {
                gpu: 'unknown', // Será preenchido pela API WebGL
                directx: 'unknown' // Será preenchido pela API WebGL
            }
        };
    }
    async measureFPS() {
        return new Promise(resolve => {
            requestAnimationFrame(t1 => {
                requestAnimationFrame(t2 => {
                    resolve(1000 / (t2 - t1));
                });
            });
        });
    }
    async getMemoryUsage() {
        // Implementação dependerá das APIs disponíveis
        return {
            total: performance.memory?.jsHeapSizeLimit || 0,
            used: performance.memory?.usedJSHeapSize || 0,
            peak: performance.memory?.totalJSHeapSize || 0
        };
    }
    async getCPUMetrics() {
        // Implementação dependerá das APIs disponíveis
        return {
            usage: 0,
            temperature: 0
        };
    }
    async getGPUMetrics() {
        // Implementação dependerá das APIs disponíveis
        return {
            usage: 0,
            temperature: 0,
            vram: {
                total: 0,
                used: 0
            }
        };
    }
    async getNetworkMetrics() {
        // Implementação dependerá do sistema de rede
        return {
            ping: 0,
            packetLoss: 0,
            bandwidth: {
                up: 0,
                down: 0
            }
        };
    }
    getGPUInfo() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl');
        if (!gl)
            return 'Unknown';
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (!debugInfo)
            return 'Unknown';
        return gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
    }
    getSystemMemory() {
        return navigator.deviceMemory || 0;
    }
    destroy() {
        if (this.performanceMonitorInterval) {
            clearInterval(this.performanceMonitorInterval);
        }
        this.subscribers.clear();
    }
}
exports.ErrorManager = ErrorManager;
ErrorManager.MEMORY_THRESHOLD = 0.9; // 90% uso de memória
ErrorManager.FPS_THRESHOLD = 60;
ErrorManager.TEMPERATURE_THRESHOLD = 80; // 80°C
ErrorManager.PACKET_LOSS_THRESHOLD = 0.05; // 5% perda de pacotes
//# sourceMappingURL=ErrorManager.js.map