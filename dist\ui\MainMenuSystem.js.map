{"version": 3, "file": "MainMenuSystem.js", "sourceRoot": "", "sources": ["../../src/ui/MainMenuSystem.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAyCtC,MAAa,cAAe,SAAQ,qBAAY;IAM5C;QACI,KAAK,EAAE,CAAC;QALJ,cAAS,GAAe,EAAE,CAAC;QAE3B,iBAAY,GAAkC,IAAI,GAAG,EAAE,CAAC;QAK5D,IAAI,CAAC,SAAS,GAAG;YACb,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE,KAAK;YAChB,aAAa,EAAE,EAAE;SACpB,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAClD,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,SAAS,GAAG;YACb;gBACI,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,WAAW;gBAC1B,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;aACb;YACD;gBACI,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,4BAA4B;gBACzC,WAAW,EAAE,eAAe;gBAC5B,aAAa,EAAE,WAAW;gBAC1B,UAAU,EAAE,aAAa;gBACzB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;aACb;YACD;gBACI,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,4BAA4B;gBACzC,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,WAAW;gBAC1B,UAAU,EAAE,aAAa;gBACzB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,IAAI;aACb;YACD;gBACI,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,8BAA8B;gBAC3C,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,WAAW;gBAC1B,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,IAAI;aACb;YACD;gBACI,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,iCAAiC;gBAC9C,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,WAAW;gBAC1B,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,IAAI;aACb;SACJ,CAAC;IACN,CAAC;IAEO,eAAe;QACnB,gCAAgC;QAChC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,sDAAsD;QACtD,MAAM,UAAU,GAAG;YACf,gBAAgB;YAChB,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;SACpB,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,cAAc;QAClB,wCAAwC;QACxC,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAwMhB,CAAC;QAEF,oDAAoD;QACpD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB;QACvB,yCAAyC;QACzC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,wBAAwB;QACxB,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAgB,EAAE,EAAE;YAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,MAAc,EAAE,EAAE;YACzC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,cAAc,CAAC,QAAgB;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;QAC3C,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,QAAe,CAAC;QAE7C,OAAO,CAAC,GAAG,CAAC,mBAAmB,YAAY,SAAS,QAAQ,EAAE,CAAC,CAAC;QAEhE,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,EAAE,YAAY;YAClB,EAAE,EAAE,QAAQ;YACZ,KAAK,EAAE,IAAI,CAAC,SAAS;SACxB,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB,CAAC,UAAmC;QAChD,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;QAEvC,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,0CAA0C,UAAU,EAAE,QAAQ,WAAW,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC;QAE3G,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,eAAe,CAAC,YAA6D;QACzE,MAAM,gBAAgB,GAAiB;YACnC,GAAG,YAAY;YACf,EAAE,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC3E,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEvD,2BAA2B;QAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,YAAY,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACvE,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,2BAA2B;QAC3B,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,cAAc,CAAC,MAAc;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QAEjE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,kCAAkC,MAAM,EAAE,CAAC,CAAC;YACxD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,GAAG,QAAQ,CAAC,IAAI,kCAAkC;aAC9D,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;IAChD,CAAC;IAEO,UAAU,CAAC,OAAgB;QAC/B,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,OAAO,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,6BAA6B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,SAAS,CAAC,SAAiB;QAC/B,2BAA2B;QAC3B,OAAO,CAAC,GAAG,CAAC,wBAAwB,SAAS,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,YAAY;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IACjC,CAAC;IAED,YAAY;QACR,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAED,sBAAsB,CAAC,cAAsB;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;QACrF,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED,qBAAqB;QACjB,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtC,CAAC;IAED,0BAA0B;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IACpE,CAAC;CACJ;AAjcD,wCAicC;AAED,kBAAe,cAAc,CAAC"}