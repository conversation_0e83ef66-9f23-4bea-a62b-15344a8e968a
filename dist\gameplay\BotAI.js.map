{"version": 3, "file": "BotAI.js", "sourceRoot": "", "sources": ["../../src/gameplay/BotAI.ts"], "names": [], "mappings": ";;;AAIA,MAAa,KAAK;IAmBd,YAAY,EAAU,EAAE,QAAiB,EAAE,UAAsB;QAC7D,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG;YACd,gBAAgB,EAAE,IAAI,GAAG,EAAE;YAC3B,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;SACjB,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,SAAiB,EAAE,QAAgB;QAC7C,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAE9B,mDAAmD;QACnD,IAAI,GAAG,GAAG,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;QACpC,CAAC;QAED,kDAAkD;QAClD,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,wBAAwB,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC;YACnF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAClC,CAAC;QAED,qDAAqD;QACrD,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,oBAAoB,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC;YAC3E,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QAC9B,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,CAAC,+CAA+C;QAEzE,sBAAsB;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,wBAAwB;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,oCAAoC;QACpC,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEO,YAAY;QAChB,uDAAuD;QACvD,8BAA8B;QAC9B,kCAAkC;QAClC,oDAAoD;IACxD,CAAC;IAEO,aAAa;QACjB,yBAAyB;QACzB,wBAAwB;QACxB,iCAAiC;IACrC,CAAC;IAEO,0BAA0B;QAC9B,gCAAgC;QAChC,+BAA+B;QAC/B,8BAA8B;IAClC,CAAC;IAEO,cAAc,CAAC,QAAgB;QACnC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACf,iDAAiD;YACjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,MAAM;gBACP,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;QACd,CAAC;IACL,CAAC;IAEO,kBAAkB;QACtB,6CAA6C;QAC7C,4CAA4C;IAChD,CAAC;IAEO,gBAAgB;QACpB,iBAAiB;QACjB,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO;QAEjC,4BAA4B;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAEO,QAAQ,CAAC,KAAc,EAAE,GAAY;QACzC,2CAA2C;QAC3C,iDAAiD;QACjD,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,cAAc,CAAC,SAAiB;QACpC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE1C,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEpE,gCAAgC;QAChC,IAAI,CAAC,QAAQ,GAAG;YACZ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ;YAC5B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC;SACrB,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG;YACZ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS;YAChD,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS;YAChD,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS;SACnD,CAAC;QAEF,sCAAsC;QACtC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,SAAiB,EAAE,QAAgB;QACrD,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,CAAC,8CAA8C;QAExE,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,WAAW;gBACZ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC7B,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAC9B,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAC9B,MAAM;QACd,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,IAAa,EAAE,EAAW;QACjD,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACzB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAE5C,OAAO;YACH,CAAC,EAAE,EAAE,GAAG,MAAM;YACd,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,EAAE,GAAG,MAAM;SACjB,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,KAAc;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,wBAAwB;IACjD,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,4CAA4C;IACpC,gBAAgB;QACpB,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;IACrD,CAAC;IAEO,YAAY;QAChB,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEO,sBAAsB;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,kBAAkB;IACV,YAAY;QAChB,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QACzB,+BAA+B;IACnC,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,4BAA4B;IAChC,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,4BAA4B;IAChC,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,+CAA+C;IACnD,CAAC;IAED,mCAAmC;IAC3B,YAAY,CAAC,SAAiB;QAClC,+BAA+B;IACnC,CAAC;IAEO,aAAa,CAAC,SAAiB;QACnC,4BAA4B;IAChC,CAAC;IAEO,aAAa,CAAC,SAAiB;QACnC,4BAA4B;IAChC,CAAC;IAEO,gBAAgB;QACpB,mCAAmC;IACvC,CAAC;IAEO,cAAc;QAClB,6BAA6B;IACjC,CAAC;IAEO,aAAa;QACjB,kCAAkC;IACtC,CAAC;IAEO,eAAe;QACnB,0CAA0C;IAC9C,CAAC;IAEO,eAAe;QACnB,oCAAoC;IACxC,CAAC;;AA3SL,sBA4SC;AA3S2B,gCAA0B,GAAG,GAAG,CAAC,CAAC,KAAK;AACvC,8BAAwB,GAAG,GAAG,CAAC,CAAC,KAAK;AACrC,0BAAoB,GAAG,GAAG,CAAC,CAAC,KAAK"}