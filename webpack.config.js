const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: 'production',
    target: 'electron-main',

    entry: {
        main: './src/electron/main.ts',
        preload: './src/electron/preload.ts'
    },

    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name].js',
        clean: true
    },

    module: {
        rules: [
            {
                test: /\.ts$/,
                use: {
                    loader: 'ts-loader',
                    options: {
                        transpileOnly: true,
                        compilerOptions: {
                            noEmit: false
                        }
                    }
                },
                exclude: /node_modules/
            },
            {
                test: /\.js$/,
                exclude: /node_modules/
            }
        ]
    },

    resolve: {
        extensions: ['.ts', '.js', '.json']
    },

    plugins: [
        new HtmlWebpackPlugin({
            template: './src/renderer/index.html',
            filename: 'index.html'
        }),

        new CopyWebpackPlugin({
            patterns: [
                {
                    from: './src/renderer/styles',
                    to: 'styles'
                },
                {
                    from: './src/renderer/js',
                    to: 'js'
                },
                {
                    from: './assets',
                    to: 'assets',
                    noErrorOnMissing: true
                }
            ]
        })
    ],

    externals: {
        'electron': 'commonjs electron'
    },

    node: {
        __dirname: false,
        __filename: false
    }
};
