{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020", "dom", "webworker"], "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "outDir": "./dist", "rootDir": "./src", "sourceMap": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "allowJs": true}, "include": ["src/electron/**/*", "src/renderer/**/*", "src/multiplayer/**/*", "src/progression/**/*", "src/economy/**/*", "src/ui/**/*"], "exclude": ["node_modules", "**/*.test.ts", "src/audio/**/*", "src/client/**/*", "src/content/**/*", "src/gameplay/**/*", "src/netcode/**/*", "src/rendering/**/*", "src/server/**/*", "src/tests/**/*"]}