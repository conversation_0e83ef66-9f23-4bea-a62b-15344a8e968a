{"version": 3, "file": "PerformanceOptimizer.js", "sourceRoot": "", "sources": ["../../src/client/PerformanceOptimizer.ts"], "names": [], "mappings": ";;;AAUA,MAAa,oBAAoB;IAuB7B,YACI,SAAqC,EAAE,EACvC,YAA0B,EAC1B,OAAY;QAdR,iBAAY,GAAY,KAAK,CAAC;QAgBlC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,oBAAoB,CAAC,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;QACpE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QAEnC,IAAI,CAAC,gCAAgC,EAAE,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,gCAAgC;QACpC,4CAA4C;QAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAW;QAC5C,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAE/B,4CAA4C;YAC5C,IAAI,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,EAAE,CAAC;gBACzD,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC;YAC3D,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC5E,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;YACxD,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,EAAE,CAAC;YAChE,CAAC;YAED,4CAA4C;YAC5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAExC,gDAAgD;QAChD,IAAI,QAAQ,EAAE,CAAC;YACX,0BAA0B;YAC1B,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAEvC,+BAA+B;YAC/B,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAEhC,kCAAkC;YAClC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAExC,gCAAgC;YAChC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAEzC,IAAI,SAAS,EAAE,CAAC;YACZ,wCAAwC;YACxC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;YAEzE,gCAAgC;YAChC,SAAS,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC;YAE1C,qCAAqC;YACrC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAErC,IAAI,OAAO,EAAE,CAAC;YACV,qCAAqC;YACrC,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAEnC,0CAA0C;YAC1C,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAE/B,qBAAqB;YACrB,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACjC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,8BAA8B;QAC9B,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,mBAAmB,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,sBAAsB,EAAE,CAAC;QAEjD,2BAA2B;QAC3B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,CAAC,EAAE,EAAE,CAAC;QAChB,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAErC,IAAI,OAAO,EAAE,CAAC;YACV,gCAAgC;YAChC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAEjC,kDAAkD;YAClD,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAErC,gCAAgC;YAChC,OAAO,CAAC,0BAA0B,EAAE,CAAC;QACzC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACnC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,qBAAqB,CAAC,GAAG,EAAE;gBACvB,MAAM,OAAO,GAAG;oBACZ,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE;oBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,sCAAsC;iBACzC,CAAC;gBACF,OAAO,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,WAAW,CAAC,UAAe,EAAE,UAAe;QAChD,2CAA2C;QAC3C,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;QAC7D,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhF,OAAO,cAAc,IAAI,iBAAiB,CAAC;IAC/C,CAAC;IAEM,kBAAkB;QACrB,gCAAgC;QAChC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC/C,CAAC;;AAnNL,oDAoNC;AAnN2B,mCAAc,GAAsB;IACxD,SAAS,EAAE,GAAG;IACd,cAAc,EAAE,GAAG,EAAE,eAAe;IACpC,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,EAAE;IACd,uBAAuB,EAAE,EAAE,CAAC,KAAK;CACpC,AANqC,CAMpC"}