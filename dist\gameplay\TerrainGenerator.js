"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerrainGenerator = void 0;
const PerformanceMetrics_1 = require("../rendering/PerformanceMetrics");
const GeometryOptimizer_1 = require("../rendering/GeometryOptimizer");
const TextureCompressor_1 = require("../rendering/TextureCompressor");
class TerrainGenerator {
    constructor(config) {
        this.biomeMap = new Map();
        this.p = new Uint8Array(512);
        this.config = config;
        this.heightData = new Float32Array((config.size / config.resolution + 1) *
            (config.size / config.resolution + 1));
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
        this.initializeBiomes();
        this.geometryOptimizer = new GeometryOptimizer_1.GeometryOptimizer();
        this.textureCompressor = new TextureCompressor_1.TextureCompressor(this.metrics);
    }
    initializeBiomes() {
        this.biomeMap = new Map();
        this.biomeMap.set('urban', {
            type: 'urban',
            heightRange: { min: 0, max: 30 },
            roughness: 0.3,
            noiseScale: 0.8,
            texturePath: 'textures/terrain/urban'
        });
        this.biomeMap.set('military', {
            type: 'military',
            heightRange: { min: 5, max: 40 },
            roughness: 0.5,
            noiseScale: 1.2,
            texturePath: 'textures/terrain/military'
        });
        this.biomeMap.set('forest', {
            type: 'forest',
            heightRange: { min: 10, max: 100 },
            roughness: 0.8,
            noiseScale: 1.5,
            texturePath: 'textures/terrain/forest'
        });
        this.biomeMap.set('industrial', {
            type: 'industrial',
            heightRange: { min: 0, max: 25 },
            roughness: 0.4,
            noiseScale: 1.0,
            texturePath: 'textures/terrain/industrial'
        });
    }
    async generateTerrain(regions) {
        // Gera altura base usando ruído de Perlin
        this.generateBaseHeight();
        // Aplica influência das regiões
        for (const region of regions) {
            const biome = this.biomeMap.get(region.type);
            if (biome) {
                this.applyRegionInfluence(region, biome);
            }
        }
        // Suaviza o terreno
        for (let i = 0; i < this.config.smoothingPasses; i++) {
            this.smoothTerrain();
        }
        // Otimiza a geometria
        const geometry = await this.generateGeometry();
        const optimizedGeometry = await this.optimizeGeometry(geometry);
        // Gera texturas do terreno
        const textures = await this.generateTextures(regions);
        return {
            geometry: optimizedGeometry,
            textures,
            heightData: this.heightData
        };
    }
    generateBaseHeight() {
        const size = this.config.size / this.config.resolution + 1;
        for (let z = 0; z < size; z++) {
            for (let x = 0; x < size; x++) {
                const height = this.generatePerlinNoise(x, z);
                this.setHeight(x, z, height);
            }
        }
    }
    generatePerlinNoise(x, z) {
        // Implementação simplificada de ruído de Perlin
        const frequency = 0.005;
        const amplitude = this.config.maxHeight - this.config.minHeight;
        let noise = 0;
        let f = frequency;
        let a = 1.0;
        for (let i = 0; i < 4; i++) {
            noise += this.noise2D(x * f, z * f) * a;
            f *= 2;
            a *= 0.5;
        }
        return this.config.minHeight + (noise + 1) * 0.5 * amplitude;
    }
    noise2D(x, y) {
        // Implementação básica de ruído 2D
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;
        x -= Math.floor(x);
        y -= Math.floor(y);
        const u = this.fade(x);
        const v = this.fade(y);
        const A = this.p[X] + Y;
        const B = this.p[X + 1] + Y;
        return this.lerp(v, this.lerp(u, this.grad(this.p[A], x, y), this.grad(this.p[B], x - 1, y)), this.lerp(u, this.grad(this.p[A + 1], x, y - 1), this.grad(this.p[B + 1], x - 1, y - 1)));
    }
    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    lerp(t, a, b) {
        return a + t * (b - a);
    }
    grad(hash, x, y) {
        const h = hash & 15;
        const grad = 1 + (h & 7);
        return ((h & 8) ? -grad : grad) * x + ((h & 4) ? -grad : grad) * y;
    }
    applyRegionInfluence(region, biome) {
        const size = this.config.size / this.config.resolution + 1;
        const centerX = Math.floor(region.center.x / this.config.resolution);
        const centerZ = Math.floor(region.center.z / this.config.resolution);
        const radiusVerts = Math.floor(region.radius / this.config.resolution);
        for (let z = 0; z < size; z++) {
            for (let x = 0; x < size; x++) {
                const dx = x - centerX;
                const dz = z - centerZ;
                const distance = Math.sqrt(dx * dx + dz * dz);
                if (distance <= radiusVerts) {
                    const influence = 1 - (distance / radiusVerts);
                    const currentHeight = this.getHeight(x, z);
                    const targetHeight = this.generateBiomeHeight(biome, x, z);
                    const newHeight = currentHeight * (1 - influence) +
                        targetHeight * influence;
                    this.setHeight(x, z, newHeight);
                }
            }
        }
    }
    generateBiomeHeight(biome, x, z) {
        const baseNoise = this.noise2D(x * biome.noiseScale, z * biome.noiseScale);
        return biome.heightRange.min +
            (baseNoise + 1) * 0.5 *
                (biome.heightRange.max - biome.heightRange.min);
    }
    smoothTerrain() {
        const size = this.config.size / this.config.resolution + 1;
        const smoothed = new Float32Array(this.heightData.length);
        for (let z = 0; z < size; z++) {
            for (let x = 0; x < size; x++) {
                let sum = 0;
                let count = 0;
                for (let dz = -1; dz <= 1; dz++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        const nx = x + dx;
                        const nz = z + dz;
                        if (nx >= 0 && nx < size && nz >= 0 && nz < size) {
                            sum += this.getHeight(nx, nz);
                            count++;
                        }
                    }
                }
                smoothed[z * size + x] = sum / count;
            }
        }
        this.heightData = smoothed;
    }
    async generateGeometry() {
        // Gera malha do terreno
        const size = this.config.size / this.config.resolution + 1;
        const vertices = [];
        const indices = [];
        const uvs = [];
        // Gera vértices e UVs
        for (let z = 0; z < size; z++) {
            for (let x = 0; x < size; x++) {
                vertices.push(x * this.config.resolution, this.getHeight(x, z), z * this.config.resolution);
                uvs.push(x / (size - 1), z / (size - 1));
            }
        }
        // Gera índices para triângulos
        for (let z = 0; z < size - 1; z++) {
            for (let x = 0; x < size - 1; x++) {
                const i0 = z * size + x;
                const i1 = i0 + 1;
                const i2 = (z + 1) * size + x;
                const i3 = i2 + 1;
                indices.push(i0, i2, i1);
                indices.push(i1, i2, i3);
            }
        }
        return {
            vertices: new Float32Array(vertices),
            indices: new Uint32Array(indices),
            uvs: new Float32Array(uvs)
        };
    }
    async optimizeGeometry(geometry) {
        return await this.geometryOptimizer.optimizeGeometry(geometry.vertices, geometry.indices, {
            simplification: {
                targetError: 0.8,
                maxDeviation: 0.1
            }
        });
    }
    async generateTextures(regions) {
        // Gera textura do terreno baseada nas regiões
        const textureSize = 4096; // 4K textura
        const textureData = new Uint8Array(textureSize * textureSize * 4);
        // Para cada pixel da textura
        for (let y = 0; y < textureSize; y++) {
            for (let x = 0; x < textureSize; x++) {
                const worldX = (x / textureSize) * this.config.size;
                const worldZ = (y / textureSize) * this.config.size;
                const influences = this.calculateRegionInfluences(worldX, worldZ, regions);
                const color = this.blendBiomeColors(influences);
                const i = (y * textureSize + x) * 4;
                textureData[i] = color.r;
                textureData[i + 1] = color.g;
                textureData[i + 2] = color.b;
                textureData[i + 3] = 255;
            }
        }
        // Simula compressão de textura (retorna array de texturas)
        return [{
                data: textureData,
                width: textureSize,
                height: textureSize,
                format: 'rgba8',
                compressed: false
            }];
    }
    calculateRegionInfluences(worldX, worldZ, regions) {
        const influences = new Map();
        for (const region of regions) {
            const dx = worldX - region.center.x;
            const dz = worldZ - region.center.z;
            const distance = Math.sqrt(dx * dx + dz * dz);
            if (distance <= region.radius) {
                const influence = 1 - (distance / region.radius);
                influences.set(region.type, influence);
            }
        }
        return influences;
    }
    blendBiomeColors(influences) {
        let r = 0, g = 0, b = 0;
        let totalInfluence = 0;
        const biomeColors = {
            urban: { r: 128, g: 128, b: 128 },
            military: { r: 96, g: 100, b: 86 },
            forest: { r: 34, g: 139, b: 34 },
            industrial: { r: 119, g: 112, b: 101 }
        };
        for (const [type, influence] of influences) {
            const color = biomeColors[type] || biomeColors.urban;
            r += color.r * influence;
            g += color.g * influence;
            b += color.b * influence;
            totalInfluence += influence;
        }
        if (totalInfluence > 0) {
            r /= totalInfluence;
            g /= totalInfluence;
            b /= totalInfluence;
        }
        return { r: Math.round(r), g: Math.round(g), b: Math.round(b) };
    }
    getHeight(x, z) {
        const size = this.config.size / this.config.resolution + 1;
        return this.heightData[z * size + x];
    }
    setHeight(x, z, height) {
        const size = this.config.size / this.config.resolution + 1;
        this.heightData[z * size + x] = height;
    }
}
exports.TerrainGenerator = TerrainGenerator;
//# sourceMappingURL=TerrainGenerator.js.map