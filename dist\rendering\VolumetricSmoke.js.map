{"version": 3, "file": "VolumetricSmoke.js", "sourceRoot": "", "sources": ["../../src/rendering/VolumetricSmoke.ts"], "names": [], "mappings": ";;;AAUA,MAAa,qBAAqB;IAQK;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,qBAAqB,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,qBAAqB,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEO,sBAAsB;QAC1B,iCAAiC;QACjC,+BAA+B;QAC/B,2BAA2B;QAC3B,wCAAwC;IAC5C,CAAC;IAEM,iBAAiB,CAAC,IAAY,EAAE,MAA6B;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAAY,cAAc,CAAC,MAA6B;QACrD,OAAO;YACH,GAAG,MAAM;YACT,UAAU,EAAE;gBACR,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,qBAAqB,CAAC,eAAe,CAAC;gBACvE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,qBAAqB,CAAC,eAAe,CAAC;gBACvE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,qBAAqB,CAAC,eAAe,CAAC;aAC1E;YACD,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACzD,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;SACpE,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,WAAmB;QAC7B,gEAAgE;QAChE,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,qBAAqB,CAAC,WAAW,EAAE,CAAC;YAC/E,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IACtC,CAAC;IAEO,mBAAmB;QACvB,wCAAwC;QACxC,uCAAuC;QACvC,mCAAmC;QACnC,kCAAkC;QAClC,8CAA8C;IAClD,CAAC;IAED,+CAA+C;IACxC,mBAAmB,CAAC,SAAkB,EAAE,SAAkB;QAC7D,iEAAiE;QACjE,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC,4CAA4C;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;QACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC;QAElC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;QAElC,wCAAwC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpD,YAAY,IAAI,OAAO,GAAG,QAAQ,CAAC;YAEnC,uCAAuC;YACvC,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;gBACtB,OAAO,GAAG,CAAC,CAAC,4BAA4B;YAC5C,CAAC;YAED,UAAU,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC;YACvC,UAAU,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC;YACvC,UAAU,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC;QAC3C,CAAC;QAED,iEAAiE;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC;IACzC,CAAC;IAEO,kBAAkB,CAAC,QAAiB;QACxC,sDAAsD;QACtD,gCAAgC;QAChC,uCAAuC;QACvC,iCAAiC;QACjC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,qCAAqC;IAC7B,SAAS,CAAC,CAAU;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO;YACH,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;SACf,CAAC;IACN,CAAC;IAEO,QAAQ,CAAC,EAAW,EAAE,EAAW;QACrC,OAAO;YACH,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACd,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACd,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SACjB,CAAC;IACN,CAAC;IAEO,QAAQ,CAAC,EAAW,EAAE,EAAW;QACrC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;;AA5HL,sDA6HC;AA5H2B,uCAAiB,GAAG,EAAE,CAAC,CAAC,0CAA0C;AAClE,qCAAe,GAAG,EAAE,CAAC,CAAG,0BAA0B;AAClD,iCAAW,GAAG,EAAE,CAAC,CAAO,0CAA0C"}