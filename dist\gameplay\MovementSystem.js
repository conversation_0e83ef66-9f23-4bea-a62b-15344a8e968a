"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MovementSystem = void 0;
class MovementSystem {
    constructor() {
        this.state = this.initializeState();
        this.config = this.initializeConfig();
    }
    initializeState() {
        return {
            position: { x: 0, y: 0, z: 0 },
            velocity: { x: 0, y: 0, z: 0 },
            acceleration: { x: 0, y: 0, z: 0 },
            grounded: false,
            crouching: false,
            sliding: false,
            running: false,
            lastJumpTime: 0,
            slideStartTime: 0,
            slideVelocity: { x: 0, y: 0, z: 0 }
        };
    }
    initializeConfig() {
        return {
            walkSpeed: 250, // Unidades por segundo
            runSpeed: 400, // Unidades por segundo
            crouchSpeed: 150, // Unidades por segundo
            jumpForce: 300, // Força do pulo
            airControl: 0.3, // Controle no ar (0-1)
            friction: 8, // Fricção no chão
            groundAcceleration: 10, // Aceleração no chão
            airAcceleration: 2, // Aceleração no ar
            gravity: 800, // Gravidade
            slideMinSpeed: 200, // Velocidade mínima para slide
            slideMaxDuration: 0.7, // Duração máxima do slide em segundos
            slideCooldown: 1.0 // Tempo de espera entre slides
        };
    }
    update(input, deltaTime, currentTime) {
        // Atualiza estado de movimento
        this.updateMovementState(input, deltaTime, currentTime);
        // Aplica física
        this.applyPhysics(deltaTime);
        // Atualiza posição
        this.updatePosition(deltaTime);
        // Resolve colisões
        this.resolveCollisions();
    }
    updateMovementState(input, deltaTime, currentTime) {
        // Atualiza estados baseado no input
        this.updateRunningState(input);
        this.updateCrouchingState(input);
        this.updateSlidingState(input, currentTime);
        this.handleJump(input, currentTime);
    }
    updateRunningState(input) {
        this.state.running = input.run && !this.state.crouching && this.state.grounded;
    }
    updateCrouchingState(input) {
        if (input.crouch) {
            this.state.crouching = true;
            // Verifica se pode iniciar slide
            if (this.canStartSlide()) {
                this.startSlide();
            }
        }
        else if (this.canStopCrouching()) {
            this.state.crouching = false;
        }
    }
    updateSlidingState(input, currentTime) {
        if (this.state.sliding) {
            const slideDuration = currentTime - this.state.slideStartTime;
            if (slideDuration >= this.config.slideMaxDuration) {
                this.stopSlide();
            }
        }
    }
    handleJump(input, currentTime) {
        if (input.jump && this.canJump(currentTime)) {
            this.jump();
            this.state.lastJumpTime = currentTime;
        }
    }
    canJump(currentTime) {
        return this.state.grounded &&
            (currentTime - this.state.lastJumpTime) > 0.1;
    }
    jump() {
        this.state.velocity.y = this.config.jumpForce;
        this.state.grounded = false;
    }
    canStartSlide() {
        const speed = this.getHorizontalSpeed();
        return this.state.grounded &&
            this.state.running &&
            speed >= this.config.slideMinSpeed;
    }
    startSlide() {
        this.state.sliding = true;
        this.state.slideStartTime = performance.now();
        this.state.slideVelocity = { ...this.state.velocity };
    }
    stopSlide() {
        this.state.sliding = false;
    }
    canStopCrouching() {
        // Verifica se há espaço para levantar
        return true; // TODO: Implementar verificação de colisão
    }
    applyPhysics(deltaTime) {
        // Aplica gravidade
        if (!this.state.grounded) {
            this.state.velocity.y -= this.config.gravity * deltaTime;
        }
        // Aplica fricção no chão
        if (this.state.grounded) {
            this.applyFriction(deltaTime);
        }
        // Aplica aceleração baseada no input
        this.applyAcceleration(deltaTime);
    }
    applyFriction(deltaTime) {
        const speed = this.getHorizontalSpeed();
        if (speed > 0) {
            const friction = this.config.friction * deltaTime;
            const scale = Math.max(0, speed - friction) / speed;
            this.state.velocity.x *= scale;
            this.state.velocity.z *= scale;
        }
    }
    applyAcceleration(deltaTime) {
        // TODO: Aplicar aceleração baseada no input e estado
    }
    updatePosition(deltaTime) {
        this.state.position.x += this.state.velocity.x * deltaTime;
        this.state.position.y += this.state.velocity.y * deltaTime;
        this.state.position.z += this.state.velocity.z * deltaTime;
    }
    resolveCollisions() {
        // TODO: Implementar sistema de colisão
    }
    getHorizontalSpeed() {
        return Math.sqrt(this.state.velocity.x * this.state.velocity.x +
            this.state.velocity.z * this.state.velocity.z);
    }
    getState() {
        return { ...this.state };
    }
    teleport(position) {
        this.state.position = { ...position };
        this.state.velocity = { x: 0, y: 0, z: 0 };
    }
}
exports.MovementSystem = MovementSystem;
MovementSystem.MAX_SLOPE_ANGLE = 45;
MovementSystem.SLIDE_RECOVERY_TIME = 0.5;
//# sourceMappingURL=MovementSystem.js.map