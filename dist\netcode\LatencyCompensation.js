"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LatencyCompensation = void 0;
// Configurações de compensação de latência
const MAX_LATENCY_COMPENSATION = 1000; // máximo de 1 segundo de compensação
const BUFFER_SIZE = 1024; // tamanho do buffer circular para histórico de estados
class LatencyCompensation {
    constructor() {
        this.stateHistory = new Map();
        this.inputBuffer = new Map();
        this.initializeBuffers();
    }
    initializeBuffers() {
        // Inicializa buffers circulares para cada jogador
    }
    addPlayerState(playerId, state, timestamp) {
        if (!this.stateHistory.has(playerId)) {
            this.stateHistory.set(playerId, []);
        }
        const history = this.stateHistory.get(playerId);
        // Mantém o histórico ordenado por timestamp
        const timestampedState = { ...state, timestamp };
        history.push(timestampedState);
        // Remove estados antigos
        while (history.length > 0 &&
            timestamp - history[0].timestamp > MAX_LATENCY_COMPENSATION) {
            history.shift();
        }
    }
    rewindTime(timestamp) {
        const rewindStates = new Map();
        this.stateHistory.forEach((history, playerId) => {
            // Encontra o estado mais próximo do timestamp desejado
            const state = this.findNearestState(history, timestamp);
            if (state) {
                const { timestamp: _, ...playerState } = state;
                rewindStates.set(playerId, playerState);
            }
        });
        return rewindStates;
    }
    findNearestState(history, timestamp) {
        // Busca binária para encontrar o estado mais próximo
        let left = 0;
        let right = history.length - 1;
        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            const state = history[mid];
            if (state.timestamp === timestamp) {
                return state;
            }
            if (state.timestamp < timestamp) {
                left = mid + 1;
            }
            else {
                right = mid - 1;
            }
        }
        // Retorna o estado mais próximo
        if (right >= 0 && left < history.length) {
            const leftDiff = Math.abs(history[right].timestamp - timestamp);
            const rightDiff = Math.abs(history[left].timestamp - timestamp);
            return leftDiff < rightDiff ? history[right] : history[left];
        }
        return history.length > 0 ? history[history.length - 1] : null;
    }
    predictPlayerState(currentState, input, deltaTime) {
        // Predição de estado baseada em física
        const predictedState = { ...currentState };
        // Aplica movimento
        const moveSpeed = 5; // unidades por segundo
        predictedState.position.x += input.movement.x * moveSpeed * deltaTime;
        predictedState.position.y += input.movement.y * moveSpeed * deltaTime;
        // Aplica gravidade se estiver no ar
        if (!this.isGrounded(predictedState.position)) {
            predictedState.velocity.y -= 9.81 * deltaTime; // Gravidade
            predictedState.position.y += predictedState.velocity.y * deltaTime;
        }
        return predictedState;
    }
    isGrounded(position) {
        // TODO: Implementar verificação de colisão com o chão
        return position.y <= 0;
    }
    reconcileState(predictedState, serverState) {
        const threshold = 0.1; // Limiar para correção suave
        const reconciled = { ...serverState };
        // Se a diferença for pequena, faz uma correção suave
        if (this.getStateDifference(predictedState, serverState) < threshold) {
            reconciled.position = this.lerp(predictedState.position, serverState.position, 0.3);
            reconciled.velocity = this.lerp(predictedState.velocity, serverState.velocity, 0.3);
        }
        return reconciled;
    }
    getStateDifference(state1, state2) {
        // Calcula a diferença entre dois estados (simplificado)
        const posDiff = this.getVectorDistance(state1.position, state2.position);
        const velDiff = this.getVectorDistance(state1.velocity, state2.velocity);
        return posDiff + velDiff * 0.5;
    }
    getVectorDistance(v1, v2) {
        const dx = v2.x - v1.x;
        const dy = v2.y - v1.y;
        const dz = v2.z - v1.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    lerp(v1, v2, t) {
        return {
            x: v1.x + (v2.x - v1.x) * t,
            y: v1.y + (v2.y - v1.y) * t,
            z: v1.z + (v2.z - v1.z) * t
        };
    }
}
exports.LatencyCompensation = LatencyCompensation;
//# sourceMappingURL=LatencyCompensation.js.map