"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
self.onmessage = (e) => {
    const { type, bots, deltaTime } = e.data;
    switch (type) {
        case 'update':
            updateBots(bots, deltaTime);
            break;
        case 'pathfinding':
            // TODO: Implementar cálculos de pathfinding em batch
            break;
        case 'perception':
            // TODO: Implementar atualizações de percepção em batch
            break;
    }
};
function updateBots(bots, deltaTime) {
    const updates = bots.map(([id, bot]) => {
        bot.update(deltaTime, calculateLODLevel(bot));
        return {
            id,
            position: bot.position,
            state: bot.state
        };
    });
    self.postMessage({
        type: 'botUpdates',
        updates
    });
}
function calculateLODLevel(bot) {
    // Calcula LOD baseado na distância do jogador mais próximo
    // Por enquanto, retorna um valor fixo para teste
    return 0;
}
//# sourceMappingURL=AIWorker.js.map