"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SafeZoneManager = void 0;
class SafeZoneManager {
    constructor(mapManager) {
        this.mapManager = mapManager;
        this.phaseTimers = new Map();
        // Inicializa o estado da zona segura
        this.state = {
            currentCenter: { x: 0, y: 0, z: 0 },
            currentRadius: SafeZoneManager.INITIAL_RADIUS,
            nextCenter: { x: 0, y: 0, z: 0 },
            nextRadius: SafeZoneManager.INITIAL_RADIUS,
            phase: 0,
            timeToShrink: SafeZoneManager.PAUSE_TIME,
            damagePerSecond: 1
        };
        this.initializeFirstCircle();
    }
    initializeFirstCircle() {
        // Escolhe um centro inicial baseado nos POIs
        const nextPhaseCenter = this.calculateNextPhaseCenter();
        const nextPhaseRadius = this.calculateNextPhaseRadius(1);
        this.state.nextCenter = nextPhaseCenter;
        this.state.nextRadius = nextPhaseRadius;
    }
    update(deltaTime) {
        // Atualiza o timer da fase atual
        this.state.timeToShrink -= deltaTime;
        if (this.state.timeToShrink <= 0) {
            if (this.state.phase < SafeZoneManager.PHASES) {
                this.startNextPhase();
            }
        }
        else if (this.state.phase > 0) {
            // Interpola o círculo durante o encolhimento
            const progress = 1 - (this.state.timeToShrink / SafeZoneManager.SHRINK_TIME);
            if (progress < 1) {
                this.interpolateCircle(progress);
            }
        }
    }
    startNextPhase() {
        this.state.phase++;
        this.state.currentCenter = { ...this.state.nextCenter };
        this.state.currentRadius = this.state.nextRadius;
        if (this.state.phase < SafeZoneManager.PHASES) {
            const nextCenter = this.calculateNextPhaseCenter();
            const nextRadius = this.calculateNextPhaseRadius(this.state.phase + 1);
            this.state.nextCenter = nextCenter;
            this.state.nextRadius = nextRadius;
            this.state.timeToShrink = SafeZoneManager.SHRINK_TIME;
            this.state.damagePerSecond = Math.pow(2, this.state.phase); // Dano aumenta exponencialmente
        }
    }
    interpolateCircle(progress) {
        // Interpola suavemente entre o círculo atual e o próximo
        const smoothProgress = this.smoothStep(progress);
        this.state.currentCenter = {
            x: this.lerp(this.state.currentCenter.x, this.state.nextCenter.x, smoothProgress),
            y: this.state.currentCenter.y,
            z: this.lerp(this.state.currentCenter.z, this.state.nextCenter.z, smoothProgress)
        };
        this.state.currentRadius = this.lerp(this.state.currentRadius, this.state.nextRadius, smoothProgress);
    }
    calculateNextPhaseRadius(phase) {
        // Calcula o raio da próxima fase com uma redução não-linear
        const progress = phase / SafeZoneManager.PHASES;
        return this.lerp(SafeZoneManager.INITIAL_RADIUS, SafeZoneManager.MIN_RADIUS, Math.pow(progress, 1.5) // Redução mais rápida no início, mais lenta no final
        );
    }
    calculateNextPhaseCenter() {
        // Implementa um algoritmo que escolhe o próximo centro baseado em:
        // 1. Densidade de jogadores
        // 2. Posição dos POIs ativos
        // 3. Terrain validity (não pode terminar na água ou fora do mapa)
        // Por enquanto, usando uma versão simplificada que escolhe um ponto aleatório
        // dentro do círculo atual
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * (this.state.currentRadius * 0.5);
        return {
            x: this.state.currentCenter.x + Math.cos(angle) * distance,
            y: 0,
            z: this.state.currentCenter.z + Math.sin(angle) * distance
        };
    }
    isPositionInSafeZone(position) {
        return this.calculateDistance(position, this.state.currentCenter) <= this.state.currentRadius;
    }
    getDamageAtPosition(position) {
        if (this.isPositionInSafeZone(position)) {
            return 0;
        }
        return this.state.damagePerSecond;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dz * dz);
    }
    lerp(start, end, progress) {
        return start + (end - start) * progress;
    }
    smoothStep(x) {
        // Função de suavização para transições mais naturais
        return x * x * (3 - 2 * x);
    }
    getState() {
        return { ...this.state };
    }
}
exports.SafeZoneManager = SafeZoneManager;
SafeZoneManager.INITIAL_RADIUS = 1000; // 1km de raio inicial
SafeZoneManager.MIN_RADIUS = 50; // 50m de raio final
SafeZoneManager.PHASES = 5; // Número de fases de encolhimento
SafeZoneManager.SHRINK_TIME = 120; // 2 minutos por fase
SafeZoneManager.PAUSE_TIME = 90; // 1.5 minutos entre fases
//# sourceMappingURL=SafeZoneManager.js.map