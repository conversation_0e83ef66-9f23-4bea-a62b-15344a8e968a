{"version": 3, "file": "SpectatorManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/SpectatorManager.ts"], "names": [], "mappings": ";;;AAiBA,MAAa,gBAAgB;IAYzB,YAAY,aAA4B;QACpC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,gCAAgC;QAChC,MAAM,iBAAiB,GAAG;YACtB,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;YACtF,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3F,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;SACjG,CAAC;QAEF,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE;gBACrB,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,GAAG,EAAE,EAAE;gBACP,aAAa,EAAE,QAAQ;aAC1B,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,eAAe,CAAC,WAAmB,EAAE,MAAgC;QACxE,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,gBAAgB,CAAC,cAAc,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,aAAa,GAAoB;YACnC,KAAK,EAAE,gBAAgB,CAAC,mBAAmB;YAC3C,mBAAmB,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,CAAC;YAC9D,cAAc,EAAE,KAAK;YACrB,YAAY,EAAE,CAAC,KAAK,CAAC;SACxB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE;YACnC,GAAG,aAAa;YAChB,GAAG,MAAM;SACZ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,WAAmB;QACrC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAEM,iBAAiB,CAAC,WAAmB,EAAE,QAAgB,EAAE,WAAsD;QAClH,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,QAAgB,EAAE,MAA6B;QAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI;YAChD,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,GAAG,EAAE,EAAE;YACP,aAAa,EAAE,QAAiB;SACnC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;YACvB,GAAG,aAAa;YAChB,GAAG,MAAM;SACZ,CAAC,CAAC;IACP,CAAC;IAEM,eAAe,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAEM,cAAc,CAAC,KAAkB;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEjC,+CAA+C;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACrB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;aACxC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CACnC,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,SAAS;YAC3D,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACjC,CAAC;IACL,CAAC;IAEM,eAAe,CAAC,WAAmB;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC;IAEM,eAAe,CAAC,EAAU,EAAE,MAAoB;QACnD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAEM,kBAAkB,CAAC,EAAU;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAEM,iBAAiB,CAAC,IAAkB,EAAE,EAAgB,EAAE,QAAgB;QAC3E,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAElE,OAAO;YACH,QAAQ,EAAE;gBACN,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;gBACjD,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;gBACjD,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;aACpD;YACD,QAAQ,EAAE;gBACN,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;gBACjD,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;gBACjD,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;aACpD;YACD,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC;YACrC,aAAa,EAAE,EAAE,CAAC,aAAa;SAClC,CAAC;IACN,CAAC;IAEM,WAAW,CAAC,MAAe;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAEM,mBAAmB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,qBAAqB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAEM,YAAY,CAAC,WAAmB;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;;AAlKL,4CAmKC;AAlK2B,oCAAmB,GAAG,EAAE,CAAC,CAAC,qBAAqB;AAC/C,qCAAoB,GAAG,IAAI,CAAC;AAC5B,+BAAc,GAAG,IAAI,CAAC"}