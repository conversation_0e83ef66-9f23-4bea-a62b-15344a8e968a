{"version": 3, "file": "ReplayWorker.js", "sourceRoot": "", "sources": ["../../src/gameplay/ReplayWorker.ts"], "names": [], "mappings": ";;AACA,+BAAwC;AAExC,MAAM,YAAY;IAAlB;QACY,WAAM,GAAkB,EAAE,CAAC;QAC3B,kBAAa,GAAsB,IAAI,CAAC;QACxC,aAAQ,GAA0B,IAAI,CAAC;IAoKnD,CAAC;IAlKU,aAAa,CAAC,KAAmB;QACpC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QAElC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO;gBACR,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACxB,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM;QACd,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,QAAwB;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEO,cAAc,CAAC,MAAqB;QACxC,IAAI,CAAC;YACD,2DAA2D;YAC3D,MAAM,SAAS,GAAG,GAAG,CAAC;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAChD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC7C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEzB,oBAAoB;gBACpB,IAAI,CAAC,WAAW,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;iBACtC,CAAC,CAAC;YACP,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,IAA8E;QAC/F,IAAI,CAAC;YACD,0CAA0C;YAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,gCAAgC;YAChC,MAAM,UAAU,GAAG;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;aACtB,CAAC;YAEF,iBAAiB;YACjB,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEvD,0BAA0B;YAC1B,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,UAAU;gBAChB,UAAU,EAAE,UAAU;aACzB,CAAC,CAAC;YAEH,eAAe;YACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,UAAuB;QACtC,IAAI,CAAC;YACD,oBAAoB;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAE3D,gCAAgC;YAChC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;YACtC,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;YAElC,iCAAiC;YACjC,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;aAC1B,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,IAAY;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,uBAAuB;aACjC,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,mDAAmD;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,WAAW,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK;aACR,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,MAAqB;QACtC,oCAAoC;QACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,KAAkB;QACpC,0CAA0C;QAC1C,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,0CAA0C;YAC1C,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;YAC5D,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;YAC5D,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAChE,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,CAAC,IAAS;QAChC,kBAAkB;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAExC,8BAA8B;QAC9B,OAAO,IAAA,cAAO,EAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;IAEO,oBAAoB,CAAC,IAAiB;QAC1C,oBAAoB;QACpB,MAAM,YAAY,GAAG,IAAA,cAAO,EAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAErE,cAAc;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;IAClE,CAAC;CACJ;AAED,oBAAoB;AACpB,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;AAClC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC"}