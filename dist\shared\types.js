"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PacketType = void 0;
// Tipos de pacotes que serão trocados entre cliente e servidor
var PacketType;
(function (PacketType) {
    PacketType["PLAYER_INPUT"] = "PLAYER_INPUT";
    PacketType["GAME_STATE"] = "GAME_STATE";
    PacketType["PLAYER_ACTION"] = "PLAYER_ACTION";
    PacketType["HIT_REGISTRATION"] = "HIT_REGISTRATION";
})(PacketType || (exports.PacketType = PacketType = {}));
//# sourceMappingURL=types.js.map