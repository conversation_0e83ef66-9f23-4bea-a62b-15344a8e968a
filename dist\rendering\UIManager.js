"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIManager = void 0;
class UIManager {
    constructor(config) {
        this.config = config;
        this.elements = new Map();
        this.elementCache = new Map();
        this.audioManager = new AudioManager();
        this.dirtyElements = new Set();
        this.worker = this.initializeWorker();
        this.initializeUI();
    }
    initializeWorker() {
        try {
            const worker = new Worker('./UIWorker.ts');
            worker.onmessage = this.handleWorkerMessage.bind(this);
            return worker;
        }
        catch (error) {
            console.error('Failed to initialize UI worker:', error);
            return null;
        }
    }
    handleWorkerMessage(event) {
        const { type, data } = event.data;
        switch (type) {
            case 'elementUpdate':
                this.updateElementFromWorker(data);
                break;
            case 'cacheInvalidation':
                this.invalidateCache(data.elementIds);
                break;
        }
    }
    initializeUI() {
        // Inicializa elementos estáticos do HUD
        this.initializeHUDElements();
        // Inicializa elementos do minimapa
        this.initializeMinimapElements();
        // Inicializa elementos de feedback
        this.initializeFeedbackElements();
        // Pré-renderiza elementos estáticos
        this.bakeStaticElements();
    }
    initializeHUDElements() {
        // Health Bar
        this.addElement({
            id: 'healthBar',
            type: 'dynamic',
            visible: true,
            position: this.config.elementPositions['healthBar'] || { x: 50, y: 550 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
        // Ammo Counter
        this.addElement({
            id: 'ammoCounter',
            type: 'dynamic',
            visible: true,
            position: this.config.elementPositions['ammoCounter'] || { x: 550, y: 550 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
        // Money Display
        this.addElement({
            id: 'moneyDisplay',
            type: 'dynamic',
            visible: true,
            position: this.config.elementPositions['moneyDisplay'] || { x: 50, y: 50 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
    }
    initializeMinimapElements() {
        this.addElement({
            id: 'minimap',
            type: 'dynamic',
            visible: true,
            position: this.config.elementPositions['minimap'] || { x: 800, y: 50 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
    }
    initializeFeedbackElements() {
        // Hitmarker
        this.addElement({
            id: 'hitmarker',
            type: 'dynamic',
            visible: false,
            position: { x: 400, y: 300 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
        // Damage Indicator
        this.addElement({
            id: 'damageIndicator',
            type: 'dynamic',
            visible: false,
            position: { x: 400, y: 300 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        });
    }
    bakeStaticElements() {
        UIManager.BAKED_TEXTURES.forEach(elementId => {
            const element = this.elements.get(elementId);
            if (element && this.worker) {
                this.worker.postMessage({
                    type: 'bakeElement',
                    element
                });
            }
        });
    }
    updateElement(elementId, data) {
        const element = this.elements.get(elementId);
        if (!element)
            return;
        element.lastUpdateTime = Date.now();
        this.dirtyElements.add(elementId);
        if (this.worker) {
            this.worker.postMessage({
                type: 'updateElement',
                elementId,
                data
            });
        }
    }
    showHitmarker(position, damage) {
        this.updateElement('hitmarker', { position, damage });
        this.audioManager.playSound('hitmarker');
    }
    showDamageIndicator(direction) {
        this.updateElement('damageIndicator', { direction });
        this.audioManager.playSound('damage');
    }
    updateMinimap(playerPosition, enemies, safeZone) {
        this.updateElement('minimap', {
            playerPosition,
            enemies,
            safeZone
        });
    }
    showNotification(type, message) {
        const notification = {
            id: `notification_${Date.now()}`,
            type: 'dynamic',
            visible: true,
            position: this.config.elementPositions['notifications'] || { x: 400, y: 100 },
            scale: this.config.scale,
            opacity: this.config.opacity,
            lastUpdateTime: Date.now()
        };
        this.addElement(notification);
        this.audioManager.playSound(type);
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.invalidateAllCaches();
        this.elements.forEach((element, id) => {
            this.dirtyElements.add(id);
        });
    }
    addElement(element) {
        this.elements.set(element.id, element);
        this.dirtyElements.add(element.id);
    }
    updateElementFromWorker(data) {
        const { elementId, cache } = data;
        this.elementCache.set(elementId, cache);
        this.dirtyElements.delete(elementId);
    }
    invalidateCache(elementIds) {
        elementIds.forEach(id => {
            this.elementCache.delete(id);
            this.dirtyElements.add(id);
        });
    }
    invalidateAllCaches() {
        this.elementCache.clear();
    }
}
exports.UIManager = UIManager;
UIManager.UPDATE_INTERVAL = 16; // ~60fps para UI
UIManager.CACHED_ELEMENTS_LIMIT = 1000;
UIManager.BAKED_TEXTURES = new Set(['healthBar', 'ammoCounter', 'minimapBorder']);
class AudioManager {
    constructor() {
        this.audioContext = new AudioContext();
        this.audioBuffers = new Map();
        this.volumes = new Map();
        this.initializeAudio();
    }
    async initializeAudio() {
        for (const [key, file] of Object.entries(AudioManager.SOUNDS)) {
            try {
                const response = await fetch(`/audio/${file}`);
                const arrayBuffer = await response.arrayBuffer();
                const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                this.audioBuffers.set(key, audioBuffer);
                this.volumes.set(key, 1.0);
            }
            catch (error) {
                console.error(`Failed to load audio: ${file}`, error);
            }
        }
    }
    playSound(type) {
        const buffer = this.audioBuffers.get(type);
        const volume = this.volumes.get(type) || 1.0;
        if (buffer) {
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            source.buffer = buffer;
            gainNode.gain.value = volume;
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            source.start(0);
        }
    }
    setVolume(type, volume) {
        this.volumes.set(type, Math.max(0, Math.min(1, volume)));
    }
}
AudioManager.SOUNDS = {
    hitmarker: 'hitmarker.wav',
    damage: 'damage.wav',
    elimination: 'elimination.wav',
    contract: 'contract.wav',
    warning: 'warning.wav'
};
//# sourceMappingURL=UIManager.js.map