"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MainMenuSystem = void 0;
const events_1 = require("events");
class MainMenuSystem extends events_1.EventEmitter {
    constructor() {
        super();
        this.gameModes = [];
        this.soundEffects = new Map();
        this.menuState = {
            currentMenu: 'main',
            isLoading: false,
            notifications: []
        };
        this.initializeGameModes();
        this.initializeAudio();
        this.createMenuHTML();
        console.log('🎮 MainMenuSystem inicializado');
    }
    initializeGameModes() {
        this.gameModes = [
            {
                id: 'team_deathmatch',
                name: 'Team Deathmatch',
                description: 'Combate em equipe clássico',
                playerCount: '8v8',
                estimatedTime: '10-15 min',
                difficulty: 'casual',
                available: true,
                featured: true,
                icon: '⚔️'
            },
            {
                id: 'battle_royale',
                name: 'Battle Royale',
                description: 'Último jogador em pé vence',
                playerCount: '100 jogadores',
                estimatedTime: '20-30 min',
                difficulty: 'competitive',
                available: true,
                featured: true,
                icon: '👑'
            },
            {
                id: 'capture_flag',
                name: 'Capture the Flag',
                description: 'Capture a bandeira inimiga',
                playerCount: '6v6',
                estimatedTime: '15-20 min',
                difficulty: 'competitive',
                available: true,
                featured: false,
                icon: '🏁'
            },
            {
                id: 'domination',
                name: 'Domination',
                description: 'Controle pontos estratégicos',
                playerCount: '8v8',
                estimatedTime: '12-18 min',
                difficulty: 'casual',
                available: true,
                featured: false,
                icon: '🎯'
            },
            {
                id: 'search_destroy',
                name: 'Search & Destroy',
                description: 'Elimine todos ou plante a bomba',
                playerCount: '5v5',
                estimatedTime: '20-25 min',
                difficulty: 'hardcore',
                available: true,
                featured: false,
                icon: '💣'
            }
        ];
    }
    initializeAudio() {
        // Simula inicialização de áudio
        console.log('🎵 Inicializando sistema de áudio do menu');
        // Em implementação real, carregaria arquivos de áudio
        const audioFiles = [
            'menu_music.mp3',
            'button_hover.wav',
            'button_click.wav',
            'notification.wav',
            'achievement.wav'
        ];
        for (const file of audioFiles) {
            console.log(`🎵 Carregando áudio: ${file}`);
        }
    }
    createMenuHTML() {
        // Cria estrutura HTML do menu principal
        const menuHTML = `
            <div id="tactical-nexus-menu" class="main-menu">
                <div class="background-video">
                    <div class="background-overlay"></div>
                </div>
                
                <header class="menu-header">
                    <div class="logo">
                        <h1>TACTICAL NEXUS</h1>
                        <span class="version">v1.0.0</span>
                    </div>
                    
                    <div class="player-info" id="player-info">
                        <div class="player-avatar"></div>
                        <div class="player-details">
                            <span class="username">Carregando...</span>
                            <span class="level">Nível --</span>
                        </div>
                        <div class="currency">
                            <span class="coins">💰 --</span>
                            <span class="premium">💎 --</span>
                        </div>
                    </div>
                </header>

                <nav class="main-navigation">
                    <button class="nav-btn active" data-menu="main">INÍCIO</button>
                    <button class="nav-btn" data-menu="play">JOGAR</button>
                    <button class="nav-btn" data-menu="loadout">EQUIPAMENTOS</button>
                    <button class="nav-btn" data-menu="store">LOJA</button>
                    <button class="nav-btn" data-menu="profile">PERFIL</button>
                    <button class="nav-btn" data-menu="settings">CONFIGURAÇÕES</button>
                </nav>

                <main class="menu-content">
                    <div id="main-content" class="content-panel active">
                        <div class="welcome-section">
                            <h2>Bem-vindo ao Tactical Nexus</h2>
                            <p>O futuro dos jogos táticos está aqui!</p>
                            
                            <div class="quick-actions">
                                <button class="btn-primary btn-large" id="quick-play">
                                    🎮 JOGO RÁPIDO
                                </button>
                                <button class="btn-secondary" id="training">
                                    🎯 TREINAMENTO
                                </button>
                            </div>
                        </div>

                        <div class="featured-modes">
                            <h3>Modos em Destaque</h3>
                            <div class="mode-cards" id="featured-modes">
                                <!-- Modos serão inseridos dinamicamente -->
                            </div>
                        </div>

                        <div class="news-section">
                            <h3>Novidades</h3>
                            <div class="news-items">
                                <div class="news-item">
                                    <h4>🎉 Tactical Nexus Lançado!</h4>
                                    <p>Bem-vindo ao futuro dos jogos táticos com 999 FPS!</p>
                                </div>
                                <div class="news-item">
                                    <h4>🗺️ Novo Mapa: Urban District</h4>
                                    <p>Explore o novo ambiente urbano com combate tático intenso.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="play-content" class="content-panel">
                        <h2>Selecionar Modo de Jogo</h2>
                        <div class="game-modes" id="game-modes">
                            <!-- Modos de jogo serão inseridos dinamicamente -->
                        </div>
                    </div>

                    <div id="loadout-content" class="content-panel">
                        <h2>Equipamentos</h2>
                        <div class="loadout-editor">
                            <div class="weapon-slots">
                                <div class="weapon-slot primary">
                                    <label>Arma Primária</label>
                                    <div class="weapon-preview">M4A1 Assault Rifle</div>
                                </div>
                                <div class="weapon-slot secondary">
                                    <label>Arma Secundária</label>
                                    <div class="weapon-preview">Glock Pistol</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="store-content" class="content-panel">
                        <h2>Loja</h2>
                        <div class="store-categories">
                            <button class="category-btn active" data-category="weapons">Armas</button>
                            <button class="category-btn" data-category="skins">Skins</button>
                            <button class="category-btn" data-category="boosts">Boosts</button>
                        </div>
                        <div class="store-items" id="store-items">
                            <!-- Itens da loja serão inseridos dinamicamente -->
                        </div>
                    </div>

                    <div id="profile-content" class="content-panel">
                        <h2>Perfil do Jogador</h2>
                        <div class="profile-stats">
                            <div class="stat-card">
                                <h3>Estatísticas</h3>
                                <div class="stats-grid">
                                    <div class="stat">
                                        <span class="label">Eliminações</span>
                                        <span class="value" id="kills-stat">0</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">Mortes</span>
                                        <span class="value" id="deaths-stat">0</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">K/D Ratio</span>
                                        <span class="value" id="kdr-stat">0.00</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">Vitórias</span>
                                        <span class="value" id="wins-stat">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="settings-content" class="content-panel">
                        <h2>Configurações</h2>
                        <div class="settings-sections">
                            <div class="settings-section">
                                <h3>Gráficos</h3>
                                <div class="setting-item">
                                    <label>Qualidade Gráfica</label>
                                    <select id="graphics-quality">
                                        <option value="low">Baixa</option>
                                        <option value="medium">Média</option>
                                        <option value="high">Alta</option>
                                        <option value="ultra" selected>Ultra</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Resolução</label>
                                    <select id="resolution">
                                        <option value="1920x1080">1920x1080</option>
                                        <option value="2560x1440" selected>2560x1440</option>
                                        <option value="3840x2160">3840x2160</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>FPS Limite</label>
                                    <select id="fps-limit">
                                        <option value="60">60 FPS</option>
                                        <option value="120">120 FPS</option>
                                        <option value="240">240 FPS</option>
                                        <option value="999" selected>999 FPS</option>
                                    </select>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Áudio</h3>
                                <div class="setting-item">
                                    <label>Volume Geral</label>
                                    <input type="range" id="master-volume" min="0" max="100" value="80">
                                </div>
                                <div class="setting-item">
                                    <label>Volume da Música</label>
                                    <input type="range" id="music-volume" min="0" max="100" value="60">
                                </div>
                                <div class="setting-item">
                                    <label>Volume dos Efeitos</label>
                                    <input type="range" id="sfx-volume" min="0" max="100" value="90">
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <div class="notifications" id="notifications">
                    <!-- Notificações serão inseridas dinamicamente -->
                </div>

                <footer class="menu-footer">
                    <div class="server-status">
                        <span class="status-indicator online"></span>
                        <span>Servidores Online</span>
                    </div>
                    <div class="player-count">
                        <span>👥 Jogadores Online: <span id="online-count">1,337</span></span>
                    </div>
                </footer>
            </div>
        `;
        // Em implementação real, isso seria inserido no DOM
        console.log('🎨 Interface do menu principal criada');
        this.setupEventListeners();
    }
    setupEventListeners() {
        // Simula configuração de event listeners
        console.log('🔗 Configurando event listeners do menu');
        // Navegação entre menus
        this.on('navigateToMenu', (menuName) => {
            this.navigateToMenu(menuName);
        });
        // Ações de jogo
        this.on('quickPlay', () => {
            this.startQuickPlay();
        });
        this.on('selectGameMode', (modeId) => {
            this.selectGameMode(modeId);
        });
    }
    navigateToMenu(menuName) {
        const previousMenu = this.menuState.currentMenu;
        this.menuState.previousMenu = previousMenu;
        this.menuState.currentMenu = menuName;
        console.log(`🧭 Navegando de ${previousMenu} para ${menuName}`);
        // Simula transição de menu
        this.playSound('button_click');
        this.emit('menuChanged', {
            from: previousMenu,
            to: menuName,
            state: this.menuState
        });
    }
    updatePlayerInfo(playerInfo) {
        this.menuState.playerInfo = playerInfo;
        // Simula atualização da interface
        console.log(`👤 Informações do jogador atualizadas: ${playerInfo?.username} (Nível ${playerInfo?.level})`);
        this.emit('playerInfoUpdated', { playerInfo });
    }
    addNotification(notification) {
        const fullNotification = {
            ...notification,
            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            read: false
        };
        this.menuState.notifications.unshift(fullNotification);
        // Limita a 10 notificações
        if (this.menuState.notifications.length > 10) {
            this.menuState.notifications = this.menuState.notifications.slice(0, 10);
        }
        console.log(`📢 Nova notificação: ${notification.title}`);
        this.playSound('notification');
        this.emit('notificationAdded', { notification: fullNotification });
    }
    startQuickPlay() {
        this.setLoading(true);
        console.log('🎮 Iniciando jogo rápido...');
        // Simula busca por partida
        setTimeout(() => {
            this.setLoading(false);
            this.emit('quickPlayStarted');
        }, 2000);
    }
    selectGameMode(modeId) {
        const gameMode = this.gameModes.find(mode => mode.id === modeId);
        if (!gameMode) {
            console.log(`❌ Modo de jogo não encontrado: ${modeId}`);
            return;
        }
        if (!gameMode.available) {
            this.addNotification({
                type: 'warning',
                title: 'Modo Indisponível',
                message: `${gameMode.name} não está disponível no momento.`
            });
            return;
        }
        console.log(`🎮 Modo selecionado: ${gameMode.name}`);
        this.playSound('button_click');
        this.emit('gameModeSelected', { gameMode });
    }
    setLoading(loading) {
        this.menuState.isLoading = loading;
        console.log(`⏳ Estado de carregamento: ${loading ? 'ativo' : 'inativo'}`);
        this.emit('loadingStateChanged', { loading });
    }
    playSound(soundName) {
        // Simula reprodução de som
        console.log(`🔊 Reproduzindo som: ${soundName}`);
    }
    getMenuState() {
        return { ...this.menuState };
    }
    getGameModes() {
        return [...this.gameModes];
    }
    getFeaturedModes() {
        return this.gameModes.filter(mode => mode.featured && mode.available);
    }
    markNotificationAsRead(notificationId) {
        const notification = this.menuState.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.emit('notificationRead', { notificationId });
        }
    }
    clearAllNotifications() {
        this.menuState.notifications = [];
        this.emit('notificationsCleared');
    }
    getUnreadNotificationCount() {
        return this.menuState.notifications.filter(n => !n.read).length;
    }
}
exports.MainMenuSystem = MainMenuSystem;
exports.default = MainMenuSystem;
//# sourceMappingURL=MainMenuSystem.js.map