{"version": 3, "file": "LootSystem.js", "sourceRoot": "", "sources": ["../../src/gameplay/LootSystem.ts"], "names": [], "mappings": ";;;AAmBA,MAAa,UAAU;IAOnB;QAHQ,cAAS,GAA0B,IAAI,GAAG,EAAE,CAAC;QAC7C,cAAS,GAA0B,IAAI,GAAG,EAAE,CAAC;QAGjD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB;QACvB,wCAAwC;QACxC,MAAM,SAAS,GAAa;YACxB,EAAE,EAAE,cAAc;YAClB,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,GAAG;YACZ,WAAW,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACvC,WAAW,EAAE;gBACT,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,GAAG;gBACT,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,GAAG;gBACV,OAAO,EAAE,GAAG;aACf;SACJ,CAAC;QAEF,yCAAyC;QACzC,MAAM,SAAS,GAAa;YACxB,EAAE,EAAE,YAAY;YAChB,QAAQ,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;YAClC,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,GAAG;YACZ,WAAW,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACvC,WAAW,EAAE;gBACT,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,GAAG;gBACV,OAAO,EAAE,GAAG;gBACZ,KAAK,EAAE,GAAG;gBACV,OAAO,EAAE,GAAG;aACf;SACJ,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAEM,SAAS,CAAC,MAAc;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAa;gBACvB,EAAE,EAAE,QAAQ,MAAM,IAAI,CAAC,EAAE;gBACzB,IAAI;gBACJ,IAAI;gBACJ,QAAQ;gBACR,QAAQ,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACjE,CAAC;YAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEM,eAAe,CAAC,QAAiB;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CACrB,UAAU,CAAC,eAAe;YAC1B,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,CAC5E,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAa;YACpB,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,MAAM;YAChB,QAAQ;SACX,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,WAAW,CAAC,MAAc;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAC,QAAiB,EAAE,MAAc;QACpD,MAAM,UAAU,GAAe,EAAE,CAAC;QAElC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC1B,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,EAAE,CAAC;gBAC5D,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,uBAAuB,CAAC,IAAc;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAEtD,OAAO;YACH,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC7C,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;SAChD,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,OAAkC;QACpD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;QAEnC,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,MAAM,CAAC;YACjB,IAAI,MAAM,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;QACjC,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAEO,aAAa,CAAC,OAAkC;QACpD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;QAEnC,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,MAAM,CAAC;YACjB,IAAI,MAAM,IAAI,CAAC;gBAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACpC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,OAAO,UAAU,GAAG,IAAI,CAAC;IAC7B,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;;AA9JL,gCA+JC;AA9J2B,0BAAe,GAAG,GAAG,AAAN,CAAO;AACtB,0BAAe,GAAG,GAAG,AAAN,CAAO"}