"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FullProductionTest = void 0;
const ContentOrchestrator_1 = require("../content/agents/ContentOrchestrator");
const PerformanceMetrics_1 = require("../rendering/PerformanceMetrics");
class FullProductionTest {
    constructor(config = {}) {
        this.startTime = 0;
        this.config = {
            enableDetailedLogging: true,
            performanceMonitoringInterval: 3000, // 3 segundos
            targetFPS: 999,
            maxExecutionTime: 1800000, // 30 minutos
            generateFullMap: true,
            ...config
        };
        this.orchestrator = new ContentOrchestrator_1.ContentOrchestrator();
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
    }
    async runFullProductionTest() {
        console.log('🚀 INICIANDO PRODUÇÃO EM MASSA COMPLETA DO TACTICAL NEXUS');
        console.log('==========================================================');
        console.log(`🎯 Meta de Performance: ${this.config.targetFPS} FPS @ 1440p`);
        console.log(`⏱️  Tempo máximo: ${this.config.maxExecutionTime / 60000} minutos`);
        console.log(`🗺️  Mapa completo: ${this.config.generateFullMap ? '2km x 2km' : 'Teste reduzido'}`);
        console.log(`📊 Monitoramento: ${this.config.enableDetailedLogging ? 'Ativado' : 'Desativado'}`);
        console.log('');
        this.startTime = Date.now();
        try {
            // Inicia monitoramento de performance em tempo real
            this.startPerformanceMonitoring();
            // Executa produção em massa completa
            await this.orchestrator.startMassProduction();
            // Para monitoramento
            this.stopPerformanceMonitoring();
            // Gera relatório final detalhado
            await this.generateDetailedReport();
            console.log('\n🎉 PRODUÇÃO EM MASSA CONCLUÍDA COM SUCESSO!');
            console.log('O Project: Tactical Nexus está pronto para jogadores!');
        }
        catch (error) {
            this.stopPerformanceMonitoring();
            console.error('\n💥 PRODUÇÃO EM MASSA FALHOU:', error);
            await this.generateErrorReport(error);
            throw error;
        }
    }
    startPerformanceMonitoring() {
        if (!this.config.enableDetailedLogging)
            return;
        console.log('📊 Iniciando monitoramento em tempo real...\n');
        this.monitoringInterval = setInterval(async () => {
            const status = await this.orchestrator.getProductionStatus();
            const elapsedTime = (Date.now() - this.startTime) / 1000;
            console.log(`⏱️  Tempo: ${this.formatTime(elapsedTime)}`);
            console.log(`📈 Progresso: ${status.overallProgress.toFixed(1)}%`);
            console.log(`🎯 FPS: ${status.currentMetrics.averageFPS.toFixed(2)}`);
            console.log(`💾 VRAM: ${status.currentMetrics.vramUsage.toFixed(0)} MB`);
            console.log(`🔄 Draw Calls: ${status.currentMetrics.drawCalls}`);
            // Mostra tarefas ativas
            const activeTasks = status.tasks.filter(task => task.status === 'in_progress');
            if (activeTasks.length > 0) {
                const taskNames = activeTasks.map(t => `${t.id} (${t.progress}%)`).join(', ');
                console.log(`🔧 Ativo: ${taskNames}`);
            }
            // Alerta de performance
            if (status.currentMetrics.averageFPS < this.config.targetFPS * 0.8) {
                console.log(`⚠️  ALERTA: FPS abaixo de 80% da meta!`);
            }
            console.log('─'.repeat(60));
        }, this.config.performanceMonitoringInterval);
    }
    stopPerformanceMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
    }
    async generateDetailedReport() {
        const totalTime = (Date.now() - this.startTime) / 1000;
        const status = await this.orchestrator.getProductionStatus();
        const reports = await this.orchestrator.getProductionReports();
        const poiSpecs = await this.orchestrator.getPOISpecs();
        console.log('\n📋 RELATÓRIO DETALHADO DE PRODUÇÃO EM MASSA');
        console.log('===========================================');
        // Informações gerais
        console.log(`⏱️  Tempo total: ${this.formatTime(totalTime)}`);
        console.log(`📊 Progresso final: ${status.overallProgress.toFixed(1)}%`);
        console.log(`✅ Tarefas concluídas: ${status.tasks.filter(t => t.status === 'completed').length}/${status.tasks.length}`);
        console.log(`❌ Tarefas falhadas: ${status.tasks.filter(t => t.status === 'failed').length}`);
        // Métricas de performance finais
        console.log('\n🎯 PERFORMANCE FINAL');
        console.log('─'.repeat(30));
        const finalMetrics = status.currentMetrics;
        const fpsStatus = finalMetrics.averageFPS >= this.config.targetFPS * 0.95 ? '✅' : '❌';
        const vramStatus = finalMetrics.vramUsage <= 8192 ? '✅' : '❌';
        const drawCallStatus = finalMetrics.drawCalls <= 2000 ? '✅' : '❌';
        console.log(`${fpsStatus} FPS: ${finalMetrics.averageFPS.toFixed(2)} (meta: ${this.config.targetFPS})`);
        console.log(`${vramStatus} VRAM: ${finalMetrics.vramUsage.toFixed(0)} MB (limite: 8192 MB)`);
        console.log(`${drawCallStatus} Draw Calls: ${finalMetrics.drawCalls} (limite: 2000)`);
        // Análise dos POIs produzidos
        console.log('\n🏗️  POIs PRODUZIDOS');
        console.log('─'.repeat(30));
        const poiByType = this.groupPOIsByType(poiSpecs);
        for (const [type, pois] of Object.entries(poiByType)) {
            console.log(`${type.toUpperCase()}: ${pois.length} POIs`);
            pois.forEach(poi => {
                const design = poi.designSpec ? '✅' : '❌';
                const art = poi.artAssets ? '✅' : '❌';
                const audio = poi.audioSetup ? '✅' : '❌';
                console.log(`  - ${poi.id}: Design ${design} | Arte ${art} | Áudio ${audio}`);
            });
        }
        // Análise de eficiência
        console.log('\n⚡ ANÁLISE DE EFICIÊNCIA');
        console.log('─'.repeat(30));
        const completedTasks = status.tasks.filter(t => t.status === 'completed' && t.actualTime);
        let totalEstimated = 0;
        let totalActual = 0;
        completedTasks.forEach(task => {
            totalEstimated += task.estimatedTime;
            totalActual += task.actualTime || 0;
            const efficiency = task.actualTime <= task.estimatedTime ? '✅' : '⚠️';
            const ratio = (task.actualTime / task.estimatedTime * 100).toFixed(1);
            console.log(`${efficiency} ${task.id}: ${(task.actualTime / 1000).toFixed(1)}s (${ratio}% do estimado)`);
        });
        const overallEfficiency = (totalEstimated / totalActual * 100).toFixed(1);
        console.log(`\n📊 Eficiência geral: ${overallEfficiency}%`);
        // Resultado final
        const allTargetsMet = fpsStatus === '✅' && vramStatus === '✅' && drawCallStatus === '✅';
        console.log(`\n🏆 RESULTADO: ${allTargetsMet ? '🎉 SUCESSO TOTAL!' : '⚠️  NECESSITA AJUSTES'}`);
        if (allTargetsMet) {
            console.log('\n🎮 O Project: Tactical Nexus está pronto para:');
            console.log('- Testes Alpha com jogadores reais');
            console.log('- Implementação de matchmaking');
            console.log('- Sistemas de progressão e economia');
            console.log('- Expansão de conteúdo adicional');
        }
    }
    async generateErrorReport(error) {
        const totalTime = (Date.now() - this.startTime) / 1000;
        const status = await this.orchestrator.getProductionStatus();
        console.log('\n💥 RELATÓRIO DE ERRO NA PRODUÇÃO');
        console.log('=================================');
        console.log(`⏱️  Tempo até falha: ${this.formatTime(totalTime)}`);
        console.log(`📊 Progresso: ${status.overallProgress.toFixed(1)}%`);
        console.log(`❌ Erro: ${error.message || error}`);
        // Estado das tarefas no momento da falha
        console.log('\n📋 Estado das tarefas:');
        status.tasks.forEach(task => {
            const icon = {
                'completed': '✅',
                'in_progress': '🔄',
                'pending': '⏳',
                'failed': '❌'
            }[task.status] || '❓';
            console.log(`${icon} ${task.id}: ${task.status} (${task.progress}%)`);
        });
    }
    groupPOIsByType(poiSpecs) {
        return poiSpecs.reduce((groups, poi) => {
            const type = poi.type;
            if (!groups[type])
                groups[type] = [];
            groups[type].push(poi);
            return groups;
        }, {});
    }
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    async runQuickValidation() {
        console.log('⚡ Executando validação rápida do sistema completo...');
        try {
            const status = await this.orchestrator.getProductionStatus();
            console.log(`✅ ContentOrchestrator: ${status.tasks.length} tarefas planejadas`);
            const poiSpecs = await this.orchestrator.getPOISpecs();
            console.log(`✅ POI Specs: ${poiSpecs.length} POIs definidos`);
            const metrics = this.metrics.getMapProductionStats();
            console.log(`✅ PerformanceMetrics: ${metrics.averageFPS.toFixed(2)} FPS simulados`);
            console.log('✅ Sistema completo validado - pronto para produção em massa!');
        }
        catch (error) {
            console.error('❌ Validação falhou:', error);
            throw error;
        }
    }
}
exports.FullProductionTest = FullProductionTest;
// Execução do teste
async function main() {
    const args = process.argv.slice(2);
    const isQuickTest = args.includes('--quick');
    const enableLogging = !args.includes('--no-logging');
    const targetFPS = parseInt(args.find(arg => arg.startsWith('--fps='))?.split('=')[1] || '999');
    const test = new FullProductionTest({
        enableDetailedLogging: enableLogging,
        targetFPS: targetFPS,
        generateFullMap: !isQuickTest
    });
    try {
        if (isQuickTest) {
            await test.runQuickValidation();
        }
        else {
            await test.runFullProductionTest();
        }
        process.exit(0);
    }
    catch (error) {
        console.error('\n💥 Teste falhou:', error);
        process.exit(1);
    }
}
// Executa apenas se chamado diretamente
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=fullProductionTest.js.map