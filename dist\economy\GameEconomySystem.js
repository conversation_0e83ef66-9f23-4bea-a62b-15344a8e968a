"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameEconomySystem = void 0;
const events_1 = require("events");
class GameEconomySystem extends events_1.EventEmitter {
    constructor() {
        super();
        this.playerEconomies = new Map();
        this.storeItems = new Map();
        this.dailyRewardTemplate = [];
        this.initializeStore();
        this.initializeDailyRewards();
        this.initializeBattlePass();
        console.log('💰 GameEconomySystem inicializado');
    }
    initializeStore() {
        const items = [
            // Armas
            {
                id: 'weapon_ak47', name: 'AK-47', description: 'Rifle de assalto poderoso',
                category: 'weapon', rarity: 'rare',
                price: { coins: 5000 },
                availability: { permanent: true, limited: false },
                requirements: { level: 10 }
            },
            {
                id: 'weapon_sniper_barrett', name: 'Barrett M82', description: 'Rifle sniper de alto calibre',
                category: 'weapon', rarity: 'epic',
                price: { coins: 12000 },
                availability: { permanent: true, limited: false },
                requirements: { level: 25 }
            },
            // Skins
            {
                id: 'skin_dragon_fire', name: 'Dragon Fire', description: 'Skin épica com efeitos de fogo',
                category: 'skin', rarity: 'epic',
                price: { premiumCurrency: 800 },
                availability: { permanent: false, limited: true, maxPurchases: 1000 }
            },
            {
                id: 'skin_gold_plated', name: 'Gold Plated', description: 'Skin dourada luxuosa',
                category: 'skin', rarity: 'legendary',
                price: { premiumCurrency: 1500 },
                availability: { permanent: true, limited: false },
                requirements: { level: 50 }
            },
            // Boosts
            {
                id: 'boost_xp_2x', name: '2x XP Boost', description: 'Dobra XP por 24 horas',
                category: 'boost', rarity: 'common',
                price: { coins: 2000, premiumCurrency: 100 },
                availability: { permanent: true, limited: false }
            },
            {
                id: 'boost_currency_1_5x', name: '1.5x Currency Boost', description: 'Aumenta ganho de moedas em 50%',
                category: 'boost', rarity: 'rare',
                price: { premiumCurrency: 200 },
                availability: { permanent: true, limited: false }
            },
            // Bundles
            {
                id: 'bundle_starter_pack', name: 'Starter Pack', description: 'Pacote inicial com armas e moedas',
                category: 'bundle', rarity: 'common',
                price: { premiumCurrency: 500 },
                availability: { permanent: true, limited: false, maxPurchases: 1 }
            },
            // Itens limitados
            {
                id: 'skin_christmas_special', name: 'Christmas Special', description: 'Skin especial de Natal',
                category: 'skin', rarity: 'mythic',
                price: { premiumCurrency: 2000 },
                availability: {
                    permanent: false,
                    limited: true,
                    startDate: new Date('2024-12-01'),
                    endDate: new Date('2024-12-31'),
                    maxPurchases: 500
                }
            }
        ];
        for (const item of items) {
            this.storeItems.set(item.id, item);
        }
    }
    initializeDailyRewards() {
        this.dailyRewardTemplate = [
            { day: 1, reward: { coins: 100 }, claimed: false },
            { day: 2, reward: { coins: 150 }, claimed: false },
            { day: 3, reward: { coins: 200, experience: 500 }, claimed: false },
            { day: 4, reward: { coins: 250 }, claimed: false },
            { day: 5, reward: { coins: 300, item: 'boost_xp_2x' }, claimed: false },
            { day: 6, reward: { coins: 400 }, claimed: false },
            { day: 7, reward: { coins: 500, premiumCurrency: 50 }, claimed: false }
        ];
    }
    initializeBattlePass() {
        const tiers = [];
        for (let i = 1; i <= 100; i++) {
            const tier = {
                tier: i,
                experienceRequired: 1000 * i,
                freeReward: i % 5 === 0 ? { type: 'coins', value: 500 } : undefined,
                premiumReward: i % 10 === 0 ?
                    { type: 'skin', value: `battlepass_skin_tier_${i}` } :
                    { type: 'coins', value: 200 }
            };
            tiers.push(tier);
        }
        this.activeBattlePass = {
            id: 'season_1_battlepass',
            name: 'Season 1: Urban Warfare',
            season: 1,
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-03-31'),
            tiers,
            premiumPrice: 1000,
            active: true
        };
    }
    createPlayerEconomy(playerId) {
        const economy = {
            playerId,
            currency: {
                coins: 2000, // Moedas iniciais
                premiumCurrency: 0
            },
            purchaseHistory: [],
            dailyRewards: {
                currentStreak: 0,
                rewards: this.dailyRewardTemplate.map(r => ({ ...r }))
            },
            battlePass: this.activeBattlePass ? {
                battlePassId: this.activeBattlePass.id,
                tier: 1,
                experience: 0,
                premiumUnlocked: false,
                claimedTiers: []
            } : undefined,
            inventory: {
                weapons: ['assault_rifle_m4'], // Arma inicial
                skins: ['skin_default'],
                attachments: [],
                cosmetics: [],
                boosts: []
            }
        };
        this.playerEconomies.set(playerId, economy);
        console.log(`💰 Economia criada para jogador ${playerId}`);
        return economy;
    }
    purchaseItem(playerId, itemId, paymentMethod) {
        const economy = this.playerEconomies.get(playerId);
        const item = this.storeItems.get(itemId);
        if (!economy || !item) {
            console.log(`❌ Compra falhou: jogador ou item não encontrado`);
            return false;
        }
        // Verifica disponibilidade
        if (!this.isItemAvailable(item)) {
            console.log(`❌ Item ${itemId} não está disponível`);
            return false;
        }
        // Verifica se já possui o item (para itens únicos)
        if (this.playerOwnsItem(economy, itemId)) {
            console.log(`❌ Jogador já possui o item ${itemId}`);
            return false;
        }
        // Verifica preço e saldo
        const price = paymentMethod === 'coins' ? item.price.coins : item.price.premiumCurrency;
        if (!price) {
            console.log(`❌ Item ${itemId} não pode ser comprado com ${paymentMethod}`);
            return false;
        }
        const currentBalance = paymentMethod === 'coins' ? economy.currency.coins : economy.currency.premiumCurrency;
        if (currentBalance < price) {
            console.log(`❌ Saldo insuficiente para comprar ${itemId}`);
            return false;
        }
        // Processa a compra
        if (paymentMethod === 'coins') {
            economy.currency.coins -= price;
        }
        else {
            economy.currency.premiumCurrency -= price;
        }
        // Adiciona item ao inventário
        this.addItemToInventory(economy, item);
        // Registra a compra
        const purchase = {
            id: `purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            playerId,
            itemId,
            price: paymentMethod === 'coins' ? { coins: price, premiumCurrency: 0 } : { coins: 0, premiumCurrency: price },
            timestamp: new Date(),
            paymentMethod
        };
        economy.purchaseHistory.push(purchase);
        console.log(`✅ ${playerId} comprou ${item.name} por ${price} ${paymentMethod}`);
        this.emit('itemPurchased', { playerId, item, purchase, economy });
        return true;
    }
    isItemAvailable(item) {
        const now = new Date();
        if (!item.availability.permanent) {
            if (item.availability.startDate && now < item.availability.startDate)
                return false;
            if (item.availability.endDate && now > item.availability.endDate)
                return false;
        }
        // Verificar limite de compras seria implementado aqui
        return true;
    }
    playerOwnsItem(economy, itemId) {
        const item = this.storeItems.get(itemId);
        if (!item)
            return false;
        switch (item.category) {
            case 'weapon':
                return economy.inventory.weapons.includes(itemId);
            case 'skin':
                return economy.inventory.skins.includes(itemId);
            case 'attachment':
                return economy.inventory.attachments.includes(itemId);
            case 'cosmetic':
                return economy.inventory.cosmetics.includes(itemId);
            default:
                return false;
        }
    }
    addItemToInventory(economy, item) {
        switch (item.category) {
            case 'weapon':
                economy.inventory.weapons.push(item.id);
                break;
            case 'skin':
                economy.inventory.skins.push(item.id);
                break;
            case 'attachment':
                economy.inventory.attachments.push(item.id);
                break;
            case 'cosmetic':
                economy.inventory.cosmetics.push(item.id);
                break;
            case 'boost':
                // Boosts são consumíveis
                break;
        }
    }
    claimDailyReward(playerId) {
        const economy = this.playerEconomies.get(playerId);
        if (!economy)
            return false;
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const lastClaim = economy.dailyRewards.lastClaimDate;
        // Verifica se já reivindicou hoje
        if (lastClaim && lastClaim >= today) {
            console.log(`❌ Recompensa diária já reivindicada hoje para ${playerId}`);
            return false;
        }
        // Verifica streak
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        if (!lastClaim || lastClaim < yesterday) {
            // Reset streak se perdeu um dia
            economy.dailyRewards.currentStreak = 0;
        }
        economy.dailyRewards.currentStreak++;
        if (economy.dailyRewards.currentStreak > 7) {
            economy.dailyRewards.currentStreak = 1; // Reinicia ciclo
        }
        const rewardDay = economy.dailyRewards.currentStreak;
        const reward = economy.dailyRewards.rewards[rewardDay - 1];
        // Aplica recompensas
        if (reward.reward.coins) {
            economy.currency.coins += reward.reward.coins;
        }
        if (reward.reward.premiumCurrency) {
            economy.currency.premiumCurrency += reward.reward.premiumCurrency;
        }
        reward.claimed = true;
        reward.claimedAt = now;
        economy.dailyRewards.lastClaimDate = now;
        console.log(`🎁 ${playerId} reivindicou recompensa diária do dia ${rewardDay}`);
        this.emit('dailyRewardClaimed', { playerId, day: rewardDay, reward, economy });
        return true;
    }
    addCurrency(playerId, amount, source = 'gameplay') {
        const economy = this.playerEconomies.get(playerId);
        if (!economy)
            return;
        economy.currency.coins += amount.coins;
        economy.currency.premiumCurrency += amount.premiumCurrency;
        console.log(`💰 ${playerId} recebeu ${amount.coins} moedas e ${amount.premiumCurrency} moeda premium (${source})`);
        this.emit('currencyAdded', { playerId, amount, source, economy });
    }
    getPlayerEconomy(playerId) {
        return this.playerEconomies.get(playerId);
    }
    getStoreItems(category) {
        const items = Array.from(this.storeItems.values());
        if (category) {
            return items.filter(item => item.category === category);
        }
        return items.filter(item => this.isItemAvailable(item));
    }
    getActiveBattlePass() {
        return this.activeBattlePass;
    }
    addBattlePassExperience(playerId, amount) {
        const economy = this.playerEconomies.get(playerId);
        if (!economy || !economy.battlePass || !this.activeBattlePass)
            return;
        economy.battlePass.experience += amount;
        // Verifica tier ups
        const currentTier = this.activeBattlePass.tiers.find(t => t.experienceRequired <= economy.battlePass.experience &&
            t.tier > economy.battlePass.tier);
        if (currentTier) {
            economy.battlePass.tier = currentTier.tier;
            console.log(`🎖️ ${playerId} avançou para o tier ${currentTier.tier} do Battle Pass`);
            this.emit('battlePassTierUp', { playerId, tier: currentTier.tier, economy });
        }
    }
    getEconomyStats() {
        const economies = Array.from(this.playerEconomies.values());
        return {
            totalPlayers: economies.length,
            totalCoinsInCirculation: economies.reduce((sum, e) => sum + e.currency.coins, 0),
            totalPremiumCurrency: economies.reduce((sum, e) => sum + e.currency.premiumCurrency, 0),
            totalPurchases: economies.reduce((sum, e) => sum + e.purchaseHistory.length, 0),
            averageSpending: economies.reduce((sum, e) => sum + e.purchaseHistory.length, 0) / economies.length,
            dailyActiveRewards: economies.filter(e => {
                const today = new Date();
                const lastClaim = e.dailyRewards.lastClaimDate;
                return lastClaim && lastClaim.toDateString() === today.toDateString();
            }).length
        };
    }
}
exports.GameEconomySystem = GameEconomySystem;
exports.default = GameEconomySystem;
//# sourceMappingURL=GameEconomySystem.js.map