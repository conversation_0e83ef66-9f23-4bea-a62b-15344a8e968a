{"version": 3, "file": "EconomySystem.js", "sourceRoot": "", "sources": ["../../src/gameplay/EconomySystem.ts"], "names": [], "mappings": ";;;AAmBA,MAAa,aAAa;IAStB;QAJQ,gBAAW,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC7C,aAAQ,GAAyB,IAAI,GAAG,EAAE,CAAC;QAC3C,gBAAW,GAAsB,IAAI,GAAG,EAAE,CAAC;QAG/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAW;YAClB,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;YAC1E,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;YAC9E,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;YAC3E,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;YAC3E,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE;YACpF,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE;YACpF,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE;YACrF,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;SACtF,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,qBAAqB,CAAC,QAAgB;QACzC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;IAChE,CAAC;IAEM,aAAa,CAAC,QAAgB;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;IAC7E,CAAC;IAEM,iBAAiB,CAAC,QAAgB,EAAE,YAAoB;QAC3D,MAAM,MAAM,GAAG,aAAa,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,MAAM,CAAC,CAAC;IAC1D,CAAC;IAEM,aAAa,CAAC,YAAoB,EAAE,UAAkB,EAAE,MAAc;QACzE,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,eAAe,GAAG,MAAM;YAAE,OAAO,KAAK,CAAC;QAE3C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,GAAG,MAAM,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,GAAG,MAAM,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,UAAU,CAAC,QAAgB,EAAE,MAAc;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IACpD,CAAC;IAEM,OAAO,CAAC,QAAgB,EAAE,MAAc;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC;YAAE,OAAO,KAAK,CAAC;QAErD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAC,OAAgB;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAEM,iBAAiB,CAAC,SAAiB;QACtC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAEM,WAAW,CAAC,QAAiB;QAChC,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM;gBAAE,OAAO,EAAE,CAAC;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;;AA1FL,sCA2FC;AA1F2B,2BAAa,GAAG,GAAG,AAAN,CAAO;AACpB,yBAAW,GAAG,GAAG,AAAN,CAAO;AAClB,kCAAoB,GAAG,IAAI,AAAP,CAAQ"}