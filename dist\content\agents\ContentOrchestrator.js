"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentOrchestrator = void 0;
const LevelDesignAgent_1 = require("./LevelDesignAgent");
const ArtAgent_1 = require("./ArtAgent");
const AudioAgent_1 = require("./AudioAgent");
const ContentProductionSystem_1 = require("../ContentProductionSystem");
const PerformanceMetrics_1 = require("../../rendering/PerformanceMetrics");
const TerrainGenerator_1 = require("../../gameplay/TerrainGenerator");
class ContentOrchestrator {
    constructor() {
        this.productionTasks = new Map();
        this.poiSpecs = new Map();
        this.productionReports = [];
        this.isProducing = false;
        this.levelDesignAgent = new LevelDesignAgent_1.LevelDesignAgent();
        this.artAgent = new ArtAgent_1.ArtAgent();
        this.audioAgent = new AudioAgent_1.AudioAgent();
        this.contentSystem = new ContentProductionSystem_1.ContentProductionSystem();
        this.terrainGenerator = new TerrainGenerator_1.TerrainGenerator({
            size: 2048,
            resolution: 2,
            maxHeight: 200,
            minHeight: 0,
            smoothingPasses: 3
        });
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
    }
    async startMassProduction() {
        if (this.isProducing) {
            throw new Error('Produção já está em andamento');
        }
        console.log('🚀 Content Orchestrator: Iniciando produção em massa do mapa principal');
        this.isProducing = true;
        try {
            // Fase 1: Planejamento e preparação
            await this.planProductionPhases();
            // Fase 2: Produção do terreno base
            await this.produceBaseTerrain();
            // Fase 3: Produção em massa dos POIs
            await this.produceMassPOIs();
            // Fase 4: Iluminação global
            await this.setupGlobalLighting();
            // Fase 5: Otimização final
            await this.performFinalOptimization();
            // Fase 6: Validação de performance
            await this.validatePerformanceTargets();
            console.log('✅ Produção em massa concluída com sucesso!');
        }
        catch (error) {
            console.error('❌ Erro na produção em massa:', error);
            throw error;
        }
        finally {
            this.isProducing = false;
        }
    }
    async planProductionPhases() {
        console.log('📋 Planejando fases de produção...');
        // Define todas as tarefas de produção
        const tasks = [
            {
                id: 'terrain_base',
                type: 'terrain',
                priority: 1,
                status: 'pending',
                progress: 0,
                dependencies: [],
                estimatedTime: 300000 // 5 minutos
            },
            {
                id: 'poi_urban_1',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 180000 // 3 minutos
            },
            {
                id: 'poi_urban_2',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 180000
            },
            {
                id: 'poi_urban_3',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 180000
            },
            {
                id: 'poi_military_1',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 200000
            },
            {
                id: 'poi_military_2',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 200000
            },
            {
                id: 'poi_industrial_1',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 190000
            },
            {
                id: 'poi_industrial_2',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 190000
            },
            {
                id: 'poi_forest_1',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 170000
            },
            {
                id: 'poi_forest_2',
                type: 'poi',
                priority: 2,
                status: 'pending',
                progress: 0,
                dependencies: ['terrain_base'],
                estimatedTime: 170000
            },
            {
                id: 'global_lighting',
                type: 'lighting',
                priority: 3,
                status: 'pending',
                progress: 0,
                dependencies: ['poi_urban_1', 'poi_urban_2', 'poi_urban_3', 'poi_military_1', 'poi_military_2', 'poi_industrial_1', 'poi_industrial_2', 'poi_forest_1', 'poi_forest_2'],
                estimatedTime: 240000 // 4 minutos
            },
            {
                id: 'final_optimization',
                type: 'optimization',
                priority: 4,
                status: 'pending',
                progress: 0,
                dependencies: ['global_lighting'],
                estimatedTime: 300000 // 5 minutos
            }
        ];
        // Armazena todas as tarefas
        for (const task of tasks) {
            this.productionTasks.set(task.id, task);
        }
        // Define especificações dos POIs
        this.definePOISpecifications();
        console.log(`✅ ${tasks.length} tarefas de produção planejadas`);
    }
    definePOISpecifications() {
        // POIs Urbanos
        this.poiSpecs.set('poi_urban_1', {
            id: 'poi_urban_1',
            type: 'urban',
            position: { x: -400, y: 10, z: -400 },
            size: { width: 150, length: 150, height: 40 }
        });
        this.poiSpecs.set('poi_urban_2', {
            id: 'poi_urban_2',
            type: 'urban',
            position: { x: 0, y: 15, z: -500 },
            size: { width: 120, length: 180, height: 35 }
        });
        this.poiSpecs.set('poi_urban_3', {
            id: 'poi_urban_3',
            type: 'urban',
            position: { x: 400, y: 12, z: -300 },
            size: { width: 140, length: 140, height: 45 }
        });
        // POIs Militares
        this.poiSpecs.set('poi_military_1', {
            id: 'poi_military_1',
            type: 'military',
            position: { x: -600, y: 5, z: 200 },
            size: { width: 200, length: 250, height: 25 }
        });
        this.poiSpecs.set('poi_military_2', {
            id: 'poi_military_2',
            type: 'military',
            position: { x: 300, y: 8, z: 400 },
            size: { width: 180, length: 200, height: 30 }
        });
        // POIs Industriais
        this.poiSpecs.set('poi_industrial_1', {
            id: 'poi_industrial_1',
            type: 'industrial',
            position: { x: -200, y: 3, z: 600 },
            size: { width: 220, length: 160, height: 50 }
        });
        this.poiSpecs.set('poi_industrial_2', {
            id: 'poi_industrial_2',
            type: 'industrial',
            position: { x: 500, y: 6, z: 100 },
            size: { width: 190, length: 170, height: 45 }
        });
        // POIs Florestais
        this.poiSpecs.set('poi_forest_1', {
            id: 'poi_forest_1',
            type: 'forest',
            position: { x: -800, y: 20, z: -100 },
            size: { width: 160, length: 160, height: 20 }
        });
        this.poiSpecs.set('poi_forest_2', {
            id: 'poi_forest_2',
            type: 'forest',
            position: { x: 700, y: 25, z: -600 },
            size: { width: 140, length: 180, height: 25 }
        });
    }
    async produceBaseTerrain() {
        console.log('🌍 Produzindo terreno base do mapa 2km x 2km...');
        const task = this.productionTasks.get('terrain_base');
        task.status = 'in_progress';
        const startTime = Date.now();
        try {
            // Define regiões do mapa
            const regions = [
                { type: 'urban', center: { x: 0, y: 15, z: -400 }, radius: 300 },
                { type: 'military', center: { x: -300, y: 8, z: 300 }, radius: 400 },
                { type: 'industrial', center: { x: 350, y: 5, z: 350 }, radius: 350 },
                { type: 'forest', center: { x: -500, y: 22, z: -350 }, radius: 450 }
            ];
            // Gera terreno usando TerrainGenerator
            const terrainData = await this.terrainGenerator.generateTerrain(regions);
            // Processa terreno através do ContentProductionSystem
            await this.contentSystem.queueEnvironmentProduction({
                models: [terrainData.geometry],
                textures: terrainData.textures,
                ambientSounds: []
            });
            task.status = 'completed';
            task.progress = 100;
            task.actualTime = Date.now() - startTime;
            console.log(`✅ Terreno base concluído em ${task.actualTime / 1000}s`);
        }
        catch (error) {
            task.status = 'failed';
            console.error('❌ Erro na produção do terreno:', error);
            throw error;
        }
    }
    async produceMassPOIs() {
        console.log('🏗️ Iniciando produção em massa dos 9 POIs principais...');
        // Produz POIs em paralelo (máximo 3 simultâneos para não sobrecarregar)
        const poiTasks = Array.from(this.productionTasks.values())
            .filter(task => task.type === 'poi')
            .sort((a, b) => a.priority - b.priority);
        const batchSize = 3;
        for (let i = 0; i < poiTasks.length; i += batchSize) {
            const batch = poiTasks.slice(i, i + batchSize);
            const batchPromises = batch.map(task => this.produceSinglePOI(task.id));
            await Promise.all(batchPromises);
            // Pequena pausa entre batches para permitir otimizações
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        console.log('✅ Todos os POIs principais foram produzidos');
    }
    async produceSinglePOI(taskId) {
        const task = this.productionTasks.get(taskId);
        const poiSpec = this.poiSpecs.get(taskId);
        console.log(`🏗️ Produzindo POI ${poiSpec.type} (${taskId})...`);
        task.status = 'in_progress';
        const startTime = Date.now();
        try {
            // Fase 1: Design tático (Level Design Agent)
            task.progress = 10;
            const designSpec = await this.levelDesignAgent.designCompletePOILayout(poiSpec.type, poiSpec.position, poiSpec.size);
            poiSpec.designSpec = designSpec;
            // Fase 2: Criação de assets visuais (Art Agent)
            task.progress = 40;
            const artAssets = await this.artAgent.createPOIAssets(poiSpec.type, poiSpec.id, designSpec.layout);
            poiSpec.artAssets = artAssets;
            // Fase 3: Setup de iluminação local
            task.progress = 60;
            const lightingSetup = await this.artAgent.setupPOILighting(poiSpec.type, poiSpec.id, 'day');
            // Fase 4: Ambientação sonora (Audio Agent)
            task.progress = 80;
            const audioSetup = await this.audioAgent.createPOIAmbientSoundscape(poiSpec.type, poiSpec.id, poiSpec.position, poiSpec.size);
            poiSpec.audioSetup = audioSetup;
            // Fase 5: Integração e otimização
            task.progress = 95;
            await this.integratePOIAssets(poiSpec);
            task.status = 'completed';
            task.progress = 100;
            task.actualTime = Date.now() - startTime;
            console.log(`✅ POI ${poiSpec.type} (${taskId}) concluído em ${task.actualTime / 1000}s`);
        }
        catch (error) {
            task.status = 'failed';
            console.error(`❌ Erro na produção do POI ${taskId}:`, error);
            throw error;
        }
    }
    async integratePOIAssets(poiSpec) {
        // Integra todos os assets do POI no sistema de produção
        if (poiSpec.artAssets) {
            await this.contentSystem.queueEnvironmentProduction({
                models: [...poiSpec.artAssets.buildings, ...poiSpec.artAssets.props],
                textures: poiSpec.artAssets.textures,
                ambientSounds: poiSpec.audioSetup?.ambientZones.map((zone) => zone.sounds).flat() || []
            });
        }
        // Monitora performance durante a integração
        const performanceMetrics = this.metrics.getMapProductionStats();
        if (performanceMetrics.averageFPS < 800) {
            console.warn(`⚠️ Performance abaixo do esperado no POI ${poiSpec.id}: ${performanceMetrics.averageFPS} FPS`);
        }
    }
    async setupGlobalLighting() {
        console.log('💡 Configurando iluminação global do mapa...');
        const task = this.productionTasks.get('global_lighting');
        task.status = 'in_progress';
        const startTime = Date.now();
        try {
            // Configura iluminação para diferentes períodos do dia
            const timeOfDaySetups = ['dawn', 'day', 'dusk', 'night'];
            for (const timeOfDay of timeOfDaySetups) {
                task.progress = (timeOfDaySetups.indexOf(timeOfDay) + 1) * 25;
                // Configura iluminação global para cada POI
                for (const [poiId, poiSpec] of this.poiSpecs) {
                    await this.artAgent.setupPOILighting(poiSpec.type, poiId, timeOfDay);
                }
            }
            task.status = 'completed';
            task.progress = 100;
            task.actualTime = Date.now() - startTime;
            console.log(`✅ Iluminação global configurada em ${task.actualTime / 1000}s`);
        }
        catch (error) {
            task.status = 'failed';
            console.error('❌ Erro na configuração de iluminação:', error);
            throw error;
        }
    }
    async performFinalOptimization() {
        console.log('🔧 Executando otimização final do mapa...');
        const task = this.productionTasks.get('final_optimization');
        task.status = 'in_progress';
        const startTime = Date.now();
        try {
            // Otimização de geometria
            task.progress = 20;
            await this.contentSystem.optimizeGeometry();
            // Otimização de texturas
            task.progress = 40;
            await this.contentSystem.optimizeTextures();
            // Otimização de iluminação
            task.progress = 60;
            await this.contentSystem.optimizeLighting();
            // Otimização de áudio
            task.progress = 80;
            await this.contentSystem.optimizeAudio();
            // Otimização final de overdraw e occlusion
            task.progress = 95;
            await this.optimizeRenderingPipeline();
            task.status = 'completed';
            task.progress = 100;
            task.actualTime = Date.now() - startTime;
            console.log(`✅ Otimização final concluída em ${task.actualTime / 1000}s`);
        }
        catch (error) {
            task.status = 'failed';
            console.error('❌ Erro na otimização final:', error);
            throw error;
        }
    }
    async optimizeRenderingPipeline() {
        // Otimiza pipeline de renderização para todos os POIs
        for (const [poiId, poiSpec] of this.poiSpecs) {
            if (poiSpec.artAssets) {
                // Simula otimização de overdraw para o POI
                const scene = {
                    camera: {
                        position: poiSpec.position,
                        frustum: { fov: 90, near: 0.1, far: 1000 }
                    },
                    objects: [
                        ...poiSpec.artAssets.buildings.map((building) => ({
                            position: poiSpec.position,
                            material: { transparent: false },
                            boundingBox: {
                                min: { x: -building.dimensions.width / 2, y: 0, z: -building.dimensions.depth / 2 },
                                max: { x: building.dimensions.width / 2, y: building.dimensions.height, z: building.dimensions.depth / 2 }
                            }
                        })),
                        ...poiSpec.artAssets.props.map((prop) => ({
                            position: poiSpec.position,
                            material: { transparent: false },
                            boundingBox: {
                                min: { x: -prop.dimensions.width / 2, y: 0, z: -prop.dimensions.depth / 2 },
                                max: { x: prop.dimensions.width / 2, y: prop.dimensions.height, z: prop.dimensions.depth / 2 }
                            }
                        }))
                    ]
                };
                // Aplica otimizações através do AssetProductionManager
                // (que já tem integração com OverdrawOptimizer)
            }
        }
    }
    async validatePerformanceTargets() {
        console.log('🎯 Validando metas de performance (999 FPS @ 1440p)...');
        // Aguarda estabilização das métricas
        await new Promise(resolve => setTimeout(resolve, 2000));
        const finalMetrics = this.metrics.getMapProductionStats();
        const targetFPS = 999;
        const tolerance = 0.95; // 5% de tolerância
        if (finalMetrics.averageFPS < targetFPS * tolerance) {
            const shortfall = targetFPS * tolerance - finalMetrics.averageFPS;
            console.warn(`⚠️ Performance abaixo da meta: ${finalMetrics.averageFPS.toFixed(2)} FPS (faltam ${shortfall.toFixed(2)} FPS)`);
            // Aplica otimizações adicionais se necessário
            await this.applyEmergencyOptimizations(shortfall);
        }
        else {
            console.log(`✅ Meta de performance atingida: ${finalMetrics.averageFPS.toFixed(2)} FPS`);
        }
        // Gera relatório final
        await this.generateFinalReport();
    }
    async applyEmergencyOptimizations(shortfall) {
        console.log(`🚨 Aplicando otimizações de emergência (déficit: ${shortfall.toFixed(2)} FPS)...`);
        if (shortfall > 100) {
            // Reduz qualidade de texturas drasticamente
            console.log('- Reduzindo qualidade de texturas para 50%');
            // Implementação específica dependeria do TextureOptimizer
        }
        if (shortfall > 50) {
            // Reduz LODs mais agressivamente
            console.log('- Aplicando LODs mais agressivos');
            // Implementação específica dependeria do GeometryOptimizer
        }
        if (shortfall > 20) {
            // Reduz qualidade de sombras
            console.log('- Reduzindo qualidade de sombras');
            // Implementação específica dependeria do sistema de iluminação
        }
        // Verifica novamente após otimizações
        await new Promise(resolve => setTimeout(resolve, 1000));
        const newMetrics = this.metrics.getMapProductionStats();
        console.log(`📊 Performance após otimizações: ${newMetrics.averageFPS.toFixed(2)} FPS`);
    }
    async generateFinalReport() {
        const completedTasks = Array.from(this.productionTasks.values())
            .filter(task => task.status === 'completed').length;
        const failedTasks = Array.from(this.productionTasks.values())
            .filter(task => task.status === 'failed').length;
        const totalTasks = this.productionTasks.size;
        const overallProgress = (completedTasks / totalTasks) * 100;
        const report = {
            timestamp: new Date(),
            totalTasks,
            completedTasks,
            failedTasks,
            overallProgress,
            performanceMetrics: this.metrics.getMapProductionStats(),
            issues: this.collectProductionIssues(),
            recommendations: this.generateRecommendations()
        };
        this.productionReports.push(report);
        console.log('\n📊 RELATÓRIO FINAL DE PRODUÇÃO');
        console.log('================================');
        console.log(`Tarefas concluídas: ${completedTasks}/${totalTasks} (${overallProgress.toFixed(1)}%)`);
        console.log(`Tarefas falhadas: ${failedTasks}`);
        console.log(`Performance final: ${report.performanceMetrics.averageFPS.toFixed(2)} FPS`);
        console.log(`VRAM utilizada: ${report.performanceMetrics.vramUsage.toFixed(0)} MB`);
        console.log(`Draw calls: ${report.performanceMetrics.drawCalls}`);
        if (report.issues.length > 0) {
            console.log('\n⚠️ Problemas identificados:');
            report.issues.forEach(issue => console.log(`- ${issue}`));
        }
        if (report.recommendations.length > 0) {
            console.log('\n💡 Recomendações:');
            report.recommendations.forEach(rec => console.log(`- ${rec}`));
        }
    }
    collectProductionIssues() {
        const issues = [];
        const metrics = this.metrics.getMapProductionStats();
        if (metrics.averageFPS < 950) {
            issues.push(`FPS abaixo do ideal: ${metrics.averageFPS.toFixed(2)} (meta: 999)`);
        }
        if (metrics.vramUsage > 7000) {
            issues.push(`Alto uso de VRAM: ${metrics.vramUsage.toFixed(0)} MB`);
        }
        if (metrics.drawCalls > 2000) {
            issues.push(`Muitos draw calls: ${metrics.drawCalls} (recomendado: <2000)`);
        }
        const failedTasks = Array.from(this.productionTasks.values())
            .filter(task => task.status === 'failed');
        if (failedTasks.length > 0) {
            issues.push(`${failedTasks.length} tarefas falharam na produção`);
        }
        return issues;
    }
    generateRecommendations() {
        const recommendations = [];
        const metrics = this.metrics.getMapProductionStats();
        if (metrics.averageFPS < 999) {
            recommendations.push('Considere reduzir densidade de props em POIs menos importantes');
            recommendations.push('Implemente LODs mais agressivos para objetos distantes');
        }
        if (metrics.vramUsage > 6000) {
            recommendations.push('Comprima texturas adicionalmente usando formatos mais eficientes');
            recommendations.push('Considere usar atlas de texturas para reduzir draw calls');
        }
        recommendations.push('Execute testes de stress com 100 jogadores simultâneos');
        recommendations.push('Monitore performance em diferentes configurações de hardware');
        return recommendations;
    }
    async getProductionStatus() {
        const tasks = Array.from(this.productionTasks.values());
        const completedTasks = tasks.filter(task => task.status === 'completed').length;
        const overallProgress = (completedTasks / tasks.length) * 100;
        return {
            isProducing: this.isProducing,
            tasks,
            overallProgress,
            currentMetrics: this.metrics.getMapProductionStats()
        };
    }
    async getProductionReports() {
        return this.productionReports;
    }
    async getPOISpecs() {
        return Array.from(this.poiSpecs.values());
    }
    async stopProduction() {
        if (this.isProducing) {
            console.log('🛑 Parando produção em massa...');
            this.isProducing = false;
        }
    }
}
exports.ContentOrchestrator = ContentOrchestrator;
//# sourceMappingURL=ContentOrchestrator.js.map