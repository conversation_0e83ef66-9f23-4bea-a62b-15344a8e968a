{"version": 3, "file": "FeedbackManager.js", "sourceRoot": "", "sources": ["../../src/server/FeedbackManager.ts"], "names": [], "mappings": ";;;AASA,MAAa,eAAe;IAexB,YAAY,SAA0B;QAClC,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAAgE;QACxF,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,WAAW,GAAmB;YAChC,GAAG,QAAQ;YACX,EAAE,EAAE,UAAU;YACd,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,SAAS;YACpB,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,kBAAkB;QAClB,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,WAAW,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAClF,CAAC;QAED,oDAAoD;QACpD,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YACvB,WAAW,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAE5C,sDAAsD;QACtD,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa,IAAI,QAAQ,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC;YACnE,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,MAAyD;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,SAAS,GAAiB;YAC5B,GAAG,MAAM;YACT,EAAE,EAAE,QAAQ;YACZ,SAAS;YACT,MAAM,EAAE,SAAS;SACpB,CAAC;QAEF,kBAAkB;QAClB,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,SAAS,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC9E,CAAC;QAED,4DAA4D;QAC5D,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEtC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,WAAiC;QAC9D,MAAM,oBAAoB,GAAyB,EAAE,CAAC;QAEtD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,mCAAmC;YACnC,IAAI,UAAU,CAAC,IAAI,GAAG,eAAe,CAAC,mBAAmB,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpE,MAAM,IAAI,KAAK,CAAC,sBAAsB,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,CAAC;YAED,oCAAoC;YACpC,IAAI,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,UAAU,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACxE,CAAC;YAED,wDAAwD;YACxD,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,CAAC;YAED,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,8CAA8C;QAC9C,OAAO;YACH,GAAG,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE;YAC/B,IAAI,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE;YACjC,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE;YAChC,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE;YAChC,QAAQ,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;YAClC,MAAM,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE;SACjC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,YAAY;QACrB,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEvD,OAAO;YACH,QAAQ,EAAE,iBAAiB;YAC3B,OAAO,EAAE,eAAe;SAC3B,CAAC;IACN,CAAC;IAEO,yBAAyB;QAC7B,MAAM,SAAS,GAAsB;YACjC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACnC,aAAa,EAAE,CAAC;YAChB,iBAAiB,EAAE,CAAC;YACpB,qBAAqB,EAAE,CAAC;YACxB,eAAe,EAAE,IAAI,GAAG,EAAE;YAC1B,SAAS,EAAE,EAAE;SAChB,CAAC;QAEF,mBAAmB;QACnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,QAAQ,CAAC,MAAM,KAAK,UAAU,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACjE,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,aAAa,EAAE,CAAC;YAC9B,CAAC;YAED,6BAA6B;YAC7B,MAAM,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpE,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,qBAAqB;QACrB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAoD,CAAC;QAC7E,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;YAClE,KAAK,CAAC,KAAK,EAAE,CAAC;YACd,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAChE,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC;QAED,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAC/C,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YACxB,QAAQ;YACR,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;SAC/C,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAElB,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,uBAAuB;QAC3B,MAAM,SAAS,GAAoB;YAC/B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC/B,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,IAAI,GAAG,EAAE;YACxB,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,CAAC;YACjB,iBAAiB,EAAE,CAAC;SACvB,CAAC;QAEF,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACzC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC/B,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC5B,IAAI,MAAM,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;oBACjC,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBACjC,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;oBAC7C,SAAS,CAAC,cAAc,EAAE,CAAC;gBAC/B,CAAC;gBAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACjD,eAAe,IAAI,UAAU,CAAC;oBAC9B,aAAa,EAAE,CAAC;gBACpB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,aAAa,EAAE,CAAC;YAC9B,CAAC;YAED,6BAA6B;YAC7B,MAAM,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,SAAS,CAAC,iBAAiB,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtF,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,gBAAgB,CAAC,QAAoC;QACzD,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;YAC1B,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,QAAwB;QAC5D,4CAA4C;QAC5C,8DAA8D;IAClE,CAAC;IAEO,UAAU;QACd,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,yDAAyD;IACjD,KAAK,CAAC,aAAa;QACvB,qDAAqD;QACrD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,6CAA6C;QAC7C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,WAAW;QACrB,gDAAgD;QAChD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,+CAA+C;QAC/C,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,GAAW;QACvC,gEAAgE;QAChE,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,GAAW;QACpC,2CAA2C;IAC/C,CAAC;;AApRL,0CAqRC;AApR2B,mCAAmB,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO;AAC/C,kCAAkB,GAAG;IACzC,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,kBAAkB;IAClB,mCAAmC;CACtC,CAAC"}