"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapProductionTest = void 0;
const InitialMapProducer_1 = require("../gameplay/InitialMapProducer");
const GeometryOptimizer_1 = require("../rendering/GeometryOptimizer");
const TextureOptimizer_1 = require("../rendering/TextureOptimizer");
const AssetProductionManager_1 = require("../content/AssetProductionManager");
// Assets dummy para teste
const DUMMY_ASSETS = {
    models: {
        cube: {
            vertices: new Float32Array([
                // Cubo simples com 8 vértices
                -1, -1, -1, 1, -1, -1, 1, 1, -1, -1, 1, -1,
                -1, -1, 1, 1, -1, 1, 1, 1, 1, -1, 1, 1
            ]),
            indices: new Uint16Array([
                0, 1, 2, 0, 2, 3, // frente
                1, 5, 6, 1, 6, 2, // direita
                5, 4, 7, 5, 7, 6, // trás
                4, 0, 3, 4, 3, 7, // esquerda
                3, 2, 6, 3, 6, 7, // topo
                4, 5, 1, 4, 1, 0 // base
            ])
        },
        building: {
            // Galpão retangular simplificado
            vertices: new Float32Array([
                // Base 10x20m
                -5, 0, -10, 5, 0, -10, 5, 0, 10, -5, 0, 10,
                // Topo
                -5, 8, -10, 5, 8, -10, 5, 8, 10, -5, 8, 10
            ]),
            indices: new Uint16Array([
                // Faces do galpão
                0, 1, 2, 0, 2, 3, // base
                4, 5, 6, 4, 6, 7, // topo
                0, 4, 7, 0, 7, 3, // esquerda
                1, 5, 6, 1, 6, 2, // direita
                0, 1, 5, 0, 5, 4, // frente
                3, 2, 6, 3, 6, 7 // trás
            ])
        }
    },
    textures: {
        checkerboard: {
            width: 512,
            height: 512,
            data: new Uint8Array(512 * 512 * 4) // RGBA
        }
    },
    sounds: {
        wind: 'assets/audio/ambient/wind.wav',
        cityAmbient: 'assets/audio/ambient/city_light.wav',
        music: 'assets/audio/music/background_loop.wav'
    }
};
/**
 * Cenário de teste para o pipeline de produção do mapa
 */
class MapProductionTest {
    constructor() {
        this.geometryOptimizer = new GeometryOptimizer_1.GeometryOptimizer();
        this.textureOptimizer = new TextureOptimizer_1.TextureOptimizer();
        this.assetManager = new AssetProductionManager_1.AssetProductionManager();
    }
    async runTest() {
        console.log('Iniciando teste do pipeline de produção do mapa...');
        try {
            // Primeiro otimiza os assets dummy
            const assetResults = await this.optimizeTestAssets();
            // Teste de cena e overdraw
            const sceneResults = await this.testSceneOptimization();
            const totalTime = this.printResults(assetResults, sceneResults);
            return {
                success: true,
                executionTime: totalTime,
                assetResults,
                sceneResults
            };
        }
        catch (error) {
            console.error('Erro durante o teste:', error instanceof Error ? error.message : 'Erro desconhecido');
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Erro desconhecido'
            };
        }
    }
    async optimizeTestAssets() {
        console.log('\nOtimizando assets dummy...');
        // Otimiza geometria do cubo
        const cubeResult = await this.geometryOptimizer.optimizeGeometry(DUMMY_ASSETS.models.cube.vertices, DUMMY_ASSETS.models.cube.indices, {
            quantization: {
                position: 16,
                normal: 8,
                uv: 12
            },
            simplification: {
                targetError: 0.01,
                maxDeviation: 0.5
            }
        });
        // Otimiza geometria do prédio
        const buildingResult = await this.geometryOptimizer.optimizeGeometry(DUMMY_ASSETS.models.building.vertices, DUMMY_ASSETS.models.building.indices, {
            quantization: {
                position: 16,
                normal: 8,
                uv: 12
            },
            simplification: {
                targetError: 0.01,
                maxDeviation: 0.5
            }
        });
        // Otimiza textura de teste
        const textureResult = await this.textureOptimizer.optimizeTexture(DUMMY_ASSETS.textures.checkerboard.data, DUMMY_ASSETS.textures.checkerboard.width, DUMMY_ASSETS.textures.checkerboard.height, {
            format: 'UASTC',
            quality: 128,
            generateMipmaps: true
        });
        console.log('\nRelatório de otimização de assets:');
        console.log('Geometria - Cubo:');
        console.log(`- Taxa de compressão: ${cubeResult.metrics.compressionRatio.toFixed(2)}x`);
        console.log(`- Redução de triângulos: ${(cubeResult.metrics.triangleReduction * 100).toFixed(2)}%`);
        console.log('\nGeometria - Prédio:');
        console.log(`- Taxa de compressão: ${buildingResult.metrics.compressionRatio.toFixed(2)}x`);
        console.log(`- Redução de triângulos: ${(buildingResult.metrics.triangleReduction * 100).toFixed(2)}%`);
        console.log('\nTextura - Checkerboard:');
        console.log(`- Taxa de compressão: ${textureResult.metrics.compressionRatio.toFixed(2)}x`);
        console.log(`- PSNR: ${textureResult.metrics.psnr?.toFixed(2) || 'N/A'} dB`);
        // Inicializa o produtor do mapa
        const mapProducer = new InitialMapProducer_1.InitialMapProducer({
            audio: { sampleRate: 48000 }
        }, this.onProgressUpdate.bind(this));
        // Define região de teste (500x500m)
        const testRegion = {
            type: 'urban',
            center: { x: 250, y: 0, z: 250 },
            radius: 250,
            poiCount: 1
        };
        console.log('\nGerando terreno base...');
        const startTime = performance.now();
        // Gera o terreno base
        await mapProducer.generateBaseTerrain([testRegion]);
        // Cria POI com geometria e texturas otimizadas
        const poiPosition = { x: 250, y: 0, z: 250 };
        const poiTemplate = mapProducer.createPOITemplate('urban');
        console.log('\nGerando POI de teste com assets otimizados...');
        const poi = await mapProducer.poiGenerator.generatePOI('urban', poiPosition, poiTemplate);
        // Configura áudio ambiental
        console.log('\nConfigurando áudio ambiental...');
        await mapProducer.setupEnvironmentalAudio([testRegion]);
        // Executa otimizações finais
        console.log('\nExecutando otimizações finais...');
        await mapProducer.optimizeAndValidate();
        const endTime = performance.now();
        const totalTime = (endTime - startTime) / 1000;
        // Relatório final
        console.log('\n=================================');
        console.log('Teste concluído com sucesso!');
        console.log('=================================');
        console.log(`Tempo total: ${totalTime.toFixed(2)}s`);
        console.log('\nMétricas de otimização:');
        console.log(this.geometryOptimizer.getMetricsReport());
        console.log(this.textureOptimizer.getMetricsReport());
        console.log('\nMétricas de assets:');
        console.log(this.assetManager.getProcessingReport());
        return {
            geometryMetrics: this.geometryOptimizer.getMetricsReport(),
            textureMetrics: this.textureOptimizer.getMetricsReport(),
            assetMetrics: this.assetManager.getProcessingReport()
        };
    }
    async testSceneOptimization() {
        console.log('\nTestando otimização de overdraw...');
        // Cria uma cena de teste com objetos sobrepostos
        const testScene = {
            camera: {
                position: { x: 0, y: 10, z: 0 },
                frustum: {
                    planes: [
                        { normal: { x: 0, y: 0, z: 1 }, distance: 10 },
                        { normal: { x: 0, y: 0, z: -1 }, distance: 100 },
                        { normal: { x: 1, y: 0, z: 0 }, distance: 10 },
                        { normal: { x: -1, y: 0, z: 0 }, distance: 10 },
                        { normal: { x: 0, y: 1, z: 0 }, distance: 10 },
                        { normal: { x: 0, y: -1, z: 0 }, distance: 10 }
                    ]
                }
            },
            objects: [
                // Objetos opacos
                {
                    position: { x: 0, y: 0, z: 10 },
                    material: { transparent: false },
                    boundingBox: {
                        min: { x: -1, y: -1, z: -1 },
                        max: { x: 1, y: 1, z: 1 }
                    }
                },
                // Objetos transparentes
                {
                    position: { x: 0, y: 0, z: 15 },
                    material: { transparent: true },
                    boundingBox: {
                        min: { x: -2, y: -2, z: -2 },
                        max: { x: 2, y: 2, z: 2 }
                    }
                }
            ]
        };
        const sceneResult = await this.assetManager.optimizeScene(testScene);
        console.log('\nRelatório de otimização de overdraw:');
        console.log(`- Objetos visíveis: ${sceneResult.objectCount}`);
        console.log(`- Média de overdraw: ${sceneResult.metrics.averageOverdraw.toFixed(2)}`);
        console.log(`- Objetos opacos: ${sceneResult.metrics.opaqueObjectCount}`);
        console.log(`- Objetos transparentes: ${sceneResult.metrics.transparentObjectCount}`);
        // Valida os resultados de overdraw
        const overdrawThreshold = 2.0;
        if (sceneResult.metrics.averageOverdraw > overdrawThreshold) {
            throw new Error(`Overdraw muito alto: ${sceneResult.metrics.averageOverdraw.toFixed(2)} (máximo: ${overdrawThreshold})`);
        }
        return sceneResult;
    }
    printResults(assetResults, sceneResults) {
        const startTime = performance.now();
        console.log('\n=================================');
        console.log('Teste concluído com sucesso!');
        console.log('=================================');
        // Resultados de otimização de assets
        console.log('\nMétricas de otimização de assets:');
        console.log(assetResults.geometryMetrics);
        // Resultados de overdraw
        console.log('\nMétricas de overdraw:');
        console.log(`- Objetos visíveis: ${sceneResults.objectCount}`);
        console.log(`- Média de overdraw: ${sceneResults.metrics.averageOverdraw.toFixed(2)}`);
        console.log(`- Objetos opacos: ${sceneResults.metrics.opaqueObjectCount}`);
        console.log(`- Objetos transparentes: ${sceneResults.metrics.transparentObjectCount}`);
        const endTime = performance.now();
        const totalTime = (endTime - startTime) / 1000;
        console.log(`\nTempo total: ${totalTime.toFixed(2)}s`);
        return totalTime;
    }
    onProgressUpdate(progress) {
        console.log(`[${progress.currentPhase}] Progresso:`, {
            terreno: `${progress.terrainProgress}%`,
            POIs: progress.poisCompleted >= 0 ?
                `${progress.poisCompleted}/${progress.totalPOIs}` : 'N/A'
        });
    }
}
exports.MapProductionTest = MapProductionTest;
// Executa o teste se o arquivo for executado diretamente
if (require.main === module) {
    const test = new MapProductionTest();
    test.runTest().then(result => {
        if (!result.success) {
            process.exit(1);
        }
    });
}
//# sourceMappingURL=MapProductionTest.js.map