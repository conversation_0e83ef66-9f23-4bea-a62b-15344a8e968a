"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GPUParticleSystem = void 0;
// Enums para configuração
var BlendMode;
(function (BlendMode) {
    BlendMode[BlendMode["ADDITIVE"] = 0] = "ADDITIVE";
    BlendMode[BlendMode["ALPHA"] = 1] = "ALPHA";
    BlendMode[BlendMode["MULTIPLY"] = 2] = "MULTIPLY";
})(BlendMode || (BlendMode = {}));
class GPUParticleSystem {
    constructor(device) {
        this.MAX_PARTICLES = 10000;
        this.WORKGROUP_SIZE = 256;
        this.dynamicLights = [];
        this.activeParticles = 0;
        this.device = device;
        this.initializeBuffers();
        this.createPipelines();
        this.loadShaders();
        this.particleSystems = new Map();
        this.activeParticles = 0;
        this.recycleQueue = [];
        this.initializeGPUBuffers();
    }
    initializeBuffers() {
        // Buffer para posições e velocidades das partículas
        this.particleBuffer = this.device.createBuffer({
            size: this.MAX_PARTICLES * 32, // 8 floats por partícula (posição + velocidade)
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.VERTEX,
            mappedAtCreation: true
        });
    }
    createPipelines() {
        // Pipeline de computação para simulação de partículas
        this.computePipeline = this.device.createComputePipeline({
            layout: 'auto',
            compute: {
                module: this.device.createShaderModule({
                    code: this.getComputeShader()
                }),
                entryPoint: 'main'
            }
        });
        // Pipeline de renderização para partículas
        this.renderPipeline = this.device.createRenderPipeline({
            layout: 'auto',
            vertex: {
                module: this.device.createShaderModule({
                    code: this.getVertexShader()
                }),
                entryPoint: 'main'
            },
            fragment: {
                module: this.device.createShaderModule({
                    code: this.getFragmentShader()
                }),
                entryPoint: 'main',
                targets: [{ format: 'bgra8unorm' }]
            }
        });
    }
    initializeGPUBuffers() {
        // TODO: Inicializar buffers na GPU
        // - Posição
        // - Velocidade
        // - Cor
        // - Tamanho
        // - Tempo de vida
        // - Matriz de transformação
    }
    createParticleSystem(name, config) {
        if (this.particleSystems.size >= GPUParticleSystem.MAX_SYSTEMS) {
            console.warn('Limite máximo de sistemas de partículas atingido');
            return false;
        }
        // Valida e ajusta configurações para performance
        const optimizedConfig = this.optimizeConfig(config);
        this.particleSystems.set(name, optimizedConfig);
        return true;
    }
    optimizeConfig(config) {
        return {
            ...config,
            maxParticles: Math.min(config.maxParticles, GPUParticleSystem.PARTICLES_PER_SYSTEM),
            spawnRate: Math.min(config.spawnRate, config.maxParticles * 2), // Limita taxa de spawn
            particleLifetime: Math.min(config.particleLifetime, 5000) // Máximo 5 segundos
        };
    }
    emitParticles(systemName, position, count) {
        const system = this.particleSystems.get(systemName);
        if (!system)
            return;
        // Verifica limite global de partículas
        const availableSlots = GPUParticleSystem.MAX_TOTAL_PARTICLES - this.activeParticles;
        const actualCount = Math.min(count, availableSlots);
        if (actualCount <= 0) {
            this.recycleOldestParticles(count);
            return;
        }
        // Emite partículas na GPU
        this.spawnParticlesGPU(system, position, actualCount);
        this.activeParticles += actualCount;
    }
    getActiveParticleCount() {
        return this.activeParticles;
    }
    emitBurst(config) {
        const commandEncoder = this.device.createCommandEncoder();
        const computePass = commandEncoder.beginComputePass();
        // Configura o shader e parâmetros
        computePass.setPipeline(this.computePipeline);
        computePass.setBindGroup(0, this.createBindGroup(config));
        // Dispara partículas em grupos de trabalho otimizados
        const workgroups = Math.ceil(config.count / this.WORKGROUP_SIZE);
        computePass.dispatchWorkgroups(workgroups, 1, 1);
        computePass.end();
        this.device.queue.submit([commandEncoder.finish()]);
        // Atualiza contagem de partículas ativas
        this.activeParticles += config.count;
        setTimeout(() => {
            this.activeParticles = Math.max(0, this.activeParticles - config.count);
        }, config.lifetime * 1000);
    }
    createBindGroup(config) {
        return this.device.createBindGroup({
            layout: this.computePipeline.getBindGroupLayout(0),
            entries: [
                {
                    binding: 0,
                    resource: { buffer: this.particleBuffer }
                },
                {
                    binding: 1,
                    resource: {
                        buffer: this.createUniformBuffer(config)
                    }
                }
            ]
        });
    }
    createUniformBuffer(config) {
        const buffer = this.device.createBuffer({
            size: 32, // posição (12) + direção (12) + tamanho (4) + tempo de vida (4)
            usage: GPUBufferUsage.UNIFORM | GPUBufferUsage.COPY_DST,
            mappedAtCreation: true
        });
        const data = new Float32Array(buffer.getMappedRange());
        data.set([
            config.position.x, config.position.y, config.position.z,
            config.direction.x, config.direction.y, config.direction.z,
            config.size,
            config.lifetime
        ]);
        buffer.unmap();
        return buffer;
    }
    recycleOldestParticles(count) {
        // Recicla partículas antigas para manter a performance
        const recycleCount = Math.min(count, this.recycleQueue.length);
        this.recycleQueue.splice(0, recycleCount);
        this.activeParticles -= recycleCount;
    }
    spawnParticlesGPU(config, position, count) {
        // TODO: Implementar spawn de partículas diretamente na GPU
        // - Usar compute shader para geração de partículas
        // - Atualizar buffers de forma eficiente
        // - Aplicar randomização quando necessário
    }
    update(deltaTime) {
        // Atualização é feita inteiramente na GPU via compute shader
        this.updateParticlesGPU(deltaTime);
        this.cullDeadParticles();
    }
    updateParticlesGPU(deltaTime) {
        // TODO: Implementar atualização GPU
        // - Posição baseada em velocidade
        // - Velocidade baseada em aceleração
        // - Atualização de cor e tamanho
        // - Tempo de vida
    }
    cullDeadParticles() {
        // TODO: Implementar culling de partículas mortas
        // - Remover partículas expiradas
        // - Atualizar contadores
        // - Reciclar slots de memória
    }
    addDynamicLight(light) {
        this.dynamicLights.push(light);
        // Remove luzes expiradas
        this.dynamicLights = this.dynamicLights.filter(l => l.duration > 0);
    }
    loadShaders() {
        this.shaderModules = new Map();
        // Carrega shaders específicos para cada tipo de efeito
        const shaderTypes = ['muzzleFlash', 'spark', 'dust', 'woodChip', 'waterDrop', 'glassShards', 'dirt', 'default', 'blood'];
        for (const type of shaderTypes) {
            this.shaderModules.set(type, this.device.createShaderModule({
                code: this.getShaderCode(type)
            }));
        }
    }
    getComputeShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> params: EmitParams;

            struct Particle {
                position: vec4<f32>,
                velocity: vec4<f32>
            };

            struct EmitParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                size: f32,
                lifetime: f32
            };

            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }

                // Lógica de simulação de partículas otimizada
                var particle = particles[index];
                
                // Atualiza posição e velocidade
                particle.position += particle.velocity * params.lifetime;
                particle.velocity += vec4<f32>(0.0, -9.8, 0.0, 0.0) * params.lifetime;
                
                particles[index] = particle;
            }
        `;
    }
    getVertexShader() {
        return `
            struct VertexOutput {
                @builtin(position) position: vec4<f32>,
                @location(0) color: vec4<f32>
            };

            @vertex
            fn main(@location(0) position: vec3<f32>,
                   @location(1) color: vec4<f32>) -> VertexOutput {
                var output: VertexOutput;
                output.position = vec4<f32>(position, 1.0);
                output.color = color;
                return output;
            }
        `;
    }
    getFragmentShader() {
        return `
            @fragment
            fn main(@location(0) color: vec4<f32>) -> @location(0) vec4<f32> {
                return color;
            }
        `;
    }
    getShaderCode(type) {
        switch (type) {
            case 'muzzleFlash':
                return this.getMuzzleFlashShader();
            case 'spark':
                return this.getSparkShader();
            case 'dust':
                return this.getDustShader();
            case 'woodChip':
                return this.getWoodChipShader();
            case 'waterDrop':
                return this.getWaterDropShader();
            case 'glassShards':
                return this.getGlassShardShader();
            case 'dirt':
                return this.getDirtShader();
            case 'blood':
                return this.getBloodShader();
            default:
                return this.getDefaultShader();
        }
    }
    getMuzzleFlashShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> muzzleParams: MuzzleFlashParams;
            
            struct MuzzleFlashParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                color: vec4<f32>,
                intensity: f32,
                time: f32
            };
            
            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }
                
                var particle = particles[index];
                
                // Calcula posição volumétrica do flash
                let angle = f32(index) * 6.28318 / 64.0;
                let radius = muzzleParams.intensity * (1.0 - particle.position.w / muzzleParams.time);
                
                particle.position.xyz = muzzleParams.position + 
                    vec3<f32>(cos(angle) * radius, sin(angle) * radius, 0.0) * 
                    mat3x3<f32>(muzzleParams.direction, cross(muzzleParams.direction, vec3<f32>(0.0, 1.0, 0.0)), vec3<f32>(0.0, 1.0, 0.0));
                
                // Aplica cor com falloff baseado no tempo de vida
                particle.color = vec4<f32>(
                    muzzleParams.color.rgb * exp(-particle.position.w * 5.0),
                    muzzleParams.color.a * (1.0 - particle.position.w / muzzleParams.time)
                );
                
                particles[index] = particle;
            }
        `;
    }
    getSparkShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> sparkParams: SparkParams;
            
            struct SparkParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                velocity: f32,
                lifetime: f32,
                time: f32
            };
            
            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }
                
                var particle = particles[index];
                
                // Física simplificada para sparks
                particle.velocity.xyz += vec3<f32>(0.0, -9.8, 0.0) * 0.016;
                particle.position.xyz += particle.velocity.xyz * 0.016;
                
                // Trail com fade out
                let trailIntensity = exp(-particle.position.w * 3.0);
                particle.color = vec4<f32>(1.0, 0.7, 0.3, trailIntensity);
                
                // Bloom effect
                let bloomSize = particle.position.w * 0.5;
                particle.size = max(0.05, bloomSize);
                
                particles[index] = particle;
            }
        `;
    }
    getDustShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> dustParams: DustParams;
            
            struct DustParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                turbulence: f32,
                lifetime: f32
            };
            
            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }
                
                var particle = particles[index];
                
                // Movimento turbulento
                let time = dustParams.lifetime - particle.position.w;
                particle.position.xyz += vec3<f32>(
                    sin(time * 2.0 + f32(index)) * dustParams.turbulence,
                    cos(time * 1.5 + f32(index)) * dustParams.turbulence,
                    sin(time * 1.8 + f32(index)) * dustParams.turbulence
                );
                
                // Fade out suave
                particle.color.a *= 0.98;
                
                particles[index] = particle;
            }
        `;
    }
    getWoodChipShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> woodParams: WoodParams;
            
            struct WoodParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                angularSpeed: f32,
                size: f32
            };
            
            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }
                
                var particle = particles[index];
                
                // Rotação realista
                let angle = woodParams.angularSpeed * particle.position.w;
                let rotation = mat3x3<f32>(
                    cos(angle), -sin(angle), 0.0,
                    sin(angle), cos(angle), 0.0,
                    0.0, 0.0, 1.0
                );
                
                particle.position.xyz = rotation * particle.position.xyz;
                
                // Aplica gravidade
                particle.velocity.y -= 9.8 * 0.016;
                particle.position.xyz += particle.velocity.xyz * 0.016;
                
                particles[index] = particle;
            }
        `;
    }
    getWaterDropShader() {
        return `// Shader para gotas d'água com refração`;
    }
    getGlassShardShader() {
        return `// Shader para fragmentos de vidro com reflexão`;
    }
    getDirtShader() {
        return `// Shader para partículas de terra com interação de luz`;
    }
    getDefaultShader() {
        return `// Shader padrão para partículas genéricas`;
    }
    getBloodShader() {
        return `
            @group(0) @binding(0) var<storage, read_write> particles: array<Particle>;
            @group(0) @binding(1) var<uniform> bloodParams: BloodParams;
            
            struct BloodParams {
                position: vec3<f32>,
                direction: vec3<f32>,
                velocity: f32,
                gravity: f32,
                viscosity: f32,
                lifetime: f32
            };
            
            @compute @workgroup_size(256)
            fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
                let index = global_id.x;
                if (index >= arrayLength(&particles)) {
                    return;
                }
                
                var particle = particles[index];
                
                // Física de fluido simplificada
                var velocity = particle.velocity.xyz;
                
                // Aplica gravidade
                velocity.y -= bloodParams.gravity * 0.016;
                
                // Aplica viscosidade do sangue
                velocity *= (1.0 - bloodParams.viscosity * 0.016);
                
                // Atualiza posição
                particle.position.xyz += velocity * 0.016;
                
                // Deformação da gota
                let speed = length(velocity);
                particle.scale = vec2<f32>(
                    mix(1.0, 1.5, min(speed * 0.1, 1.0)),
                    mix(1.0, 0.7, min(speed * 0.1, 1.0))
                );
                
                // Rotação baseada na velocidade
                particle.rotation = atan2(velocity.y, velocity.x);
                
                // Fade out suave
                let fadeStart = bloodParams.lifetime * 0.7;
                if (particle.age > fadeStart) {
                    let fade = 1.0 - (particle.age - fadeStart) / (bloodParams.lifetime - fadeStart);
                    particle.color.a *= fade;
                }
                
                // Variação de cor baseada na velocidade
                let darkening = mix(1.0, 0.7, min(speed * 0.05, 1.0));
                particle.color = vec4<f32>(
                    particle.color.r * darkening,
                    particle.color.g * darkening,
                    particle.color.b * darkening,
                    particle.color.a
                );
                
                particles[index] = particle;
            }
        `;
    }
}
exports.GPUParticleSystem = GPUParticleSystem;
GPUParticleSystem.MAX_SYSTEMS = 64; // Máximo de sistemas simultâneos
GPUParticleSystem.PARTICLES_PER_SYSTEM = 1024; // Máximo por sistema
GPUParticleSystem.MAX_TOTAL_PARTICLES = GPUParticleSystem.MAX_SYSTEMS * GPUParticleSystem.PARTICLES_PER_SYSTEM;
//# sourceMappingURL=GPUParticleSystem.js.map