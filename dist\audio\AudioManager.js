"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioManager = void 0;
class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.audioBuffers = new Map();
        this.reverbImpulses = new Map();
        this.currentReverb = null;
        this.maxChannels = 128;
        this.activeChannels = 0;
        this.context = new AudioContext();
        this.masterGain = this.context.createGain();
        this.convolver = this.context.createConvolver();
        this.masterGain.connect(this.context.destination);
        this.convolver.connect(this.masterGain);
        this.loadReverbImpulses();
    }
    async loadReverbImpulses() {
        const impulseTypes = ['small_room', 'medium_room', 'large_room', 'hall', 'tunnel'];
        for (const type of impulseTypes) {
            try {
                const response = await fetch(`/assets/audio/impulses/${type}.wav`);
                const arrayBuffer = await response.arrayBuffer();
                const audioBuffer = await this.context.decodeAudioData(arrayBuffer);
                this.reverbImpulses.set(type, audioBuffer);
            }
            catch (error) {
                console.error(`Erro ao carregar impulso de reverberação ${type}:`, error);
            }
        }
    }
    async loadSound(id, url) {
        try {
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.context.decodeAudioData(arrayBuffer);
            this.audioBuffers.set(id, audioBuffer);
        }
        catch (error) {
            console.error(`Erro ao carregar som ${id}:`, error);
            throw error;
        }
    }
    async playSound(options) {
        if (this.activeChannels >= this.maxChannels) {
            console.warn('Limite máximo de canais de áudio atingido');
            return;
        }
        const buffer = this.audioBuffers.get(options.id);
        if (!buffer) {
            console.error(`Buffer de áudio não encontrado para ${options.id}`);
            return;
        }
        try {
            const source = this.context.createBufferSource();
            source.buffer = buffer;
            source.loop = options.loop;
            const gainNode = this.context.createGain();
            gainNode.gain.value = options.volume;
            let nodes = { source, gain: gainNode };
            if (options.spatial && options.position) {
                const panner = this.context.createPanner();
                panner.setPosition(options.position.x, options.position.y, options.position.z);
                source.connect(panner);
                panner.connect(gainNode);
                nodes.panner = panner;
            }
            else {
                source.connect(gainNode);
            }
            gainNode.connect(this.masterGain);
            source.onended = () => {
                this.stopSound(options.id);
            };
            source.start();
            this.sounds.set(options.id, nodes);
            this.activeChannels++;
        }
        catch (error) {
            console.error(`Erro ao reproduzir som ${options.id}:`, error);
            throw error;
        }
    }
    stopSound(id) {
        const nodes = this.sounds.get(id);
        if (nodes) {
            try {
                nodes.source.stop();
                nodes.gain.disconnect();
                if (nodes.panner) {
                    nodes.panner.disconnect();
                }
                nodes.source.disconnect();
                this.sounds.delete(id);
                this.activeChannels--;
            }
            catch (error) {
                console.error(`Erro ao parar som ${id}:`, error);
            }
        }
    }
    setReverbParameters(params) {
        this.currentReverb = params;
        this.updateReverb();
    }
    updateReverb() {
        if (!this.currentReverb)
            return;
        const impulseType = this.selectImpulseType(this.currentReverb);
        const impulse = this.reverbImpulses.get(impulseType);
        if (impulse) {
            this.convolver.buffer = impulse;
            // Reconfigura a cadeia de efeitos
            this.convolver.disconnect();
            const reverbGain = this.context.createGain();
            reverbGain.gain.value = this.currentReverb.lateReflections;
            const delayNode = this.context.createDelay();
            delayNode.delayTime.value = this.currentReverb.preDelay / 1000;
            this.convolver.connect(delayNode);
            delayNode.connect(reverbGain);
            reverbGain.connect(this.masterGain);
        }
    }
    selectImpulseType(params) {
        if (params.roomSize < 0.3)
            return 'small_room';
        if (params.roomSize < 0.6)
            return 'medium_room';
        if (params.roomSize < 0.8)
            return 'large_room';
        return 'hall';
    }
    setListenerPosition(position, orientation) {
        const listener = this.context.listener;
        // Web Audio API moderno
        if ('positionX' in listener) {
            listener.positionX.value = position.x;
            listener.positionY.value = position.y;
            listener.positionZ.value = position.z;
            listener.forwardX.value = orientation.x;
            listener.forwardY.value = orientation.y;
            listener.forwardZ.value = orientation.z;
            listener.upX.value = 0;
            listener.upY.value = 1;
            listener.upZ.value = 0;
        }
    }
    getActiveChannels() {
        return this.activeChannels;
    }
    cleanup() {
        for (const [id, nodes] of this.sounds) {
            this.stopSound(id);
        }
        this.context.close();
        this.sounds.clear();
        this.audioBuffers.clear();
        this.reverbImpulses.clear();
        this.activeChannels = 0;
    }
}
exports.AudioManager = AudioManager;
//# sourceMappingURL=AudioManager.js.map