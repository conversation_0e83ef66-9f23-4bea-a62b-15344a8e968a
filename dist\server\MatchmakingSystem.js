"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MatchmakingSystem = void 0;
class MatchmakingSystem {
    constructor() {
        this.queuedPlayers = new Map();
        this.activeMatches = new Map();
        this.regions = new Set(['na-east', 'na-west', 'eu', 'sa', 'asia']);
        this.pendingReconnections = new Map();
    }
    queuePlayer(playerId, stats, region, squadId) {
        if (this.queuedPlayers.has(playerId))
            return false;
        const criteria = {
            minMmr: stats.mmr - MatchmakingSystem.MMR_RANGE_INCREMENT,
            maxMmr: stats.mmr + MatchmakingSystem.MMR_RANGE_INCREMENT,
            maxPing: MatchmakingSystem.MAX_PING,
            region,
            gameMode: 'standard',
            squadSize: squadId ? this.getSquadSize(squadId) : 1
        };
        this.queuedPlayers.set(playerId, {
            playerId,
            stats,
            criteria,
            squadId,
            queueTime: Date.now()
        });
        return true;
    }
    update() {
        this.expandSearchCriteria();
        this.matchPlayers();
        this.cleanupReconnections();
    }
    expandSearchCriteria() {
        const now = Date.now();
        for (const [_, player] of this.queuedPlayers) {
            const timeInQueue = now - player.queueTime;
            const expansions = Math.floor(timeInQueue / 10000); // Expande a cada 10 segundos
            if (expansions > 0) {
                const mmrExpansion = Math.min(expansions * MatchmakingSystem.MMR_RANGE_INCREMENT, MatchmakingSystem.MAX_MMR_RANGE);
                player.criteria.minMmr = player.stats.mmr - mmrExpansion;
                player.criteria.maxMmr = player.stats.mmr + mmrExpansion;
                player.criteria.maxPing = Math.min(MatchmakingSystem.MAX_PING + (expansions * 10), 200);
            }
        }
    }
    matchPlayers() {
        const playerGroups = this.groupPlayersByRegion();
        for (const [region, players] of playerGroups) {
            while (players.length >= MatchmakingSystem.MATCH_SIZE) {
                const match = this.createMatch(players.splice(0, MatchmakingSystem.MATCH_SIZE), region);
                if (match) {
                    this.activeMatches.set(match.id, match);
                    this.removePlayersFromQueue(match.players);
                }
            }
        }
    }
    groupPlayersByRegion() {
        const groups = new Map();
        for (const [playerId, player] of this.queuedPlayers) {
            const region = player.criteria.region;
            if (!groups.has(region)) {
                groups.set(region, []);
            }
            groups.get(region).push(playerId);
        }
        return groups;
    }
    createMatch(playerIds, region) {
        const matchId = `match_${Date.now()}_${region}`;
        const players = playerIds.map(id => this.queuedPlayers.get(id));
        // Calcula MMR médio
        const totalMmr = players.reduce((sum, p) => sum + p.stats.mmr, 0);
        const averageMmr = totalMmr / players.length;
        return {
            id: matchId,
            players: playerIds,
            averageMmr,
            region,
            startTime: Date.now()
        };
    }
    removePlayersFromQueue(playerIds) {
        playerIds.forEach(id => this.queuedPlayers.delete(id));
    }
    registerDisconnect(playerId, matchId) {
        const expiryTime = Date.now() + (5 * 60 * 1000); // 5 minutos para reconectar
        this.pendingReconnections.set(playerId, { matchId, expiryTime });
    }
    attemptReconnect(playerId) {
        const reconnection = this.pendingReconnections.get(playerId);
        if (!reconnection)
            return null;
        if (Date.now() > reconnection.expiryTime) {
            this.pendingReconnections.delete(playerId);
            return null;
        }
        const match = this.activeMatches.get(reconnection.matchId);
        if (!match) {
            this.pendingReconnections.delete(playerId);
            return null;
        }
        return reconnection.matchId;
    }
    cleanupReconnections() {
        const now = Date.now();
        for (const [playerId, reconnection] of this.pendingReconnections) {
            if (now > reconnection.expiryTime) {
                this.pendingReconnections.delete(playerId);
            }
        }
    }
    getSquadSize(squadId) {
        // Implementação real contaria membros do squad
        return 4;
    }
    getQueueStats() {
        return {
            totalPlayers: this.queuedPlayers.size,
            activeMatches: this.activeMatches.size,
            pendingReconnections: this.pendingReconnections.size,
            regionStats: Array.from(this.regions).map(region => ({
                region,
                players: Array.from(this.queuedPlayers.values())
                    .filter(p => p.criteria.region === region).length
            }))
        };
    }
}
exports.MatchmakingSystem = MatchmakingSystem;
MatchmakingSystem.MMR_RANGE_INCREMENT = 50;
MatchmakingSystem.MAX_MMR_RANGE = 500;
MatchmakingSystem.MAX_PING = 100;
MatchmakingSystem.MATCH_SIZE = 50; // Jogadores por partida
MatchmakingSystem.MIN_SQUAD_SIZE = 1;
MatchmakingSystem.MAX_SQUAD_SIZE = 4;
//# sourceMappingURL=MatchmakingSystem.js.map