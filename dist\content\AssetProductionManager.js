"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetProductionManager = void 0;
const GeometryOptimizer_1 = require("../rendering/GeometryOptimizer");
const TextureOptimizer_1 = require("../rendering/TextureOptimizer");
const OptimizationMetrics_1 = require("../rendering/OptimizationMetrics");
const OverdrawOptimizer_1 = require("../rendering/OverdrawOptimizer");
const PerformanceMetrics_1 = require("../rendering/PerformanceMetrics");
class AssetProductionManager {
    constructor() {
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
        this.geometryOptimizer = new GeometryOptimizer_1.GeometryOptimizer();
        this.textureOptimizer = new TextureOptimizer_1.TextureOptimizer();
        this.metricsCollector = new OptimizationMetrics_1.OptimizationMetricsCollector();
        this.overdrawOptimizer = new OverdrawOptimizer_1.OverdrawOptimizer(this.metrics);
        this.processedAssets = new Map();
    }
    async processGeometry(id, vertices, indices, options = {}) {
        const operationId = `geometry-${id}`;
        this.metricsCollector.startOperation(operationId);
        try {
            const result = await this.geometryOptimizer.optimizeGeometry(vertices, indices, options);
            this.processedAssets.set(id, {
                id,
                type: 'geometry',
                originalSize: result.originalSize,
                optimizedSize: result.optimizedSize,
                processingTimeMs: result.metrics.processingTimeMs,
                metrics: result.metrics
            });
            return result;
        }
        catch (error) {
            console.error(`Erro ao processar geometria ${id}:`, error);
            throw error;
        }
    }
    async processTexture(id, textureData, width, height, options = {}) {
        const operationId = `texture-${id}`;
        this.metricsCollector.startOperation(operationId);
        try {
            const result = await this.textureOptimizer.optimizeTexture(textureData, width, height, options);
            this.processedAssets.set(id, {
                id,
                type: 'texture',
                originalSize: result.originalSize,
                optimizedSize: result.compressedSize,
                processingTimeMs: result.metrics.processingTimeMs,
                metrics: result.metrics
            });
            return result;
        }
        catch (error) {
            console.error(`Erro ao processar textura ${id}:`, error);
            throw error;
        }
    }
    async optimizeScene(scene) {
        // Atualiza posição da câmera e frustum
        this.overdrawOptimizer.updateCamera(scene.camera.position, scene.camera.frustum);
        // Registra objetos renderizáveis
        for (let i = 0; i < scene.objects.length; i++) {
            const object = scene.objects[i];
            this.overdrawOptimizer.addRenderableObject({
                id: `object_${i}`,
                position: object.position,
                isTransparent: object.material.transparent,
                boundingBox: object.boundingBox,
            });
        }
        // Otimiza ordem de renderização
        const optimizedOrder = this.overdrawOptimizer.optimizeRenderOrder();
        // Aplica occlusion culling
        const visibleObjects = this.overdrawOptimizer.optimizeOcclusion();
        // Aplica otimizações na cena
        scene.objects = visibleObjects;
        scene.renderOrder = optimizedOrder;
        const overdrawMetrics = this.metrics.getOverdrawMetrics();
        if (overdrawMetrics.averageOverdraw > 2.0) {
            console.warn('Alto nível de overdraw detectado:', overdrawMetrics.averageOverdraw.toFixed(2));
        }
        return {
            objectCount: visibleObjects.length,
            metrics: this.metrics.getOverdrawMetrics()
        };
    }
    getProcessingReport() {
        let report = 'Relatório de Processamento de Assets\n';
        report += '===================================\n\n';
        // Métricas de geometria e textura
        report += this.generateAssetMetricsReport();
        // Métricas de overdraw
        const overdrawMetrics = this.metrics.getOverdrawMetrics();
        report += '\nMétricas de Overdraw\n';
        report += '-----------------\n';
        report += `Média de overdraw: ${overdrawMetrics.averageOverdraw.toFixed(2)}\n`;
        report += `Pixels sobrepostos: ${overdrawMetrics.pixelsOverdrawn}\n`;
        report += `Overdraw máximo: ${overdrawMetrics.maxOverdraw.toFixed(2)}\n`;
        report += `Tempo de GPU: ${overdrawMetrics.gpuOverdrawTime.toFixed(2)}ms\n`;
        report += `Score de overdraw: ${(overdrawMetrics.averageOverdraw / 3.0 * 100).toFixed(1)}%\n`;
        return report;
    }
    generateAssetMetricsReport() {
        let report = '';
        let totalOriginalSize = 0;
        let totalOptimizedSize = 0;
        let totalProcessingTime = 0;
        for (const asset of this.processedAssets.values()) {
            report += `Asset: ${asset.id} (${asset.type})\n`;
            report += `---------------------------------\n`;
            report += `Tamanho original: ${(asset.originalSize / 1024).toFixed(2)}KB\n`;
            report += `Tamanho otimizado: ${(asset.optimizedSize / 1024).toFixed(2)}KB\n`;
            report += `Taxa de compressão: ${(asset.originalSize / asset.optimizedSize).toFixed(2)}x\n`;
            report += `Tempo de processamento: ${asset.processingTimeMs.toFixed(2)}ms\n\n`;
            totalOriginalSize += asset.originalSize;
            totalOptimizedSize += asset.optimizedSize;
            totalProcessingTime += asset.processingTimeMs;
        }
        report += 'Resumo\n';
        report += '======\n';
        report += `Total de assets: ${this.processedAssets.size}\n`;
        report += `Redução total: ${((1 - totalOptimizedSize / totalOriginalSize) * 100).toFixed(2)}%\n`;
        report += `Tempo total: ${(totalProcessingTime / 1000).toFixed(2)}s\n`;
        return report;
    }
    async processEnvironmentAsset(assetData) {
        // Processa assets de ambiente (buildings, props, etc.)
        const startTime = performance.now();
        try {
            let result;
            if (assetData.type === 'building' || assetData.type === 'prop') {
                // Simula processamento de geometria para buildings e props
                const vertices = new Float32Array(assetData.vertexCount || 1000);
                const indices = new Uint32Array(assetData.indexCount || 500);
                result = await this.processGeometry(assetData.id, vertices, indices, {
                    simplificationLevel: 0.8,
                    preserveUVs: true,
                    optimizeForGPU: true
                });
            }
            else if (assetData.type === 'texture') {
                // Simula processamento de textura
                const textureData = new Uint8Array(assetData.width * assetData.height * 4);
                result = await this.processTexture(assetData.id, textureData, assetData.width || 512, assetData.height || 512, {
                    format: 'BC7',
                    generateMipmaps: true,
                    quality: 'high'
                });
            }
            const processingTime = performance.now() - startTime;
            console.log(`Asset ${assetData.id} processado em ${processingTime.toFixed(2)}ms`);
            return result;
        }
        catch (error) {
            console.error(`Erro ao processar asset de ambiente ${assetData.id}:`, error);
            throw error;
        }
    }
}
exports.AssetProductionManager = AssetProductionManager;
//# sourceMappingURL=AssetProductionManager.js.map