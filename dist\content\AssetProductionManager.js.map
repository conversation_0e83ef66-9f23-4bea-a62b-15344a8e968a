{"version": 3, "file": "AssetProductionManager.js", "sourceRoot": "", "sources": ["../../src/content/AssetProductionManager.ts"], "names": [], "mappings": ";;;AAAA,sEAAmE;AACnE,oEAAiE;AACjE,0EAAgF;AAChF,sEAAmE;AACnE,wEAAqE;AAWrE,MAAa,sBAAsB;IAQ/B;QACI,IAAI,CAAC,OAAO,GAAG,uCAAkB,CAAC,WAAW,EAAE,CAAC;QAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,kDAA4B,EAAE,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,EAAU,EACV,QAAsB,EACtB,OAAkC,EAClC,UAAe,EAAE;QAEjB,MAAM,WAAW,GAAG,YAAY,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CACxD,QAAQ,EACR,OAAO,EACP,OAAO,CACV,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzB,EAAE;gBACF,IAAI,EAAE,UAAU;gBAChB,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,gBAAgB,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB;gBACjD,OAAO,EAAE,MAAM,CAAC,OAAO;aAC1B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAChB,EAAU,EACV,WAAqC,EACrC,KAAa,EACb,MAAc,EACd,UAAe,EAAE;QAEjB,MAAM,WAAW,GAAG,WAAW,EAAE,EAAE,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CACtD,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzB,EAAE;gBACF,IAAI,EAAE,SAAS;gBACf,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,aAAa,EAAE,MAAM,CAAC,cAAc;gBACpC,gBAAgB,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB;gBACjD,OAAO,EAAE,MAAM,CAAC,OAAO;aAC1B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAU;QAC1B,uCAAuC;QACvC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAC/B,KAAK,CAAC,MAAM,CAAC,QAAQ,EACrB,KAAK,CAAC,MAAM,CAAC,OAAO,CACvB,CAAC;QAEF,iCAAiC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;gBACvC,EAAE,EAAE,UAAU,CAAC,EAAE;gBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,WAAW;gBAC1C,WAAW,EAAE,MAAM,CAAC,WAAW;aAClC,CAAC,CAAC;QACP,CAAC;QAED,gCAAgC;QAChC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;QAEpE,2BAA2B;QAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;QAElE,6BAA6B;QAC7B,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;QAC/B,KAAK,CAAC,WAAW,GAAG,cAAc,CAAC;QAEnC,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC1D,IAAI,eAAe,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,mCAAmC,EAAE,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAClG,CAAC;QAED,OAAO;YACH,WAAW,EAAE,cAAc,CAAC,MAAM;YAClC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;SAC7C,CAAC;IACN,CAAC;IAED,mBAAmB;QACf,IAAI,MAAM,GAAG,wCAAwC,CAAC;QACtD,MAAM,IAAI,yCAAyC,CAAC;QAEpD,kCAAkC;QAClC,MAAM,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAE5C,uBAAuB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC1D,MAAM,IAAI,0BAA0B,CAAC;QACrC,MAAM,IAAI,qBAAqB,CAAC;QAChC,MAAM,IAAI,sBAAsB,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/E,MAAM,IAAI,uBAAuB,eAAe,CAAC,eAAe,IAAI,CAAC;QACrE,MAAM,IAAI,oBAAoB,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,MAAM,IAAI,iBAAiB,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5E,MAAM,IAAI,sBAAsB,CAAC,eAAe,CAAC,eAAe,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAE9F,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,0BAA0B;QAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAE5B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC;YACjD,MAAM,IAAI,qCAAqC,CAAC;YAChD,MAAM,IAAI,qBAAqB,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAC5E,MAAM,IAAI,sBAAsB,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAC9E,MAAM,IAAI,uBAAuB,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5F,MAAM,IAAI,2BAA2B,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;YAE/E,iBAAiB,IAAI,KAAK,CAAC,YAAY,CAAC;YACxC,kBAAkB,IAAI,KAAK,CAAC,aAAa,CAAC;YAC1C,mBAAmB,IAAI,KAAK,CAAC,gBAAgB,CAAC;QAClD,CAAC;QAED,MAAM,IAAI,UAAU,CAAC;QACrB,MAAM,IAAI,UAAU,CAAC;QACrB,MAAM,IAAI,oBAAoB,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC;QAC5D,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACjG,MAAM,IAAI,gBAAgB,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAEvE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,SAAc;QACxC,uDAAuD;QACvD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACD,IAAI,MAAM,CAAC;YAEX,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC7D,2DAA2D;gBAC3D,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC;gBACjE,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC;gBAE7D,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAC/B,SAAS,CAAC,EAAE,EACZ,QAAQ,EACR,OAAO,EACP;oBACI,mBAAmB,EAAE,GAAG;oBACxB,WAAW,EAAE,IAAI;oBACjB,cAAc,EAAE,IAAI;iBACvB,CACJ,CAAC;YACN,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACtC,kCAAkC;gBAClC,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE3E,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAC9B,SAAS,CAAC,EAAE,EACZ,WAAW,EACX,SAAS,CAAC,KAAK,IAAI,GAAG,EACtB,SAAS,CAAC,MAAM,IAAI,GAAG,EACvB;oBACI,MAAM,EAAE,KAAK;oBACb,eAAe,EAAE,IAAI;oBACrB,OAAO,EAAE,MAAM;iBAClB,CACJ,CAAC;YACN,CAAC;YAED,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,EAAE,kBAAkB,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAElF,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uCAAuC,SAAS,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;CACJ;AA7ND,wDA6NC"}