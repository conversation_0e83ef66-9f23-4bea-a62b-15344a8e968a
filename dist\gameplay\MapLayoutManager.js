"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapLayoutManager = void 0;
class MapLayoutManager {
    constructor() {
        this.MAP_SIZE = 2048; // 2km em metros
        this.HEIGHTMAP_RESOLUTION = 0.5; // 0.5m por pixel
        this.initializeHeightmap();
        this.regions = [];
        this.buyZones = [];
        this.defineMainRegions();
    }
    initializeHeightmap() {
        const pixelSize = this.MAP_SIZE / this.HEIGHTMAP_RESOLUTION;
        this.heightmap = {
            width: pixelSize,
            height: pixelSize,
            data: new Float32Array(pixelSize * pixelSize),
            resolution: this.HEIGHTMAP_RESOLUTION
        };
    }
    defineMainRegions() {
        // Define as principais regiões do mapa
        const mainRegions = [
            {
                id: 'central-urban',
                type: 'urban',
                center: { x: 1024, y: 0, z: 1024 },
                radius: 400,
                heightRange: { min: 0, max: 50 }
            },
            {
                id: 'military-base',
                type: 'military',
                center: { x: 512, y: 0, z: 512 },
                radius: 300,
                heightRange: { min: 5, max: 15 }
            },
            {
                id: 'industrial-district',
                type: 'industrial',
                center: { x: 1536, y: 0, z: 512 },
                radius: 350,
                heightRange: { min: 0, max: 30 }
            },
            {
                id: 'forest-reserve',
                type: 'forest',
                center: { x: 1024, y: 0, z: 1536 },
                radius: 450,
                heightRange: { min: 10, max: 100 }
            }
        ];
        this.regions = mainRegions;
        this.generateTerrainForRegions();
    }
    generateTerrainForRegions() {
        // Gera o terreno base para cada região
        this.regions.forEach(region => {
            this.generateRegionTerrain(region);
        });
        // Suaviza as transições entre regiões
        this.smoothTerrainTransitions();
    }
    generateRegionTerrain(region) {
        const { center, radius, heightRange } = region;
        // Converte coordenadas do mundo para índices do heightmap
        const centerX = Math.floor(center.x / this.HEIGHTMAP_RESOLUTION);
        const centerZ = Math.floor(center.z / this.HEIGHTMAP_RESOLUTION);
        const radiusPixels = Math.floor(radius / this.HEIGHTMAP_RESOLUTION);
        // Gera altura para cada ponto dentro da região
        for (let x = -radiusPixels; x <= radiusPixels; x++) {
            for (let z = -radiusPixels; z <= radiusPixels; z++) {
                const worldX = centerX + x;
                const worldZ = centerZ + z;
                if (worldX < 0 || worldX >= this.heightmap.width ||
                    worldZ < 0 || worldZ >= this.heightmap.height) {
                    continue;
                }
                const distance = Math.sqrt(x * x + z * z);
                if (distance <= radiusPixels) {
                    const heightFactor = 1 - (distance / radiusPixels);
                    const height = heightRange.min +
                        (heightRange.max - heightRange.min) *
                            heightFactor * Math.random();
                    const index = worldZ * this.heightmap.width + worldX;
                    this.heightmap.data[index] = height;
                }
            }
        }
    }
    smoothTerrainTransitions() {
        const smoothedData = new Float32Array(this.heightmap.data.length);
        const kernelSize = 5;
        const halfKernel = Math.floor(kernelSize / 2);
        for (let z = 0; z < this.heightmap.height; z++) {
            for (let x = 0; x < this.heightmap.width; x++) {
                let sum = 0;
                let count = 0;
                for (let kz = -halfKernel; kz <= halfKernel; kz++) {
                    for (let kx = -halfKernel; kx <= halfKernel; kx++) {
                        const sampleX = x + kx;
                        const sampleZ = z + kz;
                        if (sampleX >= 0 && sampleX < this.heightmap.width &&
                            sampleZ >= 0 && sampleZ < this.heightmap.height) {
                            const index = sampleZ * this.heightmap.width + sampleX;
                            sum += this.heightmap.data[index];
                            count++;
                        }
                    }
                }
                const index = z * this.heightmap.width + x;
                smoothedData[index] = sum / count;
            }
        }
        this.heightmap.data = smoothedData;
    }
    setupBuyZones() {
        // Define as Buy Zones estratégicas no mapa
        const buyZones = [
            {
                id: 'central-market',
                position: { x: 1024, y: 0, z: 1024 },
                radius: 50,
                lootTier: 3
            },
            {
                id: 'military-depot',
                position: { x: 512, y: 0, z: 512 },
                radius: 40,
                lootTier: 4
            },
            {
                id: 'industrial-warehouse',
                position: { x: 1536, y: 0, z: 512 },
                radius: 45,
                lootTier: 3
            }
        ];
        this.buyZones = buyZones;
    }
    getHeightAt(worldX, worldZ) {
        const x = Math.floor(worldX / this.HEIGHTMAP_RESOLUTION);
        const z = Math.floor(worldZ / this.HEIGHTMAP_RESOLUTION);
        if (x < 0 || x >= this.heightmap.width ||
            z < 0 || z >= this.heightmap.height) {
            return 0;
        }
        const index = z * this.heightmap.width + x;
        return this.heightmap.data[index];
    }
    getRegionAt(position) {
        return this.regions.find(region => {
            const dx = position.x - region.center.x;
            const dz = position.z - region.center.z;
            const distanceSquared = dx * dx + dz * dz;
            return distanceSquared <= region.radius * region.radius;
        }) || null;
    }
    getNearbyBuyZones(position, radius) {
        return this.buyZones.filter(zone => {
            const dx = position.x - zone.position.x;
            const dz = position.z - zone.position.z;
            const distanceSquared = dx * dx + dz * dz;
            const searchRadius = radius + zone.radius;
            return distanceSquared <= searchRadius * searchRadius;
        });
    }
    exportMapData() {
        return {
            heightmap: this.heightmap,
            regions: this.regions,
            buyZones: this.buyZones,
            mapSize: this.MAP_SIZE,
            resolution: this.HEIGHTMAP_RESOLUTION
        };
    }
}
exports.MapLayoutManager = MapLayoutManager;
//# sourceMappingURL=MapLayoutManager.js.map