{"version": 3, "file": "ReplayAnalysisWorker.js", "sourceRoot": "", "sources": ["../../src/server/ReplayAnalysisWorker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAAwD;AAExD,0DAA4C;AAS5C,MAAM,oBAAoB;IAItB;QAFQ,iBAAY,GAAY,KAAK,CAAC;QAGlC,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,2BAAU,CAAC,WAAW,CAAC,CAAC;IACpE,CAAC;IAEO,mBAAmB;QACvB,2BAAU,EAAE,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACxC,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,2BAAU,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC9B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAiB;QACxC,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAE7C,kBAAkB;QAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACvE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACxB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,uBAAuB;QACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAC9E,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;YAC7B,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,sBAAsB;QACtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACtF,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;YAC7B,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QACzF,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAC9B,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEO,eAAe,CAAC,KAAiB;QACrC,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;YACrD,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;YACrD,iBAAiB,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;SAC1D,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,KAAiB;QACxC,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,sBAAsB;QACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEnD,sBAAsB;QACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEpD,sBAAsB;QACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEpD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,KAAiB;QAC7C,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,qCAAqC;QACrC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEpD,+BAA+B;QAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEnD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,KAAiB;QAC7C,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,sCAAsC;QACtC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC,CAAC;QAExD,oCAAoC;QACpC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/D,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,wBAAwB,CAAC,KAAiB;QAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,6BAA6B;QAC7B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC,CAAC;QAE3D,mCAAmC;QACnC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC,CAAC;QAE7D,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,+CAA+C;IACvC,uBAAuB,CAAC,KAAiB;QAC7C,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,wBAAwB,CAAC,KAAiB;QAC9C,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,wBAAwB,CAAC,KAAiB;QAC9C,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,wBAAwB,CAAC,KAAiB;QAC9C,oDAAoD;QACpD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,uBAAuB,CAAC,KAAiB;QAC7C,gDAAgD;QAChD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,4BAA4B,CAAC,KAAiB;QAClD,wDAAwD;QACxD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,mCAAmC,CAAC,KAAiB;QACzD,iEAAiE;QACjE,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,+BAA+B,CAAC,KAAiB;QACrD,8DAA8D;QAC9D,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,iCAAiC,CAAC,KAAiB;QACvD,gEAAgE;QAChE,OAAO,CAAC,CAAC;IACb,CAAC;CACJ;AAED,sBAAsB;AACtB,IAAI,oBAAoB,EAAE,CAAC"}