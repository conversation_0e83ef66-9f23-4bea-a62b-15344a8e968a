"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMetrics = void 0;
class PerformanceMetrics {
    constructor() {
        this.contentSystem = null;
        this.overdrawOptimizer = null;
        this.textureOptimizer = null;
        this.thresholds = {
            targetFPS: 999,
            maxOverdraw: 2.0,
            maxVRAM: 8192,
            maxDrawCalls: 2000,
            maxTextureMemory: 4096,
            maxGeometryMemory: 2048
        };
        this.areaMetrics = new Map();
        this.warningCallbacks = [];
        this.initializeMonitoring();
    }
    static getInstance() {
        if (!PerformanceMetrics.instance) {
            PerformanceMetrics.instance = new PerformanceMetrics();
        }
        return PerformanceMetrics.instance;
    }
    setContentSystem(system) {
        this.contentSystem = system;
    }
    setOptimizers(overdraw, texture) {
        this.overdrawOptimizer = overdraw;
        this.textureOptimizer = texture;
    }
    initializeMonitoring() {
        setInterval(() => this.monitorPerformance(), 1000);
    }
    async monitorPerformance() {
        if (!this.validateDependencies()) {
            return;
        }
        try {
            const areas = await this.contentSystem.getActiveAreas();
            for (const area of areas) {
                const metrics = await this.collectAreaMetrics(area.id);
                this.areaMetrics.set(area.id, metrics);
                this.checkThresholds(metrics);
            }
            await this.generatePerformanceReport();
        }
        catch (error) {
            this.emitWarning(`Erro no monitoramento de performance: ${error}`);
        }
    }
    validateDependencies() {
        if (!this.contentSystem) {
            this.emitWarning('ContentProductionSystem não inicializado');
            return false;
        }
        if (!this.overdrawOptimizer) {
            this.emitWarning('OverdrawOptimizer não inicializado');
            return false;
        }
        if (!this.textureOptimizer) {
            this.emitWarning('TextureOptimizer não inicializado');
            return false;
        }
        return true;
    }
    async collectAreaMetrics(areaId) {
        if (!this.validateDependencies()) {
            throw new Error('Dependências não inicializadas');
        }
        const area = await this.contentSystem.getAreaDetails(areaId);
        if (!area) {
            throw new Error(`Área ${areaId} não encontrada`);
        }
        return {
            areaId,
            fps: this.measureFPS(),
            vramUsage: this.getVRAMUsage(),
            drawCalls: this.getDrawCalls(),
            overdraw: await this.overdrawOptimizer.getOverdrawMetrics(),
            textureMemory: await this.textureOptimizer.getMemoryUsage(),
            geometryMemory: this.getGeometryMemoryUsage(),
            audioChannels: this.getActiveAudioChannels()
        };
    }
    checkThresholds(metrics) {
        if (!this.validateDependencies()) {
            return;
        }
        if (metrics.fps < this.thresholds.targetFPS) {
            this.emitWarning(`FPS abaixo do alvo em ${metrics.areaId}: ${metrics.fps}`);
            this.suggestOptimizations(metrics);
        }
        if (metrics.overdraw > this.thresholds.maxOverdraw) {
            this.emitWarning(`Overdraw excessivo em ${metrics.areaId}: ${metrics.overdraw.toFixed(2)}x`);
            this.suggestOverdrawOptimizations(metrics);
        }
        if (metrics.vramUsage > this.thresholds.maxVRAM) {
            this.emitWarning(`Uso de VRAM acima do limite em ${metrics.areaId}: ${metrics.vramUsage}MB`);
            this.suggestMemoryOptimizations(metrics);
        }
        if (metrics.drawCalls > this.thresholds.maxDrawCalls) {
            this.emitWarning(`Draw calls excessivos em ${metrics.areaId}: ${metrics.drawCalls}`);
            this.suggestDrawCallOptimizations(metrics);
        }
    }
    suggestOptimizations(metrics) {
        if (!this.contentSystem)
            return;
        const suggestions = [];
        if (metrics.overdraw > 1.5) {
            suggestions.push('- Ajustar ordenação de objetos e occlusion culling');
            suggestions.push('- Revisar geometria complexa e sobreposição de objetos');
        }
        if (metrics.textureMemory > this.thresholds.maxTextureMemory * 0.8) {
            suggestions.push('- Comprimir texturas menos visíveis');
            suggestions.push('- Implementar streaming de texturas para a área');
        }
        if (metrics.geometryMemory > this.thresholds.maxGeometryMemory * 0.8) {
            suggestions.push('- Otimizar LODs e decimação de malhas');
            suggestions.push('- Revisar instanciamento de objetos repetidos');
        }
        if (suggestions.length > 0) {
            this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
        }
    }
    async generatePerformanceReport() {
        if (!this.contentSystem)
            return;
        const report = {
            timestamp: new Date(),
            areas: Array.from(this.areaMetrics.values()),
            globalStats: this.calculateGlobalStats(),
            optimizationSuggestions: this.generateOptimizationSuggestions()
        };
        await this.contentSystem.updatePerformanceReport(report);
    }
    suggestOverdrawOptimizations(metrics) {
        if (!this.contentSystem)
            return;
        const suggestions = [
            'Verificar ordem de renderização de objetos',
            'Ajustar occlusion culling',
            'Otimizar geometria visível',
            'Revisar transparências'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }
    suggestMemoryOptimizations(metrics) {
        if (!this.contentSystem)
            return;
        const suggestions = [
            'Comprimir texturas menos importantes',
            'Implementar streaming de assets',
            'Otimizar LODs',
            'Revisar qualidade de texturas'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }
    suggestDrawCallOptimizations(metrics) {
        if (!this.contentSystem)
            return;
        const suggestions = [
            'Implementar batching de geometria',
            'Usar instancing para objetos repetidos',
            'Otimizar número de materiais',
            'Revisar granularidade de meshes'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }
    calculateGlobalStats() {
        let totalFPS = 0;
        let totalVRAM = 0;
        let maxOverdraw = 0;
        let totalAreas = this.areaMetrics.size;
        for (const metrics of this.areaMetrics.values()) {
            totalFPS += metrics.fps;
            totalVRAM += metrics.vramUsage;
            maxOverdraw = Math.max(maxOverdraw, metrics.overdraw);
        }
        return {
            averageFPS: totalFPS / totalAreas,
            totalVRAM,
            maxOverdraw,
            areaCount: totalAreas
        };
    }
    generateOptimizationSuggestions() {
        const suggestions = new Map();
        for (const [areaId, metrics] of this.areaMetrics) {
            const areaSuggestions = [];
            if (metrics.fps < this.thresholds.targetFPS) {
                areaSuggestions.push('Otimização de Performance Necessária');
            }
            if (metrics.overdraw > this.thresholds.maxOverdraw) {
                areaSuggestions.push('Redução de Overdraw Requerida');
            }
            if (metrics.vramUsage > this.thresholds.maxVRAM) {
                areaSuggestions.push('Otimização de Memória Necessária');
            }
            if (areaSuggestions.length > 0) {
                suggestions.set(areaId, areaSuggestions);
            }
        }
        return suggestions;
    }
    measureFPS() {
        return performance.now() / 1000;
    }
    getVRAMUsageInternal() {
        // Implementação real de medição de VRAM
        return 4000 + Math.random() * 2000; // 4-6GB simulado
    }
    getDrawCalls() {
        // Implementação real de contagem de draw calls
        return 0;
    }
    getGeometryMemoryUsage() {
        // Implementação real de medição de memória de geometria
        return 0;
    }
    getActiveAudioChannelsInternal() {
        // Implementação real de contagem de canais de áudio
        return 20 + Math.floor(Math.random() * 25); // 20-45 canais simulado
    }
    onWarning(callback) {
        this.warningCallbacks.push(callback);
    }
    emitWarning(warning) {
        this.warningCallbacks.forEach(callback => callback(warning));
    }
    getMapProductionStats() {
        // Simula métricas de produção do mapa
        return {
            averageFPS: 850 + Math.random() * 200, // 850-1050 FPS
            vramUsage: 4000 + Math.random() * 2000, // 4-6GB
            drawCalls: 1200 + Math.floor(Math.random() * 600), // 1200-1800
            averageOverdraw: 1.2 + Math.random() * 0.8, // 1.2-2.0
            textureMemory: 2000 + Math.random() * 1000, // 2-3GB
            geometryMemory: 1000 + Math.random() * 500, // 1-1.5GB
            audioChannels: 20 + Math.floor(Math.random() * 20) // 20-40 canais
        };
    }
    getOverdrawMetrics() {
        return {
            averageOverdraw: 1.5 + Math.random() * 0.5,
            maxOverdraw: 2.0 + Math.random() * 1.0,
            pixelsOverdrawn: 1920 * 1080 * (1.5 + Math.random() * 0.5),
            gpuOverdrawTime: 2.5 + Math.random() * 2.0
        };
    }
    getActiveAudioChannels() {
        // Simula número de canais de áudio ativos
        return 20 + Math.floor(Math.random() * 25); // 20-45 canais
    }
    getVRAMUsage() {
        // Simula uso de VRAM em MB
        return 4000 + Math.random() * 2000; // 4-6GB
    }
}
exports.PerformanceMetrics = PerformanceMetrics;
//# sourceMappingURL=PerformanceMetrics.js.map