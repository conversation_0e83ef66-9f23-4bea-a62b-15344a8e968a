"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DetailedWeaponVFX = void 0;
const GPUParticleSystem_1 = require("./GPUParticleSystem");
const WeaponVFXSystem_1 = require("./WeaponVFXSystem");
class DetailedWeaponVFX {
    constructor(device) {
        this.particleSystem = new GPUParticleSystem_1.GPUParticleSystem(device);
        this.vfxSystem = new WeaponVFXSystem_1.WeaponVFXSystem(this.particleSystem);
        this.weaponProfiles = new Map();
        this.initializeWeaponProfiles();
    }
    initializeWeaponProfiles() {
        // Rifle híbrido AK-47/M4A1
        this.weaponProfiles.set('hybrid_rifle', {
            name: 'Hybrid Assault Rifle',
            config: {
                muzzleFlash: {
                    type: 'rifle',
                    scale: 1.2,
                    duration: 0.1,
                    color: { r: 1.0, g: 0.7, b: 0.3, a: 0.8 },
                    intensity: 2.0
                },
                shellEjection: {
                    model: 'models/fx/rifle_shell.glb',
                    force: 2.0,
                    spin: { x: 10, y: 5, z: 15 },
                    lifetime: 2.0
                },
                impacts: [
                    {
                        type: 'metal',
                        decal: 'textures/fx/impact_metal.ktx2',
                        particles: 'fx/metal_spark.json',
                        sound: 'sounds/impact_metal.ogg'
                    },
                    {
                        type: 'concrete',
                        decal: 'textures/fx/impact_concrete.ktx2',
                        particles: 'fx/concrete_dust.json',
                        sound: 'sounds/impact_concrete.ogg'
                    },
                    {
                        type: 'wood',
                        decal: 'textures/fx/impact_wood.ktx2',
                        particles: 'fx/wood_splinter.json',
                        sound: 'sounds/impact_wood.ogg'
                    },
                    {
                        type: 'flesh',
                        decal: 'textures/fx/blood_splatter.ktx2',
                        particles: 'fx/blood_spray.json',
                        sound: 'sounds/impact_flesh.ogg'
                    }
                ]
            },
            performance: {
                maxParticles: 100,
                maxDecals: 50,
                maxLights: 2
            }
        });
        // AWP Modernizada
        this.weaponProfiles.set('modern_awp', {
            name: 'Modern AWP',
            config: {
                muzzleFlash: {
                    type: 'sniper',
                    scale: 1.5,
                    duration: 0.15,
                    color: { r: 1.0, g: 0.8, b: 0.4, a: 0.9 },
                    intensity: 3.0
                },
                shellEjection: {
                    model: 'models/fx/sniper_shell.glb',
                    force: 1.5,
                    spin: { x: 8, y: 4, z: 12 },
                    lifetime: 2.5
                },
                impacts: [
                    {
                        type: 'metal',
                        decal: 'textures/fx/impact_metal_heavy.ktx2',
                        particles: 'fx/metal_spark_heavy.json',
                        sound: 'sounds/impact_metal_heavy.ogg'
                    },
                    {
                        type: 'concrete',
                        decal: 'textures/fx/impact_concrete_heavy.ktx2',
                        particles: 'fx/concrete_explosion.json',
                        sound: 'sounds/impact_concrete_heavy.ogg'
                    },
                    {
                        type: 'wood',
                        decal: 'textures/fx/impact_wood_heavy.ktx2',
                        particles: 'fx/wood_explosion.json',
                        sound: 'sounds/impact_wood_heavy.ogg'
                    },
                    {
                        type: 'flesh',
                        decal: 'textures/fx/blood_explosion.ktx2',
                        particles: 'fx/blood_heavy.json',
                        sound: 'sounds/impact_flesh_heavy.ogg'
                    }
                ]
            },
            performance: {
                maxParticles: 150,
                maxDecals: 75,
                maxLights: 3
            }
        });
        // Desert Eagle
        this.weaponProfiles.set('desert_eagle', {
            name: 'Desert Eagle',
            config: {
                muzzleFlash: {
                    type: 'pistol',
                    scale: 1.0,
                    duration: 0.08,
                    color: { r: 1.0, g: 0.6, b: 0.2, a: 0.7 },
                    intensity: 1.5
                },
                shellEjection: {
                    model: 'models/fx/pistol_shell.glb',
                    force: 1.8,
                    spin: { x: 12, y: 6, z: 18 },
                    lifetime: 1.5
                },
                impacts: [
                    {
                        type: 'metal',
                        decal: 'textures/fx/impact_metal_medium.ktx2',
                        particles: 'fx/metal_spark_medium.json',
                        sound: 'sounds/impact_metal_medium.ogg'
                    },
                    {
                        type: 'concrete',
                        decal: 'textures/fx/impact_concrete_medium.ktx2',
                        particles: 'fx/concrete_medium.json',
                        sound: 'sounds/impact_concrete_medium.ogg'
                    },
                    {
                        type: 'wood',
                        decal: 'textures/fx/impact_wood_medium.ktx2',
                        particles: 'fx/wood_medium.json',
                        sound: 'sounds/impact_wood_medium.ogg'
                    },
                    {
                        type: 'flesh',
                        decal: 'textures/fx/blood_medium.ktx2',
                        particles: 'fx/blood_medium.json',
                        sound: 'sounds/impact_flesh_medium.ogg'
                    }
                ]
            },
            performance: {
                maxParticles: 80,
                maxDecals: 40,
                maxLights: 1
            }
        });
        this.setupBloodEffects();
    }
    setupBloodEffects() {
        // Configurações específicas para efeitos de sangue
        this.weaponProfiles.set('blood_effects', {
            name: 'Blood Effects',
            config: {
                muzzleFlash: {
                    type: 'none',
                    scale: 0,
                    duration: 0,
                    color: { r: 0, g: 0, b: 0, a: 0 },
                    intensity: 0
                },
                shellEjection: {
                    model: '',
                    force: 0,
                    spin: { x: 0, y: 0, z: 0 },
                    lifetime: 0
                },
                impacts: [
                    {
                        type: 'flesh',
                        decal: 'textures/fx/blood_impact.ktx2',
                        particles: 'fx/blood_spray.json',
                        sound: 'sounds/impact_flesh.ogg'
                    },
                    {
                        type: 'flesh_critical',
                        decal: 'textures/fx/blood_critical.ktx2',
                        particles: 'fx/blood_heavy.json',
                        sound: 'sounds/impact_flesh_critical.ogg'
                    }
                ]
            },
            performance: {
                maxParticles: 200,
                maxDecals: 100,
                maxLights: 0
            }
        });
    }
    spawnWeaponEffect(weaponId, effectType, position, direction, surfaceType) {
        const profile = this.weaponProfiles.get(weaponId);
        if (!profile) {
            console.error(`Perfil de VFX não encontrado para arma: ${weaponId}`);
            return;
        }
        // Verifica limites de performance
        if (this.particleSystem.getActiveParticleCount() >= profile.performance.maxParticles) {
            console.warn(`Limite de partículas atingido para arma: ${weaponId}`);
            return;
        }
        switch (effectType) {
            case 'fire':
                this.spawnFireEffect(profile.config, position, direction);
                break;
            case 'impact':
                if (surfaceType) {
                    this.spawnImpactEffect(profile.config, position, direction, surfaceType);
                }
                break;
            case 'reload':
                this.spawnReloadEffect(profile.config, position);
                break;
        }
        // Métricas de performance em tempo real
        const metrics = this.getPerformanceMetrics(weaponId);
        if (metrics) {
            const { particleCount, decalCount, lightCount } = metrics;
            const hitsPerfLimit = particleCount >= profile.performance.maxParticles ||
                decalCount >= profile.performance.maxDecals ||
                lightCount >= profile.performance.maxLights;
            if (hitsPerfLimit) {
                console.warn(`Limites de performance atingidos para ${weaponId}:`, metrics);
            }
        }
    }
    spawnFireEffect(config, position, direction) {
        // Spawn muzzle flash
        this.vfxSystem.spawnMuzzleFlash(position, direction, config.muzzleFlash.type);
        // Eject shell casing
        this.vfxSystem.ejectShell(position, direction, config.shellEjection);
    }
    spawnImpactEffect(config, position, normal, surfaceType) {
        const impact = config.impacts.find(imp => imp.type === surfaceType);
        if (!impact)
            return;
        this.vfxSystem.createImpactEffect(position, normal, surfaceType);
    }
    spawnReloadEffect(config, position) {
        // Efeitos específicos de reload (ex: magazine drop)
        // TODO: Implementar efeitos de reload
    }
    spawnBloodEffect(position, direction, intensity, isCritical = false) {
        const profile = this.weaponProfiles.get('blood_effects');
        if (!profile)
            return;
        const impactType = isCritical ? 'flesh_critical' : 'flesh';
        const impact = profile.config.impacts.find(imp => imp.type === impactType);
        if (!impact)
            return;
        // Sistema de partículas otimizado para sangue
        this.vfxSystem.createImpactEffect(position, direction, impactType);
        // Decals dinâmicos de sangue com variação
        this.spawnBloodDecals(position, direction, intensity, isCritical);
        // Gotas de sangue secundárias
        if (intensity > 0.5) {
            this.spawnBloodDroplets(position, direction, intensity);
        }
    }
    spawnBloodDecals(position, direction, intensity, isCritical) {
        const decalCount = Math.floor(intensity * (isCritical ? 5 : 3));
        const spread = isCritical ? 0.5 : 0.3;
        for (let i = 0; i < decalCount; i++) {
            const offset = {
                x: (Math.random() - 0.5) * spread,
                y: (Math.random() - 0.5) * spread,
                z: (Math.random() - 0.5) * spread
            };
            const decalPos = {
                x: position.x + offset.x,
                y: position.y + offset.y,
                z: position.z + offset.z
            };
            const decalNormal = {
                x: direction.x + (Math.random() - 0.5) * 0.2,
                y: direction.y + (Math.random() - 0.5) * 0.2,
                z: direction.z + (Math.random() - 0.5) * 0.2
            };
            this.vfxSystem.createImpactEffect(decalPos, decalNormal, 'flesh');
        }
    }
    spawnBloodDroplets(position, direction, intensity) {
        const config = {
            position,
            direction,
            count: Math.floor(intensity * 30),
            lifetime: 0.5 + Math.random() * 0.5,
            size: 0.02 + Math.random() * 0.03,
            color: { r: 0.7, g: 0.0, b: 0.0, a: 0.8 },
            shader: 'blood',
            useInstancing: true
        };
        this.particleSystem.emitBurst(config);
    }
    getPerformanceMetrics(weaponId) {
        const profile = this.weaponProfiles.get(weaponId);
        if (!profile)
            return null;
        return {
            particleCount: this.particleSystem.getActiveParticleCount(),
            decalCount: this.vfxSystem.getActiveDecalCount(),
            lightCount: this.vfxSystem.getActiveLightCount(),
            memoryUsage: this.calculateMemoryUsage(profile),
            drawCalls: this.estimateDrawCalls(profile)
        };
    }
    calculateMemoryUsage(profile) {
        // TODO: Implementar cálculo real de uso de memória
        return 0;
    }
    estimateDrawCalls(profile) {
        // TODO: Implementar estimativa real de draw calls
        return 0;
    }
    calculateOverdraw() {
        // TODO: Implementar cálculo real de overdraw
        return 0;
    }
    measureFrameTime() {
        // TODO: Implementar medição real de tempo de frame
        return 0;
    }
    getBloodEffectMetrics() {
        const profile = this.weaponProfiles.get('blood_effects');
        if (!profile)
            return null;
        return {
            particleCount: this.particleSystem.getActiveParticleCount(),
            decalCount: this.vfxSystem.getActiveDecalCount(),
            memoryUsage: this.calculateMemoryUsage(profile),
            drawCalls: this.estimateDrawCalls(profile),
            performance: {
                avgFrameTime: this.measureFrameTime(),
                overdraw: this.calculateOverdraw()
            }
        };
    }
}
exports.DetailedWeaponVFX = DetailedWeaponVFX;
//# sourceMappingURL=DetailedWeaponVFX.js.map