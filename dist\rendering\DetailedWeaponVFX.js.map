{"version": 3, "file": "DetailedWeaponVFX.js", "sourceRoot": "", "sources": ["../../src/rendering/DetailedWeaponVFX.ts"], "names": [], "mappings": ";;;AACA,2DAAwD;AACxD,uDAAoD;AAYpD,MAAa,iBAAiB;IAK1B,YAAY,MAAiB;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,qCAAiB,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAEhC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,2BAA2B;QAC3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE;YACpC,IAAI,EAAE,sBAAsB;YAC5B,MAAM,EAAE;gBACJ,WAAW,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;oBACzC,SAAS,EAAE,GAAG;iBACjB;gBACD,aAAa,EAAE;oBACX,KAAK,EAAE,2BAA2B;oBAClC,KAAK,EAAE,GAAG;oBACV,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;oBAC5B,QAAQ,EAAE,GAAG;iBAChB;gBACD,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,+BAA+B;wBACtC,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,yBAAyB;qBACnC;oBACD;wBACI,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,kCAAkC;wBACzC,SAAS,EAAE,uBAAuB;wBAClC,KAAK,EAAE,4BAA4B;qBACtC;oBACD;wBACI,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,8BAA8B;wBACrC,SAAS,EAAE,uBAAuB;wBAClC,KAAK,EAAE,wBAAwB;qBAClC;oBACD;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,iCAAiC;wBACxC,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,yBAAyB;qBACnC;iBACJ;aACJ;YACD,WAAW,EAAE;gBACT,YAAY,EAAE,GAAG;gBACjB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC;aACf;SACJ,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,EAAE;YAClC,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE;gBACJ,WAAW,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;oBACzC,SAAS,EAAE,GAAG;iBACjB;gBACD,aAAa,EAAE;oBACX,KAAK,EAAE,4BAA4B;oBACnC,KAAK,EAAE,GAAG;oBACV,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;oBAC3B,QAAQ,EAAE,GAAG;iBAChB;gBACD,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,qCAAqC;wBAC5C,SAAS,EAAE,2BAA2B;wBACtC,KAAK,EAAE,+BAA+B;qBACzC;oBACD;wBACI,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,wCAAwC;wBAC/C,SAAS,EAAE,4BAA4B;wBACvC,KAAK,EAAE,kCAAkC;qBAC5C;oBACD;wBACI,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,oCAAoC;wBAC3C,SAAS,EAAE,wBAAwB;wBACnC,KAAK,EAAE,8BAA8B;qBACxC;oBACD;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,kCAAkC;wBACzC,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,+BAA+B;qBACzC;iBACJ;aACJ;YACD,WAAW,EAAE;gBACT,YAAY,EAAE,GAAG;gBACjB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC;aACf;SACJ,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE;YACpC,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE;gBACJ,WAAW,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;oBACzC,SAAS,EAAE,GAAG;iBACjB;gBACD,aAAa,EAAE;oBACX,KAAK,EAAE,4BAA4B;oBACnC,KAAK,EAAE,GAAG;oBACV,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;oBAC5B,QAAQ,EAAE,GAAG;iBAChB;gBACD,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,sCAAsC;wBAC7C,SAAS,EAAE,4BAA4B;wBACvC,KAAK,EAAE,gCAAgC;qBAC1C;oBACD;wBACI,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,yCAAyC;wBAChD,SAAS,EAAE,yBAAyB;wBACpC,KAAK,EAAE,mCAAmC;qBAC7C;oBACD;wBACI,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,qCAAqC;wBAC5C,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,+BAA+B;qBACzC;oBACD;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,+BAA+B;wBACtC,SAAS,EAAE,sBAAsB;wBACjC,KAAK,EAAE,gCAAgC;qBAC1C;iBACJ;aACJ;YACD,WAAW,EAAE;gBACT,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC;aACf;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAAY,iBAAiB;QAC1B,mDAAmD;QACnD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,EAAE;YACrC,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE;gBACJ,WAAW,EAAE;oBACT,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,CAAC;oBACR,QAAQ,EAAE,CAAC;oBACX,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;oBACjC,SAAS,EAAE,CAAC;iBACf;gBACD,aAAa,EAAE;oBACX,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,CAAC;oBACR,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;oBAC1B,QAAQ,EAAE,CAAC;iBACd;gBACD,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,+BAA+B;wBACtC,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,yBAAyB;qBACnC;oBACD;wBACI,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE,iCAAiC;wBACxC,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,kCAAkC;qBAC5C;iBACJ;aACJ;YACD,WAAW,EAAE;gBACT,YAAY,EAAE,GAAG;gBACjB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACf;SACJ,CAAC,CAAC;IACP,CAAC;IAEM,iBAAiB,CACpB,QAAgB,EAChB,UAAwC,EACxC,QAAiB,EACjB,SAAkB,EAClB,WAAqD;QAErD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,2CAA2C,QAAQ,EAAE,CAAC,CAAC;YACrE,OAAO;QACX,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACnF,OAAO,CAAC,IAAI,CAAC,4CAA4C,QAAQ,EAAE,CAAC,CAAC;YACrE,OAAO;QACX,CAAC;QAED,QAAQ,UAAU,EAAE,CAAC;YACjB,KAAK,MAAM;gBACP,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAC1D,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,WAAW,EAAE,CAAC;oBACd,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC7E,CAAC;gBACD,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACjD,MAAM;QACd,CAAC;QAED,wCAAwC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;YAC1D,MAAM,aAAa,GACf,aAAa,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY;gBACjD,UAAU,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS;gBAC3C,UAAU,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC;YAEhD,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,yCAAyC,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,MAAiB,EAAE,QAAiB,EAAE,SAAkB;QAC5E,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE9E,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;IACzE,CAAC;IAEO,iBAAiB,CACrB,MAAiB,EACjB,QAAiB,EACjB,MAAe,EACf,WAAoD;QAEpD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAEO,iBAAiB,CAAC,MAAiB,EAAE,QAAiB;QAC1D,oDAAoD;QACpD,sCAAsC;IAC1C,CAAC;IAEM,gBAAgB,CACnB,QAAiB,EACjB,SAAkB,EAClB,SAAiB,EACjB,aAAsB,KAAK;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC;QAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,8CAA8C;QAC9C,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAEnE,0CAA0C;QAC1C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAElE,8BAA8B;QAC9B,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEO,gBAAgB,CACpB,QAAiB,EACjB,SAAkB,EAClB,SAAiB,EACjB,UAAmB;QAEnB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG;gBACX,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM;gBACjC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM;gBACjC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM;aACpC,CAAC;YAEF,MAAM,QAAQ,GAAG;gBACb,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;gBACxB,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;gBACxB,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;aAC3B,CAAC;YAEF,MAAM,WAAW,GAAG;gBAChB,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC5C,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC5C,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;aAC/C,CAAC;YAEF,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEO,kBAAkB,CACtB,QAAiB,EACjB,SAAkB,EAClB,SAAiB;QAEjB,MAAM,MAAM,GAAG;YACX,QAAQ;YACR,SAAS;YACT,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YACnC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;YACjC,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,OAAO;YACf,aAAa,EAAE,IAAI;SACtB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,qBAAqB,CAAC,QAAgB;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE;YAC3D,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;YAChD,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC/C,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;SAC7C,CAAC;IACN,CAAC;IAEO,oBAAoB,CAAC,OAAyB;QAClD,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,iBAAiB,CAAC,OAAyB;QAC/C,kDAAkD;QAClD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,iBAAiB;QACrB,6CAA6C;QAC7C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,gBAAgB;QACpB,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,qBAAqB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE;YAC3D,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC/C,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAC1C,WAAW,EAAE;gBACT,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACrC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACrC;SACJ,CAAC;IACN,CAAC;CACJ;AAtZD,8CAsZC"}