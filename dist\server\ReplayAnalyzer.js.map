{"version": 3, "file": "ReplayAnalyzer.js", "sourceRoot": "", "sources": ["../../src/server/ReplayAnalyzer.ts"], "names": [], "mappings": ";;;AAGA,mDAAwC;AAexC,MAAa,cAAc;IAavB,YAAY,aAA4B,EAAE,OAAgB,EAAE,cAAsB,CAAC;QAC/E,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAEO,oBAAoB,CAAC,KAAa;QACtC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC5C,IAAI,uBAAM,CAAC,2BAA2B,EAAE;YACpC,UAAU,EAAE;gBACR,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;aACtC;SACJ,CAAC,CACL,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAC/B,QAAgB,EAChB,QAAgB,EAChB,MAAc;QAEd,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE3D,+CAA+C;QAC/C,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACnE,IAAI,CAAC,eAAe;gBAAE,MAAM;YAE5B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI;gBAAE,MAAM;YAEjB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,MAAc;QACxD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAE/C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC3D,gBAAgB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,MAAkB;QAC5C,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;QACxD,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAEnC,OAAO,WAAW,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,cAAc,CAAC,mBAAmB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAC5F,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC9D,WAAW,GAAG,QAAQ,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAiB,EAAE,MAAc;QACxD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,MAAyB,EAAE,EAAE;gBACjD,OAAO,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC1B,QAAgB,EAChB,MAAyB;QAEzB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEzE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,UAAU,IAAI,cAAc,CAAC,uBAAuB,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAC9B,QAAgB,EAChB,MAAyB;QAEzB,MAAM,KAAK,GAAG,IAAI,GAAG,EAAkB,CAAC;QAExC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,oBAAoB;YAE5D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAC7C,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,CAAC,QAAQ,CACjB,CAAC;YAEF,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAC/B,KAAsB,EACtB,KAA0B;QAE1B,MAAM,QAAQ,GAAG;YACb,KAAK;YACL,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC;YAChC,QAAQ,EAAE;gBACN,UAAU,EAAE,iBAAiB;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,UAAU,EAAE,KAAK,CAAC,UAAU;aAC/B;SACJ,CAAC;QAEF,0CAA0C;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAChE,CAAC;;AAtJL,wCAuJC;AAtJ2B,kCAAmB,GAAG,KAAK,CAAC,CAAC,cAAc;AAC3C,sCAAuB,GAAG,GAAG,CAAC"}