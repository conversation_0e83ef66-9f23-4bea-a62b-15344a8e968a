{"version": 3, "file": "AntiCheatSystem.js", "sourceRoot": "", "sources": ["../../src/server/AntiCheatSystem.ts"], "names": [], "mappings": ";;;AACA,qDAAkD;AA0ClD,MAAa,eAAe;IAexB,YAAY,MAA4B;QACpC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1E,CAAC;IAEO,iBAAiB;QACrB,qDAAqD;QACrD,yEAAyE;QACzE,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACrB,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,wBAAwB;aAC5D,CAAC;SACL,CAAC;IACN,CAAC;IAEM,kBAAkB,CAAC,QAAgB,EAAE,KAAkB;QAC1D,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAE/C,4CAA4C;QAC5C,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEM,qBAAqB,CAAC,QAAgB,EAAE,QAAiB,EAAE,SAAiB;QAC/E,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjD,sBAAsB;QACtB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAEzC,+BAA+B;QAC/B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAEM,sBAAsB,CAAC,QAAgB,EAAE,GAAQ;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,qCAAqC;QACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,WAAmB,GAAG;QACrE,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAC5C,QAAQ,EACR,QAAQ,EACR,kBAAkB,CACrB,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,QAAgB,EAAE,UAAkB;QAClE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAEhF,sDAAsD;QACtD,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAC5C,MAAM,CAAC,EAAE,EACT,GAAG,EAAE,4CAA4C;YACjD,iBAAiB,CACpB,CAAC;QACN,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAClC,QAAgB,EAChB,QAAa;QAEb,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI;YACnD,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,cAAc,EAAE,CAAC;YACjB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;SAChC,CAAC;QAEF,0CAA0C;QAC1C,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,CAAC,cAAc,EAAE,CAAC;QAExB,mEAAmE;QACnE,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QAC/C,6CAA6C;QAC7C,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;QAE3C,gCAAgC;QAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAChF,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAC5C,MAAM,CAAC,EAAE,EACT,GAAG,EAAE,wBAAwB;YAC7B,8BAA8B,CACjC,CAAC;QACN,CAAC;IACL,CAAC;IAEL,iDAAiD;IACrC,gBAAgB,CAAC,GAAa;QAClC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC/B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,GAAa;QACjC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,YAAY,CAAC,QAAgB,EAAE,KAAkB;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAE1D,IAAI,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAClD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC;QAED,4CAA4C;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,UAAU,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;YACpD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,OAAsB;QAC5C,MAAM,QAAQ,GAAG;YACb,OAAO,CAAC,QAAQ,CAAC,QAAQ;YACzB,OAAO,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC;YACnD,OAAO,CAAC,SAAS,CAAC,mBAAmB;YACrC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;SACxD,CAAC;QACF,OAAO,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,KAAkB;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO;QAEvC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAC/C,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACnE,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC5C,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAgB,EAAE,QAAiB;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,6CAA6C;QAC7C,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACpE,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAC;gBACrD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;gBACvD,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,GAAQ;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,oCAAoC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,WAAW,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAgB,EAAE,GAAQ;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,QAAgB,EAAE,QAAgB,EAAE,UAAkB;QACrE,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,UAAU,GAAG;gBACT,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;aAChC,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,CAAC;QAED,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5B,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE1C,IAAI,UAAU,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC;gBACV,QAAQ;gBACR,SAAS,EAAE,QAAQ;gBACnB,UAAU;gBACV,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBACtC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;aAC3C,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAAY,sBAAsB,CAAC,QAAgB,EAAE,SAAiB,EAAE,UAAkB;QACvF,IAAI,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG;gBACL,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;aAChC,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5B,MAAM,CAAC,cAAc,EAAE,CAAC;QACxB,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEtC,mCAAmC;QACnC,IAAI,MAAM,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YACrE,IAAI,CAAC,QAAQ,CAAC;gBACV,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,QAAQ,EAAE,WAAW;gBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;aAC3C,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,QAAQ,CAAC,OAAgB;QAC7B,oCAAoC;QACpC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,4BAA4B;QAC5B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAEO,oBAAoB,CAAC,cAAsB;QAC/C,mCAAmC;QACnC,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;IACnH,CAAC;IAEO,eAAe,CAAC,QAAgB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,OAAO;YACH,QAAQ,EAAE,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE;YACjC,aAAa,EAAE,EAAE,GAAG,OAAO,CAAC,aAAa,EAAE;YAC3C,SAAS,EAAE,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE;YACnC,UAAU,EAAE;gBACR,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,qBAAqB;gBACpE,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;aACxD;SACJ,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,OAAsB,EAAE,gBAAwB;QACpE,MAAM,MAAM,GAAG,gBAAgB,GAAG,eAAe,CAAC,eAAe,CAAC;QAElE,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACtE,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAE5E,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACxE,OAAO,CAAC,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAE9E,2BAA2B;QAC3B,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QAC5F,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IAC1F,CAAC;IAEO,uBAAuB;QAC3B,OAAO;YACH,QAAQ,EAAE;gBACN,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,CAAC;gBACZ,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,EAAE;aACpB;YACD,aAAa,EAAE;gBACX,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,EAAE;aACjB;YACD,SAAS,EAAE;gBACP,SAAS,EAAE,CAAC;gBACZ,mBAAmB,EAAE,CAAC;gBACtB,cAAc,EAAE,IAAI,GAAG,EAAE;aAC5B;YACD,UAAU,EAAE;gBACR,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,EAAE;aACjB;SACJ,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,SAAoB,EAAE,UAAoB;QAC9D,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,GAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW;YAClE,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,kBAAkB,CAAC,OAAsB;QAC7C,4CAA4C;QAC5C,OAAO;YACH,OAAO,CAAC,QAAQ,CAAC,QAAQ;YACzB,OAAO,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1E,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;SAC3C,CAAC;IACN,CAAC;IAEO,oBAAoB,CAAC,OAAsB;QAC/C,6BAA6B;QAC7B,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,cAAc;IAC7C,CAAC;IAEO,eAAe,CAAC,OAAY;QAChC,4DAA4D;QAC5D,uDAAuD;QACvD,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;;AArYL,0CAsYC;AArY2B,kCAAkB,GAAG,IAAI,CAAC,CAAC,YAAY;AACvC,+BAAe,GAAG,MAAM,CAAC,CAAC,YAAY;AACtC,oCAAoB,GAAG,IAAI,CAAC"}