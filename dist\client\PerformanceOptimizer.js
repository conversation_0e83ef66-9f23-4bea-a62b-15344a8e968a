"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceOptimizer = void 0;
class PerformanceOptimizer {
    constructor(config = {}, errorManager, systems) {
        this.isOptimizing = false;
        this.config = { ...PerformanceOptimizer.DEFAULT_CONFIG, ...config };
        this.errorManager = errorManager;
        this.systems = systems;
        this.optimizationQueue = new Map();
        this.initializeOptimizationStrategies();
        this.setupErrorHandlers();
    }
    initializeOptimizationStrategies() {
        // Estratégias de otimização de renderização
        this.optimizationQueue.set('renderingQuality', async () => {
            await this.optimizeRenderingQuality();
        });
        // Estratégias de otimização de partículas
        this.optimizationQueue.set('particleSystem', async () => {
            await this.optimizeParticleSystem();
        });
        // Estratégias de otimização de física
        this.optimizationQueue.set('physics', async () => {
            await this.optimizePhysics();
        });
        // Estratégias de otimização de memória
        this.optimizationQueue.set('memoryUsage', async () => {
            await this.optimizeMemoryUsage();
        });
        // Estratégias de otimização de rede
        this.optimizationQueue.set('networkOptimization', async () => {
            await this.optimizeNetworkUsage();
        });
    }
    setupErrorHandlers() {
        this.errorManager.subscribe(async (report) => {
            if (report.type === 'performance_issue') {
                await this.handlePerformanceIssue(report);
            }
        });
    }
    async handlePerformanceIssue(report) {
        if (this.isOptimizing)
            return;
        this.isOptimizing = true;
        try {
            const metrics = report.context;
            // Prioriza otimizações baseado nas métricas
            if (metrics.fps < this.config.targetFPS * 0.9) {
                await this.optimizationQueue.get('renderingQuality')?.();
                await this.optimizationQueue.get('particleSystem')?.();
            }
            if (metrics.memory?.used / metrics.memory?.total > this.config.maxMemoryUsage) {
                await this.optimizationQueue.get('memoryUsage')?.();
            }
            if (metrics.network?.ping > this.config.networkLatencyThreshold) {
                await this.optimizationQueue.get('networkOptimization')?.();
            }
            // Verifica se as otimizações foram efetivas
            const newMetrics = await this.collectPerformanceMetrics();
            if (this.hasImproved(metrics, newMetrics)) {
                console.log('Performance optimization successful');
            }
            else {
                console.warn('Performance optimization had limited effect');
            }
        }
        finally {
            this.isOptimizing = false;
        }
    }
    async optimizeRenderingQuality() {
        const renderer = this.systems.rendering;
        // Ajusta dinamicamente a qualidade do rendering
        if (renderer) {
            // Reduz sombras dinâmicas
            renderer.setDynamicShadowQuality(0.75);
            // Ajusta resolução de texturas
            renderer.setTextureQuality(0.8);
            // Reduz efeitos pós-processamento
            renderer.setPostProcessingQuality(0.75);
            // Otimiza LOD (Level of Detail)
            renderer.optimizeLODDistances();
        }
    }
    async optimizeParticleSystem() {
        const particles = this.systems.particles;
        if (particles) {
            // Reduz quantidade máxima de partículas
            particles.setMaxParticles(Math.floor(particles.getMaxParticles() * 0.8));
            // Aumenta culling de partículas
            particles.setParticleCullingDistance(0.8);
            // Simplifica simulação de partículas
            particles.setSimulationQuality(0.75);
        }
    }
    async optimizePhysics() {
        const physics = this.systems.physics;
        if (physics) {
            // Reduz precisão da simulação física
            physics.setSimulationAccuracy(0.8);
            // Aumenta threshold de sleep para objetos
            physics.setSleepThreshold(1.2);
            // Otimiza broadphase
            physics.optimizeBroadphase();
        }
    }
    async optimizeMemoryUsage() {
        // Limpa caches não essenciais
        this.systems.rendering?.clearUnusedTextures();
        this.systems.particles?.clearInactiveParticles();
        // Força garbage collection
        if (window.gc) {
            window.gc();
        }
        // Reduz tamanho de buffers
        this.systems.netcode?.optimizeBuffers();
        this.systems.rendering?.optimizeBuffers();
    }
    async optimizeNetworkUsage() {
        const netcode = this.systems.netcode;
        if (netcode) {
            // Aumenta compressão de pacotes
            netcode.setCompressionLevel(0.9);
            // Reduz taxa de atualização de dados não críticos
            netcode.setUpdateRatePriority('low');
            // Otimiza predição de movimento
            netcode.optimizeMovementPrediction();
        }
    }
    async collectPerformanceMetrics() {
        return new Promise(resolve => {
            requestAnimationFrame(() => {
                const metrics = {
                    fps: performance.now(),
                    memory: performance.memory,
                    // Adiciona outras métricas relevantes
                };
                resolve(metrics);
            });
        });
    }
    hasImproved(oldMetrics, newMetrics) {
        // Verifica se houve melhoria significativa
        const fpsImprovement = newMetrics.fps > oldMetrics.fps * 1.1;
        const memoryImprovement = newMetrics.memory.used < oldMetrics.memory.used * 0.9;
        return fpsImprovement || memoryImprovement;
    }
    resetOptimizations() {
        // Restaura configurações padrão
        this.systems.rendering?.resetQuality();
        this.systems.particles?.resetQuality();
        this.systems.physics?.resetSimulation();
        this.systems.netcode?.resetOptimizations();
    }
}
exports.PerformanceOptimizer = PerformanceOptimizer;
PerformanceOptimizer.DEFAULT_CONFIG = {
    targetFPS: 999,
    maxMemoryUsage: 0.9, // 90% do total
    maxGPUTemp: 80,
    maxCPUTemp: 80,
    networkLatencyThreshold: 20 // ms
};
//# sourceMappingURL=PerformanceOptimizer.js.map