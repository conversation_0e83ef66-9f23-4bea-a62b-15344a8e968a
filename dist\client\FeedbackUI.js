"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackUI = void 0;
class FeedbackUI {
    constructor(config) {
        this.visible = false;
        this.currentView = 'feedback';
        this.attachments = [];
        this.screenshotMode = false;
        this.dragStartPos = null;
        this.config = config;
        this.initializeUI();
        this.setupEventListeners();
    }
    initializeUI() {
        // Cria os elementos base da UI
        this.createMainContainer();
        this.createTabButtons();
        this.createFeedbackForm();
        this.createReportForm();
        this.createAttachmentArea();
        this.createScreenshotTools();
    }
    createMainContainer() {
        const container = document.createElement('div');
        container.id = 'feedback-ui';
        container.style.cssText = `
            position: absolute;
            left: ${this.config.position.x}px;
            top: ${this.config.position.y}px;
            width: ${this.config.size.x}px;
            height: ${this.config.size.y}px;
            background: ${this.config.theme.background};
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: none;
            z-index: 1000;
        `;
        document.body.appendChild(container);
    }
    createFeedbackForm() {
        const form = document.createElement('form');
        form.id = 'feedback-form';
        form.innerHTML = `
            <h2>Enviar Feedback</h2>
            <select id="feedback-type" required>
                <option value="bug">Bug/Problema</option>
                <option value="suggestion">Sugestão</option>
                <option value="balance">Balanceamento</option>
                <option value="performance">Performance</option>
                <option value="other">Outro</option>
            </select>
            <input type="text" id="feedback-title" placeholder="Título" required>
            <textarea id="feedback-description" placeholder="Descreva em detalhes..." required></textarea>
            <div class="attachment-area"></div>
            <button type="submit">Enviar Feedback</button>
        `;
        document.getElementById('feedback-ui')?.appendChild(form);
    }
    createReportForm() {
        const form = document.createElement('form');
        form.id = 'report-form';
        form.style.display = 'none';
        form.innerHTML = `
            <h2>Denunciar Jogador</h2>
            <input type="text" id="reported-player" placeholder="Nome do jogador" required>
            <select id="report-type" required>
                <option value="cheating">Trapaça</option>
                <option value="griefing">Griefing</option>
                <option value="harassment">Assédio</option>
                <option value="inappropriate_content">Conteúdo Impróprio</option>
            </select>
            <textarea id="report-description" placeholder="Descreva o ocorrido..." required></textarea>
            <div class="attachment-area"></div>
            <button type="submit">Enviar Denúncia</button>
        `;
        document.getElementById('feedback-ui')?.appendChild(form);
    }
    createAttachmentArea() {
        const area = document.createElement('div');
        area.className = 'attachment-tools';
        area.innerHTML = `
            <button id="take-screenshot">Capturar Tela</button>
            <button id="attach-log">Anexar Log</button>
            <button id="attach-replay">Anexar Replay</button>
            <div class="attachments-preview"></div>
        `;
        document.getElementById('feedback-ui')?.appendChild(area);
    }
    createScreenshotTools() {
        const tools = document.createElement('div');
        tools.id = 'screenshot-tools';
        tools.style.display = 'none';
        tools.innerHTML = `
            <div class="screenshot-overlay"></div>
            <div class="screenshot-cursor"></div>
            <div class="screenshot-controls">
                <button id="capture">Capturar</button>
                <button id="cancel">Cancelar</button>
            </div>
        `;
        document.body.appendChild(tools);
    }
    setupEventListeners() {
        // Toggle entre feedback e denúncia
        document.getElementById('feedback-tab')?.addEventListener('click', () => {
            this.switchView('feedback');
        });
        document.getElementById('report-tab')?.addEventListener('click', () => {
            this.switchView('report');
        });
        // Captura de tela
        document.getElementById('take-screenshot')?.addEventListener('click', () => {
            this.startScreenshotMode();
        });
        // Drag and drop para anexos
        const attachmentArea = document.querySelector('.attachment-area');
        if (attachmentArea) {
            attachmentArea.addEventListener('dragover', this.handleDragOver.bind(this));
            attachmentArea.addEventListener('drop', this.handleDrop.bind(this));
        }
        // Submissão de formulários
        document.getElementById('feedback-form')?.addEventListener('submit', this.handleFeedbackSubmit.bind(this));
        document.getElementById('report-form')?.addEventListener('submit', this.handleReportSubmit.bind(this));
    }
    show() {
        this.visible = true;
        const container = document.getElementById('feedback-ui');
        if (container) {
            container.style.display = 'block';
        }
    }
    hide() {
        this.visible = false;
        const container = document.getElementById('feedback-ui');
        if (container) {
            container.style.display = 'none';
        }
    }
    switchView(view) {
        this.currentView = view;
        const feedbackForm = document.getElementById('feedback-form');
        const reportForm = document.getElementById('report-form');
        if (feedbackForm && reportForm) {
            feedbackForm.style.display = view === 'feedback' ? 'block' : 'none';
            reportForm.style.display = view === 'report' ? 'block' : 'none';
        }
    }
    async startScreenshotMode() {
        this.screenshotMode = true;
        const tools = document.getElementById('screenshot-tools');
        if (tools) {
            tools.style.display = 'block';
        }
        // Esconde a UI de feedback temporariamente
        this.hide();
    }
    async handleScreenshot(area) {
        try {
            const screenshot = await this.captureScreenshot(area);
            await this.addAttachment({
                id: `screenshot_${Date.now()}`,
                type: 'screenshot',
                url: screenshot,
                size: 0, // Será calculado
                mimeType: 'image/png'
            });
        }
        catch (error) {
            console.error('Erro ao capturar screenshot:', error);
        }
        finally {
            this.screenshotMode = false;
            this.show();
        }
    }
    async captureScreenshot(area) {
        // Implementação dependerá do sistema de captura de tela
        return '';
    }
    async addAttachment(attachment) {
        this.attachments.push(attachment);
        this.updateAttachmentPreview();
    }
    updateAttachmentPreview() {
        const preview = document.querySelector('.attachments-preview');
        if (!preview)
            return;
        preview.innerHTML = '';
        for (const attachment of this.attachments) {
            const element = document.createElement('div');
            element.className = 'attachment-preview';
            if (attachment.type === 'screenshot' && attachment.thumbnail) {
                element.innerHTML = `
                    <img src="${attachment.thumbnail}" alt="Screenshot">
                    <button data-id="${attachment.id}" class="remove-attachment">X</button>
                `;
            }
            else {
                element.innerHTML = `
                    <span>${attachment.type}</span>
                    <button data-id="${attachment.id}" class="remove-attachment">X</button>
                `;
            }
            preview.appendChild(element);
        }
    }
    async handleFeedbackSubmit(event) {
        event.preventDefault();
        const form = event.target;
        const feedback = {
            type: document.getElementById('feedback-type').value,
            title: document.getElementById('feedback-title').value,
            description: document.getElementById('feedback-description').value,
            attachments: this.attachments
        };
        try {
            await window.game.submitFeedback(feedback);
            this.showSuccess('Feedback enviado com sucesso!');
            form.reset();
            this.attachments = [];
            this.updateAttachmentPreview();
        }
        catch (error) {
            this.showError('Erro ao enviar feedback. Tente novamente.');
        }
    }
    async handleReportSubmit(event) {
        event.preventDefault();
        const form = event.target;
        const report = {
            reportedPlayerId: document.getElementById('reported-player').value,
            type: document.getElementById('report-type').value,
            description: document.getElementById('report-description').value,
            attachments: this.attachments
        };
        try {
            await window.game.submitReport(report);
            this.showSuccess('Denúncia enviada com sucesso!');
            form.reset();
            this.attachments = [];
            this.updateAttachmentPreview();
        }
        catch (error) {
            this.showError('Erro ao enviar denúncia. Tente novamente.');
        }
    }
    showSuccess(message) {
        // Implementação de notificação de sucesso
    }
    showError(message) {
        // Implementação de notificação de erro
    }
    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        // Adiciona visual feedback
    }
    async handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        const files = event.dataTransfer?.files;
        if (!files)
            return;
        for (const file of Array.from(files)) {
            if (file.size > 50 * 1024 * 1024) { // 50MB limit
                this.showError(`Arquivo muito grande: ${file.name}`);
                continue;
            }
            try {
                const attachment = await this.processFile(file);
                await this.addAttachment(attachment);
            }
            catch (error) {
                this.showError(`Erro ao processar arquivo: ${file.name}`);
            }
        }
    }
    async processFile(file) {
        // Implementação do processamento de arquivo
        return {
            id: `file_${Date.now()}`,
            type: file.type.startsWith('image/') ? 'screenshot' : 'log',
            url: URL.createObjectURL(file),
            size: file.size,
            mimeType: file.type
        };
    }
}
exports.FeedbackUI = FeedbackUI;
//# sourceMappingURL=FeedbackUI.js.map