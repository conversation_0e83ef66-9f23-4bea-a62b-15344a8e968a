"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextureCompressor = void 0;
const perf_hooks_1 = require("perf_hooks");
class TextureCompressor {
    constructor(metrics) {
        this.wasmModule = null;
        this.metrics = metrics;
        this.qualityCache = new Map();
    }
    async initialize() {
        // Carrega o módulo WASM do basis-universal
        const response = await fetch('/lib/basis_encoder.wasm');
        const wasmBytes = await response.arrayBuffer();
        this.wasmModule = await WebAssembly.instantiate(wasmBytes, {
            env: {
                memory: new WebAssembly.Memory({ initial: 512 }),
                emscripten_notify_memory_growth: () => { }
            }
        });
        // Inicializa o encoder e transcoder
        this.basisEncoder = {
            // Mapeia funções do WASM para métodos JavaScript
            encode: this.wasmModule.instance.exports.basis_encode,
            init: this.wasmModule.instance.exports.basis_init,
            transcode: this.wasmModule.instance.exports.basis_transcode
        };
        await this.basisEncoder.init();
    }
    async compressTexture(imageData, options) {
        const startTime = perf_hooks_1.performance.now();
        // Métricas originais
        const originalSize = imageData.data.byteLength;
        const vramUsageOriginal = this.estimateVRAMUsage(imageData.width, imageData.height, 4 // RGBA
        );
        // Comprime a textura
        const compressedData = await this.encodeToBasis(imageData, options);
        // Métricas de compressão
        const compressionTime = perf_hooks_1.performance.now() - startTime;
        const compressedSize = compressedData.byteLength;
        // Métricas de qualidade
        const quality = await this.analyzeQuality(imageData, compressedData);
        // Estima uso de VRAM após compressão
        const vramUsageCompressed = this.estimateCompressedVRAMUsage(imageData.width, imageData.height, options.format);
        const metrics = {
            originalSize,
            compressedSize,
            compressionRatio: originalSize / compressedSize,
            compressionTime,
            transcodingTime: 0, // Será atualizado durante a transcodificação
            vramUsageOriginal,
            vramUsageCompressed,
            quality
        };
        // Atualiza métricas globais
        this.updatePerformanceMetrics(metrics);
        return [compressedData, metrics];
    }
    async transcodeTexture(compressedData, targetFormat) {
        const startTime = perf_hooks_1.performance.now();
        // Transcodifica para o formato nativo da GPU
        const transcodedData = await this.basisEncoder.transcode(compressedData, targetFormat);
        const transcodingTime = perf_hooks_1.performance.now() - startTime;
        // Atualiza métricas de transcodificação
        this.metrics.updateMetric('textureTranscodingTime', transcodingTime);
        return [transcodedData, transcodingTime];
    }
    async encodeToBasis(imageData, options) {
        // Configura parâmetros de codificação
        const encoderParams = {
            width: imageData.width,
            height: imageData.height,
            format: options.format,
            quality: options.quality,
            compLevel: options.compressionLevel,
            flipY: options.yFlip,
            genMips: options.mipmap,
            isNormal: options.normalMap,
            separateRGBA: options.separateRGBFromAlpha,
            perceptual: options.perceptual
        };
        // Codifica para formato Basis
        return await this.basisEncoder.encode(imageData.data, encoderParams);
    }
    async analyzeQuality(original, compressed) {
        // Cache key baseado no hash dos dados
        const cacheKey = await this.generateDataHash(compressed);
        if (this.qualityCache.has(cacheKey)) {
            return this.qualityCache.get(cacheKey);
        }
        // Decodifica dados comprimidos para comparação
        const decodedData = await this.decodeForAnalysis(compressed);
        // Calcula métricas de qualidade
        const metrics = {
            psnr: this.calculatePSNR(original.data, decodedData),
            ssim: this.calculateSSIM(original.data, decodedData),
            artifacts: this.detectArtifacts(original.data, decodedData)
        };
        // Armazena no cache
        this.qualityCache.set(cacheKey, metrics);
        return metrics;
    }
    async generateDataHash(data) {
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hashBuffer))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }
    async decodeForAnalysis(compressedData) {
        // Decodifica dados comprimidos para RGBA
        return new Uint8Array(0); // TODO: Implementar decodificação real
    }
    calculatePSNR(original, decoded) {
        let mse = 0;
        for (let i = 0; i < original.length; i++) {
            const diff = original[i] - decoded[i];
            mse += diff * diff;
        }
        mse /= original.length;
        if (mse === 0)
            return Infinity;
        return 20 * Math.log10(255) - 10 * Math.log10(mse);
    }
    calculateSSIM(original, decoded) {
        // Implementação simplificada do SSIM
        // TODO: Implementar cálculo completo do SSIM
        return 0.95; // Valor exemplo
    }
    detectArtifacts(original, decoded) {
        // Detecta artefatos de compressão
        // TODO: Implementar detecção real de artefatos
        return 0.05; // Valor exemplo (5% de artefatos)
    }
    estimateVRAMUsage(width, height, bytesPerPixel) {
        // Estima uso de VRAM incluindo mipmaps
        let total = 0;
        let w = width;
        let h = height;
        while (w >= 1 && h >= 1) {
            total += w * h * bytesPerPixel;
            w = Math.floor(w / 2);
            h = Math.floor(h / 2);
        }
        return total;
    }
    estimateCompressedVRAMUsage(width, height, format) {
        // Calcula uso de VRAM para formato comprimido
        const blockSize = format === 'UASTC' ? 16 : 8; // bytes por bloco 4x4
        const blocksWidth = Math.ceil(width / 4);
        const blocksHeight = Math.ceil(height / 4);
        return blocksWidth * blocksHeight * blockSize;
    }
    updatePerformanceMetrics(metrics) {
        this.metrics.updateMetric('textureCompressionRatio', metrics.compressionRatio);
        this.metrics.updateMetric('textureCompressionTime', metrics.compressionTime);
        this.metrics.updateMetric('textureTranscodingTime', metrics.transcodingTime);
        this.metrics.updateMetric('textureVRAMSaving', 1 - (metrics.vramUsageCompressed / metrics.vramUsageOriginal));
        this.metrics.updateMetric('textureQualityPSNR', metrics.quality.psnr);
        this.metrics.updateMetric('textureQualitySSIM', metrics.quality.ssim);
        this.metrics.updateMetric('textureArtifacts', metrics.quality.artifacts);
    }
}
exports.TextureCompressor = TextureCompressor;
//# sourceMappingURL=TextureCompressor.js.map