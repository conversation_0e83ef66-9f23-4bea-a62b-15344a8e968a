"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeaponVFXSystem = void 0;
class WeaponVFXSystem {
    constructor(particleSystem, config) {
        this.activeDecals = 0;
        this.activeLights = 0;
        this.particleSystem = particleSystem;
        this.config = config;
        this.shellPool = [];
        this.decalPool = [];
        this.initializeShellPool();
        this.initializeDecalPool();
    }
    getActiveDecalCount() {
        return this.activeDecals;
    }
    getActiveLightCount() {
        return this.activeLights;
    }
    getInactiveShell() {
        return this.shellPool.find(shell => !shell.inUse);
    }
    calculateShellVelocity(direction, config) {
        return {
            x: direction.x * config.velocity.x + (Math.random() - 0.5) * 0.5,
            y: direction.y * config.velocity.y + Math.random() * 1.5,
            z: direction.z * config.velocity.z + (Math.random() - 0.5) * 0.5
        };
    }
    addMuzzleLight(position, config) {
        const light = {
            position,
            color: {
                r: config.color.r * 1.2,
                g: config.color.g * 1.1,
                b: config.color.b
            },
            intensity: config.intensity * 2,
            radius: config.scale * 3,
            decay: 10,
            duration: config.duration
        };
        this.particleSystem.addDynamicLight(light);
    }
    spawnMuzzleFlash(position, direction, weaponType) {
        const config = this.getMuzzleFlashConfig(weaponType);
        // Usa GPU Instancing para renderizar todas as partículas do flash
        this.particleSystem.emitBurst({
            position,
            direction,
            count: Math.min(config.maxParticles, WeaponVFXSystem.MAX_MUZZLE_FLASH_PARTICLES),
            lifetime: config.duration,
            size: config.scale,
            color: config.color,
            shader: 'muzzleFlash',
            useInstancing: true
        });
        // Adiciona luz dinâmica otimizada
        this.addMuzzleLight(position, config);
    }
    ejectShell(position, direction, config) {
        const shell = this.getInactiveShell();
        if (!shell)
            return;
        shell.mesh.position = position;
        shell.physics.velocity = this.calculateShellVelocity(direction, {
            velocity: { x: config.force, y: config.force, z: config.force },
            angularVelocity: config.spin,
            lifetime: config.lifetime,
            scale: 1.0,
            maxShells: this.config.shellEjection.maxShells
        });
        shell.inUse = true;
        setTimeout(() => {
            shell.inUse = false;
        }, config.lifetime * 1000);
    }
    createImpactEffect(position, normal, surfaceType) {
        const config = this.config.impactEffects;
        // Sistema de decals otimizado
        this.addImpactDecal(position, normal, surfaceType);
        // Efeitos de partículas específicos por superfície
        switch (surfaceType) {
            case 'metal':
                this.createMetalImpact(position, normal);
                break;
            case 'concrete':
                this.createConcreteImpact(position, normal);
                break;
            case 'wood':
                this.createWoodImpact(position, normal);
                break;
            case 'dirt':
                this.createDirtImpact(position, normal);
                break;
            case 'water':
                this.createWaterImpact(position, normal);
                break;
            case 'glass':
                this.createGlassImpact(position, normal);
                break;
            default:
                this.createDefaultImpact(position, normal);
        }
    }
    createMetalImpact(position, normal) {
        // Sparks intensos e quentes
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 15,
            lifetime: 0.3,
            size: 0.05,
            color: { r: 1, g: 0.7, b: 0.3, a: 1 },
            shader: 'spark',
            useInstancing: true
        });
    }
    createConcreteImpact(position, normal) {
        // Poeira e fragmentos
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 20,
            lifetime: 1.0,
            size: 0.1,
            color: { r: 0.7, g: 0.7, b: 0.7, a: 0.8 },
            shader: 'dust',
            useInstancing: true
        });
    }
    createWoodImpact(position, normal) {
        // Lascas de madeira
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 12,
            lifetime: 0.8,
            size: 0.08,
            color: { r: 0.6, g: 0.4, b: 0.2, a: 1 },
            shader: 'woodChip',
            useInstancing: true
        });
    }
    createWaterImpact(position, normal) {
        // Gotas e respingos
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 25,
            lifetime: 0.6,
            size: 0.06,
            color: { r: 0.3, g: 0.5, b: 1.0, a: 0.7 },
            shader: 'waterDrop',
            useInstancing: true
        });
    }
    createGlassImpact(position, normal) {
        // Fragmentos de vidro
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 18,
            lifetime: 0.7,
            size: 0.04,
            color: { r: 0.9, g: 0.9, b: 1.0, a: 0.5 },
            shader: 'glassShards',
            useInstancing: true
        });
    }
    createDirtImpact(position, normal) {
        // Partículas de terra
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 15,
            lifetime: 1.2,
            size: 0.12,
            color: { r: 0.4, g: 0.3, b: 0.2, a: 0.9 },
            shader: 'dirt',
            useInstancing: true
        });
    }
    createDefaultImpact(position, normal) {
        // Efeito genérico de impacto
        this.particleSystem.emitBurst({
            position,
            direction: normal,
            count: 10,
            lifetime: 0.5,
            size: 0.07,
            color: { r: 0.8, g: 0.8, b: 0.8, a: 0.8 },
            shader: 'default',
            useInstancing: true
        });
    }
    createShellMesh() {
        // TODO: Implementar criação de mesh
        return {};
    }
    createShellPhysics() {
        return {
            velocity: { x: 0, y: 0, z: 0 },
            angularVelocity: { x: 0, y: 0, z: 0 },
            drag: 0.1
        };
    }
    initializeShellPool() {
        // Pre-aloca pool de cartuchos para reutilização
        for (let i = 0; i < WeaponVFXSystem.MAX_SHELL_CASINGS; i++) {
            this.shellPool.push({
                mesh: this.createShellMesh(),
                physics: this.createShellPhysics(),
                inUse: false
            });
        }
    }
    initializeDecalPool() {
        // Pre-aloca pool de decals
        for (let i = 0; i < WeaponVFXSystem.MAX_DECALS; i++) {
            this.decalPool.push({
                texture: null,
                position: { x: 0, y: 0, z: 0 },
                normal: { x: 0, y: 1, z: 0 },
                age: Infinity
            });
        }
    }
    addImpactDecal(position, normal, surfaceType) {
        // Gerencia o pool de decals para reutilização
        const decal = this.getInactiveDecal();
        if (!decal) {
            this.removeOldestDecal();
        }
        // Carrega textura específica para o tipo de superfície
        const texture = this.loadDecalTexture(surfaceType);
        // Adiciona novo decal
        this.decalPool.push({
            texture,
            position,
            normal,
            age: 0
        });
    }
    getInactiveDecal() {
        // Procura por um decal inativo no pool
        return this.decalPool.find(decal => decal.age >= this.config.impactEffects.decalLifetime);
    }
    removeOldestDecal() {
        if (this.decalPool.length === 0)
            return;
        // Remove o decal mais antigo
        let oldestIndex = 0;
        let oldestAge = 0;
        this.decalPool.forEach((decal, index) => {
            if (decal.age > oldestAge) {
                oldestAge = decal.age;
                oldestIndex = index;
            }
        });
        this.decalPool.splice(oldestIndex, 1);
    }
    loadDecalTexture(surfaceType) {
        // Carrega textura específica para o tipo de superfície
        const textureKey = `decal_${surfaceType}`;
        return this.getTextureFromCache(textureKey) || this.createDefaultDecalTexture();
    }
    getTextureFromCache(key) {
        // Implementação do cache de texturas
        return null; // TODO: Implementar cache de texturas
    }
    createDefaultDecalTexture() {
        // Cria textura padrão para decals
        return {
        // Implementação da textura padrão
        };
    }
    updateShellPhysics(deltaTime) {
        this.shellPool
            .filter(shell => shell.inUse)
            .forEach(shell => {
            // Atualiza física dos cartuchos ativos
            const physics = shell.physics;
            physics.velocity.y -= 9.81 * deltaTime; // Gravidade
            physics.velocity.x *= (1 - physics.drag * deltaTime);
            physics.velocity.z *= (1 - physics.drag * deltaTime);
            shell.mesh.position.x += physics.velocity.x * deltaTime;
            shell.mesh.position.y += physics.velocity.y * deltaTime;
            shell.mesh.position.z += physics.velocity.z * deltaTime;
            // Rotação do cartucho
            shell.mesh.rotation.x += physics.angularVelocity.x * deltaTime;
            shell.mesh.rotation.y += physics.angularVelocity.y * deltaTime;
            shell.mesh.rotation.z += physics.angularVelocity.z * deltaTime;
        });
    }
    getMuzzleFlashConfig(weaponType) {
        // Configurações específicas por tipo de arma
        const configs = {
            rifle: {
                maxParticles: 8,
                duration: 0.1,
                scale: 1.2,
                color: { r: 1.0, g: 0.7, b: 0.3, a: 0.8 }
            },
            pistol: {
                maxParticles: 5,
                duration: 0.08,
                scale: 0.8,
                color: { r: 1.0, g: 0.6, b: 0.2, a: 0.7 }
            },
            shotgun: {
                maxParticles: 12,
                duration: 0.15,
                scale: 1.5,
                color: { r: 1.0, g: 0.8, b: 0.4, a: 0.9 }
            }
        };
        return configs[weaponType] || configs.rifle;
    }
    update(deltaTime) {
        // Atualiza física dos cartuchos
        this.updateShellPhysics(deltaTime);
        // Atualiza idade dos decals
        this.decalPool.forEach(decal => {
            decal.age += deltaTime;
        });
        // Remove decals expirados
        this.decalPool = this.decalPool.filter(decal => decal.age < this.config.impactEffects.decalLifetime);
    }
}
exports.WeaponVFXSystem = WeaponVFXSystem;
WeaponVFXSystem.MAX_MUZZLE_FLASH_PARTICLES = 10;
WeaponVFXSystem.MAX_SHELL_CASINGS = 30;
WeaponVFXSystem.MAX_IMPACT_PARTICLES = 15;
WeaponVFXSystem.MAX_DECALS = 100;
//# sourceMappingURL=WeaponVFXSystem.js.map