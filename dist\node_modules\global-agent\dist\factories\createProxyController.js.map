{"version": 3, "sources": ["../../src/factories/createProxyController.js"], "names": ["log", "<PERSON><PERSON>", "child", "namespace", "KNOWN_PROPERTY_NAMES", "Proxy", "HTTP_PROXY", "HTTPS_PROXY", "NO_PROXY", "set", "subject", "name", "value", "includes", "Error", "info", "change", "newConfiguration"], "mappings": ";;;;;;;AAEA;;;;AAQA,MAAMA,GAAG,GAAGC,gBAAOC,KAAP,CAAa;AACvBC,EAAAA,SAAS,EAAE;AADY,CAAb,CAAZ;;AAIA,MAAMC,oBAAoB,GAAG,CAC3B,YAD2B,EAE3B,aAF2B,EAG3B,UAH2B,CAA7B;;oCAM0C;AACxC;AACA,SAAO,IAAIC,KAAJ,CAAU;AACfC,IAAAA,UAAU,EAAE,IADG;AAEfC,IAAAA,WAAW,EAAE,IAFE;AAGfC,IAAAA,QAAQ,EAAE;AAHK,GAAV,EAIJ;AACDC,IAAAA,GAAG,EAAE,CAACC,OAAD,EAAUC,IAAV,EAAgBC,KAAhB,KAA0B;AAC7B,UAAI,CAACR,oBAAoB,CAACS,QAArB,CAA8BF,IAA9B,CAAL,EAA0C;AACxC,cAAM,IAAIG,KAAJ,CAAU,sCAAsCH,IAAtC,GAA6C,IAAvD,CAAN;AACD;;AAEDD,MAAAA,OAAO,CAACC,IAAD,CAAP,GAAgBC,KAAhB;AAEAZ,MAAAA,GAAG,CAACe,IAAJ,CAAS;AACPC,QAAAA,MAAM,EAAE;AACNL,UAAAA,IADM;AAENC,UAAAA;AAFM,SADD;AAKPK,QAAAA,gBAAgB,EAAEP;AALX,OAAT,EAMG,uBANH;AAQA,aAAO,IAAP;AACD;AAjBA,GAJI,CAAP;AAuBD,C", "sourcesContent": ["// @flow\n\nimport Logger from '../Logger';\n\ntype ProxyControllerType = {|\n  HTTP_PROXY: string | null,\n  HTTPS_PROXY: string | null,\n  NO_PROXY: string | null,\n|};\n\nconst log = Logger.child({\n  namespace: 'createProxyController',\n});\n\nconst KNOWN_PROPERTY_NAMES = [\n  'HTTP_PROXY',\n  'HTTPS_PROXY',\n  'NO_PROXY',\n];\n\nexport default (): ProxyControllerType => {\n  // eslint-disable-next-line fp/no-proxy\n  return new Proxy({\n    HTTP_PROXY: null,\n    HTTPS_PROXY: null,\n    NO_PROXY: null,\n  }, {\n    set: (subject, name, value) => {\n      if (!KNOWN_PROPERTY_NAMES.includes(name)) {\n        throw new Error('Cannot set an unmapped property \"' + name + '\".');\n      }\n\n      subject[name] = value;\n\n      log.info({\n        change: {\n          name,\n          value,\n        },\n        newConfiguration: subject,\n      }, 'configuration changed');\n\n      return true;\n    },\n  });\n};\n"], "file": "createProxyController.js"}