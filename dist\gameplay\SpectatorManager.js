"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpectatorManager = void 0;
class SpectatorManager {
    constructor(replayManager) {
        this.replayManager = replayManager;
        this.cameras = new Map();
        this.activeSpectators = new Map();
        this.broadcastBuffer = [];
        this.currentPerspective = null;
        this.isLive = false;
        this.initializeDefaultCameras();
    }
    initializeDefaultCameras() {
        // Câmeras táticas pré-definidas
        const tacticalPositions = [
            { id: 'overview', position: { x: 0, y: 500, z: 0 }, rotation: { x: -90, y: 0, z: 0 } },
            { id: 'bombsiteA', position: { x: 100, y: 50, z: 100 }, rotation: { x: -30, y: 45, z: 0 } },
            { id: 'bombsiteB', position: { x: -100, y: 50, z: -100 }, rotation: { x: -30, y: 225, z: 0 } }
        ];
        tacticalPositions.forEach(cam => {
            this.cameras.set(cam.id, {
                position: cam.position,
                rotation: cam.rotation,
                fov: 90,
                interpolation: 'smooth'
            });
        });
    }
    startSpectating(spectatorId, config) {
        if (this.activeSpectators.size >= SpectatorManager.MAX_SPECTATORS) {
            return false;
        }
        const defaultConfig = {
            delay: SpectatorManager.MIN_SPECTATOR_DELAY,
            allowedPerspectives: ['firstPerson', 'thirdPerson', 'freeCam'],
            customOverlays: false,
            allowedTeams: ['all']
        };
        this.activeSpectators.set(spectatorId, {
            ...defaultConfig,
            ...config
        });
        return true;
    }
    stopSpectating(spectatorId) {
        this.activeSpectators.delete(spectatorId);
    }
    switchPerspective(spectatorId, targetId, perspective) {
        const config = this.activeSpectators.get(spectatorId);
        if (!config || !config.allowedPerspectives.includes(perspective)) {
            return false;
        }
        this.currentPerspective = targetId;
        return true;
    }
    updateCamera(cameraId, config) {
        const currentConfig = this.cameras.get(cameraId) || {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            fov: 90,
            interpolation: 'linear'
        };
        this.cameras.set(cameraId, {
            ...currentConfig,
            ...config
        });
    }
    getCameraConfig(cameraId) {
        return this.cameras.get(cameraId) || null;
    }
    broadcastFrame(frame) {
        if (!this.isLive)
            return;
        // Adiciona frame ao buffer com timestamp
        this.broadcastBuffer.push(frame);
        // Limpa frames antigos baseado no delay máximo
        const maxDelay = Math.max(...Array.from(this.activeSpectators.values())
            .map(config => config.delay));
        while (this.broadcastBuffer.length > maxDelay * 60) { // 60 fps
            this.broadcastBuffer.shift();
        }
    }
    getDelayedFrame(spectatorId) {
        const config = this.activeSpectators.get(spectatorId);
        if (!config)
            return null;
        const frameIndex = Math.max(0, this.broadcastBuffer.length - (config.delay * 60));
        return this.broadcastBuffer[frameIndex] || null;
    }
    addCustomCamera(id, config) {
        this.cameras.set(id, config);
    }
    removeCustomCamera(id) {
        this.cameras.delete(id);
    }
    interpolateCamera(from, to, progress) {
        const lerp = (a, b, t) => a + (b - a) * t;
        return {
            position: {
                x: lerp(from.position.x, to.position.x, progress),
                y: lerp(from.position.y, to.position.y, progress),
                z: lerp(from.position.z, to.position.z, progress)
            },
            rotation: {
                x: lerp(from.rotation.x, to.rotation.x, progress),
                y: lerp(from.rotation.y, to.rotation.y, progress),
                z: lerp(from.rotation.z, to.rotation.z, progress)
            },
            fov: lerp(from.fov, to.fov, progress),
            interpolation: to.interpolation
        };
    }
    setLiveMode(isLive) {
        this.isLive = isLive;
        if (!isLive) {
            this.broadcastBuffer = [];
        }
    }
    getActiveSpectators() {
        return Array.from(this.activeSpectators.keys());
    }
    getCurrentPerspective() {
        return this.currentPerspective;
    }
    isSpectating(spectatorId) {
        return this.activeSpectators.has(spectatorId);
    }
}
exports.SpectatorManager = SpectatorManager;
SpectatorManager.MIN_SPECTATOR_DELAY = 30; // 30 segundos mínimo
SpectatorManager.CAMERA_SMOOTH_FACTOR = 0.15;
SpectatorManager.MAX_SPECTATORS = 1000;
//# sourceMappingURL=SpectatorManager.js.map