"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InitialMapProducer = void 0;
const TerrainGenerator_1 = require("./TerrainGenerator");
const POIGenerator_1 = require("./POIGenerator");
const MapLayoutManager_1 = require("./MapLayoutManager");
const ContentProductionSystem_1 = require("../content/ContentProductionSystem");
const AssetProductionManager_1 = require("../content/AssetProductionManager");
const GameAudioSystem_1 = require("../audio/GameAudioSystem");
const PerformanceMetrics_1 = require("../rendering/PerformanceMetrics");
class InitialMapProducer {
    constructor(config, progressCallback) {
        this.terrainGenerator = new TerrainGenerator_1.TerrainGenerator({
            size: 2048, // 2km
            resolution: 2, // 2m por vértice
            maxHeight: 200,
            minHeight: 0,
            smoothingPasses: 3
        });
        this.poiGenerator = new POIGenerator_1.POIGenerator();
        this.mapLayout = new MapLayoutManager_1.MapLayoutManager();
        this.contentSystem = new ContentProductionSystem_1.ContentProductionSystem();
        this.assetManager = new AssetProductionManager_1.AssetProductionManager();
        this.audioSystem = new GameAudioSystem_1.GameAudioSystem(config.audio);
        this.metrics = new PerformanceMetrics_1.PerformanceMetrics();
        this.onProgressUpdate = progressCallback;
    }
    async produceInitialMap() {
        try {
            this.updateProgress({
                terrainProgress: 0,
                poisCompleted: 0,
                totalPOIs: 0,
                currentPhase: 'Iniciando produção do mapa',
                performanceMetrics: null
            });
            // Define as regiões principais do mapa
            const regions = this.defineMainRegions();
            const totalPOIs = regions.reduce((sum, r) => sum + r.poiCount, 0);
            // Gera o terreno base
            await this.generateBaseTerrain(regions);
            // Gera POIs iniciais
            let completedPOIs = 0;
            for (const region of regions) {
                for (let i = 0; i < region.poiCount; i++) {
                    this.updateProgress({
                        terrainProgress: 100,
                        poisCompleted: completedPOIs,
                        totalPOIs,
                        currentPhase: `Gerando POI ${completedPOIs + 1}/${totalPOIs} (${region.type})`,
                        performanceMetrics: null
                    });
                    const position = this.calculatePOIPosition(region, i);
                    const template = this.createPOITemplate(region.type);
                    const poi = await this.poiGenerator.generatePOI(region.type, position, template);
                    await this.contentSystem.queueEnvironmentProduction({
                        models: [...poi.assets.buildings, ...poi.assets.props],
                        textures: poi.assets.textures,
                        ambientSounds: poi.assets.ambientSounds
                    });
                    completedPOIs++;
                }
            }
            // Configura áudio ambiental
            await this.setupEnvironmentalAudio(regions);
            // Otimiza e valida performance
            await this.optimizeAndValidate();
            return {
                success: true,
                metrics: this.metrics.getMapProductionStats()
            };
        }
        catch (error) {
            console.error('Erro na produção do mapa:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Erro desconhecido'
            };
        }
    }
    defineMainRegions() {
        return [
            {
                type: 'urban',
                center: { x: 1024, y: 0, z: 1024 },
                radius: 400,
                poiCount: 3
            },
            {
                type: 'military',
                center: { x: 512, y: 0, z: 512 },
                radius: 300,
                poiCount: 2
            },
            {
                type: 'industrial',
                center: { x: 1536, y: 0, z: 512 },
                radius: 350,
                poiCount: 2
            },
            {
                type: 'forest',
                center: { x: 1024, y: 0, z: 1536 },
                radius: 450,
                poiCount: 2
            }
        ];
    }
    async generateBaseTerrain(regions) {
        this.updateProgress({
            terrainProgress: 0,
            poisCompleted: 0,
            totalPOIs: regions.reduce((sum, r) => sum + r.poiCount, 0),
            currentPhase: 'Gerando terreno base',
            performanceMetrics: null
        });
        const terrain = await this.terrainGenerator.generateTerrain(regions);
        // Processa o terreno através do sistema de produção
        await this.contentSystem.queueEnvironmentProduction({
            models: [terrain.geometry],
            textures: terrain.textures,
            ambientSounds: []
        });
        this.updateProgress({
            terrainProgress: 100,
            poisCompleted: 0,
            totalPOIs: regions.reduce((sum, r) => sum + r.poiCount, 0),
            currentPhase: 'Terreno base concluído',
            performanceMetrics: this.metrics.getMapProductionStats()
        });
    }
    async generateInitialPOIs(regions) {
        let completedPOIs = 0;
        const totalPOIs = regions.reduce((sum, r) => sum + r.poiCount, 0);
        for (const region of regions) {
            for (let i = 0; i < region.poiCount; i++) {
                this.updateProgress({
                    terrainProgress: 100,
                    poisCompleted: completedPOIs,
                    totalPOIs,
                    currentPhase: `Gerando POI ${completedPOIs + 1}/${totalPOIs}`,
                    performanceMetrics: this.metrics.getMapProductionStats()
                });
                const poiPosition = this.calculatePOIPosition(region, i);
                const template = this.createPOITemplate(region.type);
                const poi = await this.poiGenerator.generatePOI(region.type, poiPosition, template);
                // Processa assets do POI
                await this.contentSystem.queueEnvironmentProduction({
                    models: [...poi.assets.buildings, ...poi.assets.props],
                    textures: poi.assets.textures,
                    ambientSounds: poi.assets.ambientSounds
                });
                completedPOIs++;
            }
        }
    }
    calculatePOIPosition(region, index) {
        const angle = (index / region.poiCount) * Math.PI * 2;
        const distance = region.radius * 0.7; // 70% do raio para manter dentro da região
        return {
            x: region.center.x + Math.cos(angle) * distance,
            y: 0,
            z: region.center.z + Math.sin(angle) * distance
        };
    }
    createPOITemplate(type) {
        const templates = {
            urban: {
                type: 'urban',
                size: { width: 200, length: 200, height: 50 },
                buildingDensity: 0.7,
                propDensity: 0.4,
                coverPercentage: 0.6,
                verticalityFactor: 0.8
            },
            military: {
                type: 'military',
                size: { width: 250, length: 250, height: 30 },
                buildingDensity: 0.5,
                propDensity: 0.6,
                coverPercentage: 0.8,
                verticalityFactor: 0.4
            },
            industrial: {
                type: 'industrial',
                size: { width: 300, length: 300, height: 40 },
                buildingDensity: 0.6,
                propDensity: 0.5,
                coverPercentage: 0.7,
                verticalityFactor: 0.6
            },
            forest: {
                type: 'forest',
                size: { width: 400, length: 400, height: 35 },
                buildingDensity: 0.2,
                propDensity: 0.8,
                coverPercentage: 0.9,
                verticalityFactor: 0.3
            }
        };
        return templates[type];
    }
    async setupEnvironmentalAudio(regions) {
        this.updateProgress({
            terrainProgress: 100,
            poisCompleted: -1,
            totalPOIs: -1,
            currentPhase: 'Configurando áudio ambiental',
            performanceMetrics: null
        });
        for (const region of regions) {
            // Configura áudio ambiental para a região
            this.audioSystem.configureAmbientZone({
                id: `region-${region.type}`,
                position: region.center,
                size: {
                    width: region.radius * 2,
                    length: region.radius * 2,
                    height: 1000 // Altura suficiente para cobrir a área verticalmente
                },
                sounds: this.getAmbientSoundsForType(region.type),
                reverb: `${region.type}_reverb`,
                transitionDistance: region.radius * 0.3 // Transição suave nas bordas
            });
        }
        this.updateProgress({
            terrainProgress: 100,
            poisCompleted: -1,
            totalPOIs: -1,
            currentPhase: 'Áudio ambiental configurado',
            performanceMetrics: null
        });
    }
    getAmbientSoundsForType(type) {
        const soundSets = {
            urban: [
                'city_ambience_day',
                'traffic_continuous',
                'distant_voices',
                'construction_occasional'
            ],
            military: [
                'base_ambience',
                'distant_vehicles',
                'radio_chatter',
                'metal_impacts'
            ],
            industrial: [
                'factory_machinery',
                'industrial_hum',
                'metal_works',
                'steam_release'
            ],
            forest: [
                'forest_ambience_day',
                'wind_through_trees',
                'birds_ambient',
                'distant_water'
            ]
        };
        return [...soundSets[type]];
    }
    async optimizeAndValidate() {
        this.updateProgress({
            terrainProgress: 100,
            poisCompleted: -1,
            totalPOIs: -1,
            currentPhase: 'Otimizando e validando performance',
            performanceMetrics: null
        });
        // Executa otimizações básicas
        await this.contentSystem.optimizeGeometry();
        await this.contentSystem.optimizeTextures();
        await this.contentSystem.optimizeLighting();
        await this.contentSystem.optimizeAudio();
        // Aguarda alguns frames para coletar métricas
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Coleta métricas
        const finalMetrics = this.metrics.getMapProductionStats();
        const targetFPS = 999;
        // Verifica se atinge a meta de FPS
        if (finalMetrics.averageFPS < targetFPS * 0.95) { // 5% de tolerância
            throw new Error(`Performance abaixo da meta: ${finalMetrics.averageFPS.toFixed(2)} FPS ` +
                `(mínimo: ${(targetFPS * 0.95).toFixed(2)} FPS)`);
        }
        this.updateProgress({
            terrainProgress: 100,
            poisCompleted: -1,
            totalPOIs: -1,
            currentPhase: 'Produção inicial concluída com sucesso',
            performanceMetrics: finalMetrics
        });
    }
    updateProgress(progress) {
        if (this.onProgressUpdate) {
            this.onProgressUpdate(progress);
        }
    }
}
exports.InitialMapProducer = InitialMapProducer;
//# sourceMappingURL=InitialMapProducer.js.map