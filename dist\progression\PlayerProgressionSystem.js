"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerProgressionSystem = void 0;
const events_1 = require("events");
class PlayerProgressionSystem extends events_1.EventEmitter {
    constructor() {
        super();
        this.playerProfiles = new Map();
        this.experienceTable = [];
        this.unlockDatabase = [];
        this.achievementDatabase = [];
        this.initializeExperienceTable();
        this.initializeUnlocks();
        this.initializeAchievements();
        console.log('📈 PlayerProgressionSystem inicializado');
    }
    initializeExperienceTable() {
        // Tabela de experiência exponencial para 100 níveis
        for (let level = 1; level <= 100; level++) {
            const baseXP = 1000;
            const multiplier = Math.pow(1.15, level - 1);
            this.experienceTable.push(Math.floor(baseXP * multiplier));
        }
    }
    initializeUnlocks() {
        this.unlockDatabase = [
            // Armas
            { id: 'assault_rifle_m4', type: 'weapon', name: 'M4A1 Assault Rifle', description: 'Rifle de assalto versátil', requirement: { type: 'level', value: 1 }, unlocked: false },
            { id: 'smg_mp5', type: 'weapon', name: 'MP5 SMG', description: 'Submetralhadora rápida', requirement: { type: 'level', value: 5 }, unlocked: false },
            { id: 'sniper_awp', type: 'weapon', name: 'AWP Sniper', description: 'Rifle de precisão letal', requirement: { type: 'level', value: 15 }, unlocked: false },
            { id: 'shotgun_m870', type: 'weapon', name: 'M870 Shotgun', description: 'Espingarda de combate próximo', requirement: { type: 'level', value: 10 }, unlocked: false },
            // Attachments
            { id: 'red_dot_sight', type: 'attachment', name: 'Red Dot Sight', description: 'Mira de ponto vermelho', requirement: { type: 'kills', value: 50, weapon: 'assault_rifle_m4' }, unlocked: false },
            { id: 'suppressor', type: 'attachment', name: 'Suppressor', description: 'Silenciador de som', requirement: { type: 'kills', value: 100, weapon: 'assault_rifle_m4' }, unlocked: false },
            { id: 'extended_mag', type: 'attachment', name: 'Extended Magazine', description: 'Carregador estendido', requirement: { type: 'kills', value: 75, weapon: 'smg_mp5' }, unlocked: false },
            // Skins
            { id: 'skin_desert_camo', type: 'skin', name: 'Desert Camo', description: 'Camuflagem do deserto', requirement: { type: 'level', value: 8 }, unlocked: false },
            { id: 'skin_urban_camo', type: 'skin', name: 'Urban Camo', description: 'Camuflagem urbana', requirement: { type: 'level', value: 12 }, unlocked: false },
            { id: 'skin_forest_camo', type: 'skin', name: 'Forest Camo', description: 'Camuflagem da floresta', requirement: { type: 'level', value: 18 }, unlocked: false },
            // Perks
            { id: 'perk_fast_reload', type: 'perk', name: 'Fast Reload', description: 'Recarga 25% mais rápida', requirement: { type: 'level', value: 6 }, unlocked: false },
            { id: 'perk_extra_health', type: 'perk', name: 'Extra Health', description: '+25 pontos de vida', requirement: { type: 'level', value: 14 }, unlocked: false },
            { id: 'perk_silent_steps', type: 'perk', name: 'Silent Steps', description: 'Passos silenciosos', requirement: { type: 'level', value: 20 }, unlocked: false },
            // Títulos e Emblemas
            { id: 'title_rookie', type: 'title', name: 'Rookie', description: 'Novato no campo de batalha', requirement: { type: 'level', value: 1 }, unlocked: false },
            { id: 'title_veteran', type: 'title', name: 'Veteran', description: 'Veterano experiente', requirement: { type: 'level', value: 25 }, unlocked: false },
            { id: 'emblem_skull', type: 'emblem', name: 'Skull Emblem', description: 'Emblema de caveira', requirement: { type: 'kills', value: 500 }, unlocked: false },
        ];
    }
    initializeAchievements() {
        this.achievementDatabase = [
            {
                id: 'first_kill', name: 'First Blood', description: 'Consiga sua primeira eliminação',
                category: 'combat', rarity: 'common', progress: 0, maxProgress: 1, completed: false,
                reward: { experience: 100 }
            },
            {
                id: 'kill_streak_5', name: 'Kill Streak', description: 'Elimine 5 inimigos seguidos',
                category: 'combat', rarity: 'rare', progress: 0, maxProgress: 5, completed: false,
                reward: { experience: 500, currency: 100 }
            },
            {
                id: 'headshot_master', name: 'Headshot Master', description: 'Consiga 100 headshots',
                category: 'combat', rarity: 'epic', progress: 0, maxProgress: 100, completed: false,
                reward: { experience: 1000, currency: 250, unlock: 'skin_gold_camo' }
            },
            {
                id: 'team_player', name: 'Team Player', description: 'Consiga 50 assistências',
                category: 'teamwork', rarity: 'rare', progress: 0, maxProgress: 50, completed: false,
                reward: { experience: 750, currency: 150 }
            },
            {
                id: 'level_25', name: 'Rising Star', description: 'Alcance o nível 25',
                category: 'progression', rarity: 'epic', progress: 0, maxProgress: 25, completed: false,
                reward: { experience: 2000, currency: 500 }
            },
            {
                id: 'win_streak_10', name: 'Unstoppable', description: 'Vença 10 partidas seguidas',
                category: 'special', rarity: 'legendary', progress: 0, maxProgress: 10, completed: false,
                reward: { experience: 5000, currency: 1000, unlock: 'title_unstoppable' }
            }
        ];
    }
    createPlayerProfile(playerId, username) {
        const profile = {
            playerId,
            username,
            level: {
                level: 1,
                experience: 0,
                experienceToNext: this.experienceTable[0],
                totalExperience: 0,
                prestige: 0
            },
            stats: {
                kills: 0,
                deaths: 0,
                assists: 0,
                wins: 0,
                losses: 0,
                playtime: 0,
                headshots: 0,
                accuracy: 0,
                damageDealt: 0,
                damageReceived: 0
            },
            unlocks: this.unlockDatabase.map(unlock => ({ ...unlock })),
            achievements: this.achievementDatabase.map(achievement => ({ ...achievement })),
            selectedLoadout: {
                primaryWeapon: 'assault_rifle_m4',
                secondaryWeapon: 'pistol_glock',
                equipment: ['grenade_frag', 'smoke_grenade'],
                perks: ['perk_fast_reload'],
                skin: 'skin_default',
                emblem: 'emblem_default',
                title: 'title_rookie'
            },
            currency: {
                coins: 1000, // Moedas iniciais
                premiumCurrency: 0
            },
            createdAt: new Date(),
            lastPlayed: new Date()
        };
        // Desbloqueia itens iniciais
        this.checkUnlocks(profile);
        this.playerProfiles.set(playerId, profile);
        console.log(`👤 Perfil criado para ${username} (ID: ${playerId})`);
        return profile;
    }
    addExperience(playerId, amount, source = 'gameplay') {
        const profile = this.playerProfiles.get(playerId);
        if (!profile)
            return;
        const oldLevel = profile.level.level;
        profile.level.experience += amount;
        profile.level.totalExperience += amount;
        // Verifica level up
        while (profile.level.experience >= profile.level.experienceToNext && profile.level.level < 100) {
            profile.level.experience -= profile.level.experienceToNext;
            profile.level.level++;
            if (profile.level.level < 100) {
                profile.level.experienceToNext = this.experienceTable[profile.level.level - 1];
            }
            else {
                profile.level.experienceToNext = 0;
            }
        }
        if (profile.level.level > oldLevel) {
            console.log(`🎉 ${profile.username} subiu para o nível ${profile.level.level}!`);
            this.emit('levelUp', { playerId, oldLevel, newLevel: profile.level.level, profile });
            // Verifica novos desbloqueios
            this.checkUnlocks(profile);
        }
        this.emit('experienceGained', { playerId, amount, source, profile });
    }
    updateStats(playerId, statUpdates) {
        const profile = this.playerProfiles.get(playerId);
        if (!profile)
            return;
        Object.assign(profile.stats, statUpdates);
        profile.lastPlayed = new Date();
        // Verifica progresso de conquistas
        this.checkAchievements(profile);
        // Verifica desbloqueios baseados em estatísticas
        this.checkUnlocks(profile);
        this.emit('statsUpdated', { playerId, updates: statUpdates, profile });
    }
    checkUnlocks(profile) {
        for (const unlock of profile.unlocks) {
            if (unlock.unlocked)
                continue;
            let shouldUnlock = false;
            switch (unlock.requirement.type) {
                case 'level':
                    shouldUnlock = profile.level.level >= unlock.requirement.value;
                    break;
                case 'kills':
                    if (unlock.requirement.weapon) {
                        // Implementar tracking de kills por arma
                        shouldUnlock = profile.stats.kills >= unlock.requirement.value;
                    }
                    else {
                        shouldUnlock = profile.stats.kills >= unlock.requirement.value;
                    }
                    break;
                case 'wins':
                    shouldUnlock = profile.stats.wins >= unlock.requirement.value;
                    break;
                case 'playtime':
                    shouldUnlock = profile.stats.playtime >= unlock.requirement.value;
                    break;
            }
            if (shouldUnlock) {
                unlock.unlocked = true;
                unlock.unlockedAt = new Date();
                console.log(`🔓 ${profile.username} desbloqueou: ${unlock.name}`);
                this.emit('unlockEarned', { playerId: profile.playerId, unlock, profile });
            }
        }
    }
    checkAchievements(profile) {
        for (const achievement of profile.achievements) {
            if (achievement.completed)
                continue;
            let newProgress = achievement.progress;
            switch (achievement.id) {
                case 'first_kill':
                    newProgress = Math.min(profile.stats.kills, 1);
                    break;
                case 'headshot_master':
                    newProgress = profile.stats.headshots;
                    break;
                case 'team_player':
                    newProgress = profile.stats.assists;
                    break;
                case 'level_25':
                    newProgress = profile.level.level;
                    break;
                // Adicionar mais lógica para outras conquistas
            }
            if (newProgress > achievement.progress) {
                achievement.progress = newProgress;
                if (achievement.progress >= achievement.maxProgress && !achievement.completed) {
                    achievement.completed = true;
                    achievement.completedAt = new Date();
                    // Aplica recompensas
                    this.addExperience(profile.playerId, achievement.reward.experience, 'achievement');
                    if (achievement.reward.currency) {
                        profile.currency.coins += achievement.reward.currency;
                    }
                    console.log(`🏆 ${profile.username} completou a conquista: ${achievement.name}`);
                    this.emit('achievementCompleted', { playerId: profile.playerId, achievement, profile });
                }
            }
        }
    }
    getPlayerProfile(playerId) {
        return this.playerProfiles.get(playerId);
    }
    getLeaderboard(category, limit = 10) {
        const profiles = Array.from(this.playerProfiles.values());
        const sorted = profiles.sort((a, b) => {
            switch (category) {
                case 'level':
                    return b.level.totalExperience - a.level.totalExperience;
                case 'kills':
                    return b.stats.kills - a.stats.kills;
                case 'wins':
                    return b.stats.wins - a.stats.wins;
                case 'kdr':
                    const kdrA = a.stats.deaths > 0 ? a.stats.kills / a.stats.deaths : a.stats.kills;
                    const kdrB = b.stats.deaths > 0 ? b.stats.kills / b.stats.deaths : b.stats.kills;
                    return kdrB - kdrA;
                default:
                    return 0;
            }
        });
        return sorted.slice(0, limit).map((profile, index) => {
            let value;
            switch (category) {
                case 'level':
                    value = profile.level.level;
                    break;
                case 'kills':
                    value = profile.stats.kills;
                    break;
                case 'wins':
                    value = profile.stats.wins;
                    break;
                case 'kdr':
                    value = profile.stats.deaths > 0 ? profile.stats.kills / profile.stats.deaths : profile.stats.kills;
                    break;
                default:
                    value = 0;
            }
            return {
                rank: index + 1,
                profile,
                value
            };
        });
    }
    getProgressionStats() {
        const profiles = Array.from(this.playerProfiles.values());
        return {
            totalPlayers: profiles.length,
            averageLevel: profiles.reduce((sum, p) => sum + p.level.level, 0) / profiles.length,
            totalPlaytime: profiles.reduce((sum, p) => sum + p.stats.playtime, 0),
            totalKills: profiles.reduce((sum, p) => sum + p.stats.kills, 0),
            totalMatches: profiles.reduce((sum, p) => sum + p.stats.wins + p.stats.losses, 0)
        };
    }
}
exports.PlayerProgressionSystem = PlayerProgressionSystem;
exports.default = PlayerProgressionSystem;
//# sourceMappingURL=PlayerProgressionSystem.js.map