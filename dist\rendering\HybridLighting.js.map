{"version": 3, "file": "HybridLighting.js", "sourceRoot": "", "sources": ["../../src/rendering/HybridLighting.ts"], "names": [], "mappings": ";;;AAiBA,IAAK,cAIJ;AAJD,WAAK,cAAc;IACf,yEAAe,CAAA;IACf,mEAAY,CAAA;IACZ,iEAAW,CAAA,CAAM,uCAAuC;AAC5D,CAAC,EAJI,cAAc,KAAd,cAAc,QAIlB;AAED,MAAa,oBAAoB;IAS7B;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,kEAAkE;QAClE,MAAM,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,yBAAyB;QACnE,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,yBAAyB;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC;YACjE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACrB,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC1B,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;gBACvB,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,+BAA+B;gBACtD,QAAQ,EAAE,CAAC,GAAG,GAAG,CAAC,kCAAkC;aACvD,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEM,eAAe,CAAC,EAAU,EAAE,KAAY;QAC3C,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,aAAa,CAAC,KAAY;QAC9B,OAAO;YACH,GAAG,KAAK;YACR,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,+BAA+B;YACpE,YAAY,EAAE,KAAK,CAAC,SAAS,GAAG,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,iDAAiD;SAC9G,CAAC;IACN,CAAC;IAEM,qBAAqB,CAAC,cAAuB,EAAE,cAAqB;QACvE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAChC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QACtC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAEO,UAAU,CAAC,cAAuB;QACtC,kEAAkE;QAClE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,iDAAiD;gBACjD,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;YACxB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,cAAuB;QAC5C,0CAA0C;QAC1C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YACrC,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC;gBAAE,OAAO;YAExD,mDAAmD;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;YACnE,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC3C,OAAO,CAAC,CAAC;YACb,CAAC;QACL,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,KAAY,EAAE,OAAsB;QACxD,+CAA+C;QAC/C,yCAAyC;QACzC,mCAAmC;QACnC,wCAAwC;IAC5C,CAAC;IAEM,gBAAgB,CAAC,EAAU,EAAE,IAAkB;QAClD,yDAAyD;QACzD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,gBAAgB,CAAC,IAAkB;QACvC,kDAAkD;QAClD,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,iDAAiD;QACjD,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YACrC,SAAS,CAAC,MAAM,GAAG,cAAc,CAAC,eAAe,CAAC;QACtD,CAAC;aAAM,CAAC;YACJ,SAAS,CAAC,MAAM,GAAG,cAAc,CAAC,WAAW,CAAC;QAClD,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,2BAA2B;QAC/B,sCAAsC;QACtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,cAAqB;QAC/C,wDAAwD;QACxD,wCAAwC;QACxC,2BAA2B;QAC3B,oCAAoC;IACxC,CAAC;IAED,cAAc;IACN,QAAQ,CAAC,EAAW,EAAE,EAAW;QACrC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;;AAtIL,oDAuIC;AAtI2B,uCAAkB,GAAG,EAAE,CAAC;AACxB,yCAAoB,GAAG,CAAC,CAAC;AACzB,kCAAa,GAAG,IAAI,CAAC"}