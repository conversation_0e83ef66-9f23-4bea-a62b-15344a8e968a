"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIManager = void 0;
class AIManager {
    constructor(mapManager) {
        this.bots = new Map();
        this.squads = new Map();
        this.mapManager = mapManager;
        this.workerPool = this.initializeWorkerPool();
    }
    initializeWorkerPool() {
        const cores = navigator.hardwareConcurrency || 4;
        const workers = [];
        for (let i = 0; i < cores - 1; i++) { // Deixa um core livre para o thread principal
            const worker = new Worker('./AIWorker.ts');
            workers.push(worker);
        }
        return workers;
    }
    update(deltaTime) {
        // Distribui atualizações de bots entre workers
        const botsPerWorker = Math.ceil(this.bots.size / this.workerPool.length);
        let currentBotIndex = 0;
        for (const worker of this.workerPool) {
            const botSlice = Array.from(this.bots.entries())
                .slice(currentBotIndex, currentBotIndex + botsPerWorker);
            worker.postMessage({
                type: 'update',
                bots: botSlice,
                deltaTime
            });
            currentBotIndex += botsPerWorker;
        }
    }
    addBot(botId, position, squadId) {
        const bot = new BotAI(botId, position, this.mapManager);
        this.bots.set(botId, bot);
        if (squadId) {
            this.assignBotToSquad(botId, squadId);
        }
    }
    removeBot(botId) {
        const bot = this.bots.get(botId);
        if (bot) {
            this.removeFromSquad(botId);
            this.bots.delete(botId);
        }
    }
    assignBotToSquad(botId, squadId) {
        let squad = this.squads.get(squadId);
        if (!squad) {
            squad = {
                id: squadId,
                members: [],
                leader: botId,
                formation: 'spread',
                objective: {
                    type: 'loot',
                    position: { x: 0, y: 0, z: 0 },
                    priority: 1
                }
            };
            this.squads.set(squadId, squad);
        }
        squad.members.push(botId);
        if (squad.members.length === 1) {
            squad.leader = botId;
        }
    }
    removeFromSquad(botId) {
        for (const [squadId, squad] of this.squads) {
            const index = squad.members.indexOf(botId);
            if (index !== -1) {
                squad.members.splice(index, 1);
                if (squad.leader === botId && squad.members.length > 0) {
                    squad.leader = squad.members[0];
                }
                if (squad.members.length === 0) {
                    this.squads.delete(squadId);
                }
                break;
            }
        }
    }
    getBotLODLevel(botPosition, playerPosition) {
        const distance = this.calculateDistance(botPosition, playerPosition);
        for (let i = 0; i < AIManager.BOT_LOD_DISTANCES.length; i++) {
            if (distance <= AIManager.BOT_LOD_DISTANCES[i]) {
                return i;
            }
        }
        return AIManager.BOT_LOD_DISTANCES.length;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    getSquadFormationPosition(squadId, botId) {
        const squad = this.squads.get(squadId);
        if (!squad)
            return null;
        const leader = this.bots.get(squad.leader);
        if (!leader)
            return null;
        const memberIndex = squad.members.indexOf(botId);
        if (memberIndex === -1)
            return null;
        // Calcula posição baseada na formação do esquadrão
        const offset = this.calculateFormationOffset(squad.formation, memberIndex);
        return {
            x: leader.position.x + offset.x,
            y: leader.position.y,
            z: leader.position.z + offset.z
        };
    }
    calculateFormationOffset(formation, index) {
        const spacing = 3; // metros entre membros
        switch (formation) {
            case 'spread':
                return {
                    x: Math.cos(index * Math.PI * 0.5) * spacing,
                    y: 0,
                    z: Math.sin(index * Math.PI * 0.5) * spacing
                };
            case 'line':
                return {
                    x: (index - 1) * spacing,
                    y: 0,
                    z: 0
                };
            case 'tight':
                return {
                    x: (index % 2) * spacing,
                    y: 0,
                    z: Math.floor(index / 2) * spacing
                };
            default:
                return { x: 0, y: 0, z: 0 };
        }
    }
}
exports.AIManager = AIManager;
AIManager.UPDATE_RATE = 60; // Updates por segundo
AIManager.MAX_PERCEPTION_DISTANCE = 100; // Metros
AIManager.MAX_SOUND_DISTANCE = 200; // Metros
AIManager.BOT_LOD_DISTANCES = [50, 100, 200]; // Metros
//# sourceMappingURL=AIManager.js.map