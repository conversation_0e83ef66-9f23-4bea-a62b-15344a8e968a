{"version": 3, "file": "EconomyBalanceTest.js", "sourceRoot": "", "sources": ["../../src/tests/EconomyBalanceTest.ts"], "names": [], "mappings": ";;AAAA,6DAA0D;AAC1D,uDAAoD;AACpD,iEAA8D;AAW9D,MAAM,kBAAkB;IAUpB;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IACjD,CAAC;IAEM,KAAK,CAAC,cAAc;QACvB,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,kBAAkB,CAAC,cAAc,CAAC;QACpD,IAAI,iBAAiB,GAAG,SAAS,CAAC;QAElC,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,cAAc,GAA8B,EAAE,CAAC;QAErD,8BAA8B;QAC9B,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;YAC1C,8BAA8B;YAC9B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAE3C,oCAAoC;YACpC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAE9C,4BAA4B;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAE9D,2BAA2B;YAC3B,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,GAAG,iBAAiB,EAAE,CAAC;gBAC5C,iBAAiB,GAAG,IAAI,CAAC;YAC7B,CAAC;YAED,iCAAiC;YACjC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAC1B,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1E,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACpF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;QAEnD,6CAA6C;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;QAE5E,OAAO;YACH,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,mBAAmB,EAAE,iBAAiB;SACzC,CAAC;IACN,CAAC;IAEO,eAAe;QACnB,MAAM,KAAK,GAAe,EAAE,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1D,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpB,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBACnD,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YAC7D,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAAC,KAAiB,EAAE,IAAY;QAC5D,yDAAyD;QACzD,wDAAwD;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,kBAAkB,CAAC,cAAc,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAE9F,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAC5B,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAC5B,IAAI,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,EAAE,CAAC;oBACrD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACtC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,KAAe,EAAE,KAAe;QACnD,qCAAqC;QACrC,oCAAoC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QACtC,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9C,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QAE7C,6BAA6B;QAC7B,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,2BAA2B,CAAC,KAAiB,EAAE,IAAY;QAC/D,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjB,4CAA4C;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,kBAAkB,CAAC,cAAc,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YAEzF,gDAAgD;YAChD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,kBAAkB,CAAC,cAAc,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;YAE/F,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACpB,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC;oBAC7B,wCAAwC;oBACxC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBACtD,CAAC;gBAED,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC;oBACjC,+BAA+B;oBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACjE,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB,CAAC,KAAiB,EAAE,IAAY;QAC7D,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACpB,0DAA0D;gBAC1D,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;oBACpE,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;oBAC3D,IAAI,YAAY;wBAAE,cAAc,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,4BAA4B,CAAC,QAAgB,EAAE,IAAY;QAC/D,gEAAgE;QAChE,qCAAqC;QACrC,MAAM,YAAY,GAAG,IAAI,GAAG,kBAAkB,CAAC,cAAc,CAAC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IACnD,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,4CAA4C;QAC5C,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAEnF,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;IAEO,kBAAkB,CAAC,IAAc;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YACnC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAEO,0BAA0B,CAAC,WAAqB;QACpD,MAAM,YAAY,GAA8B;YAC5C,MAAM,EAAE,CAAC,EAAK,SAAS;YACvB,QAAQ,EAAE,CAAC,EAAG,YAAY;YAC1B,MAAM,EAAE,CAAC,CAAI,SAAS;SACzB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,KAAK,GAAG,IAAI;gBAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;iBACpC,IAAI,KAAK,GAAG,IAAI;gBAAE,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;;gBAC3C,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACxB,CAAC;IAEM,cAAc,CAAC,OAA2B;QAC7C,MAAM,oBAAoB,GAAG;YACzB,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;SACV,CAAC;QAEF,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAE/B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrB,aAAa,IAAI,MAAM,CAAC,gBAAgB,CAAC;YACzC,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC;YACrC,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC;YACrC,sBAAsB,IAAI,MAAM,CAAC,mBAAmB,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC9D,oBAAoB,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;YACvC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3F,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,sBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1G,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC1D,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACP,CAAC;;AAnOuB,oCAAiB,GAAG,IAAI,CAAC;AACzB,mCAAgB,GAAG,CAAC,CAAC;AACrB,kCAAe,GAAG,EAAE,CAAC;AACrB,iCAAc,GAAG,IAAI,CAAC,CAAC,yBAAyB;AAmO5E,kBAAe,kBAAkB,CAAC"}