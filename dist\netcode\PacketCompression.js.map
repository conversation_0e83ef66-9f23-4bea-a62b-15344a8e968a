{"version": 3, "file": "PacketCompression.js", "sourceRoot": "", "sources": ["../../src/netcode/PacketCompression.ts"], "names": [], "mappings": ";;;AACA,mCAAgC;AAEhC,MAAa,iBAAiB;IAM1B,gCAAgC;IACzB,MAAM,CAAC,mBAAmB,CAAC,KAAkB;QAChD,MAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;QAC1D,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,mBAAmB;QACnB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE3D,sBAAsB;QACtB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE3D,2DAA2D;QAC3D,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;QACvE,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QAEzE,mCAAmC;QACnC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAE5D,mCAAmC;QACnC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,CAAC;QAEZ,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,mCAAmC;IAC5B,MAAM,CAAC,qBAAqB,CAAC,MAAc;QAC9C,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,sBAAsB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,IAAI,EAAE,CAAC;QAEb,yBAAyB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,IAAI,EAAE,CAAC;QAEb,yBAAyB;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/C,MAAM,IAAI,CAAC,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC;QACjD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC;QAEhD,sCAAsC;QACtC,MAAM,kBAAkB,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEvD,OAAO;YACH,QAAQ;YACR,QAAQ;YACR,WAAW,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;YACvC,MAAM;YACN,KAAK;YACL,kBAAkB;YAClB,EAAE,EAAE,EAAE,EAAE,gCAAgC;YACxC,OAAO,EAAE,EAAE,CAAC,0CAA0C;SACzD,CAAC;IACN,CAAC;IAED,+BAA+B;IACxB,MAAM,CAAC,mBAAmB,CAAC,KAAkB;QAChD,MAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;QAC1D,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,sBAAsB;QACtB,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,IAAI,CAAC,CAAC;QAEZ,4BAA4B;QAC5B,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7C,MAAM,IAAI,CAAC,CAAC;QAEZ,qBAAqB;QACrB,MAAM,YAAY,GACd,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YACxC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QACpD,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC3C,MAAM,IAAI,CAAC,CAAC;QAEZ,wBAAwB;QACxB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;QAChE,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,IAAI,CAAC,CAAC;QAEZ,mBAAmB;QACnB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEvC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,0DAA0D;IAClD,MAAM,CAAC,YAAY,CAAC,MAAc,EAAE,MAAc,EAAE,MAAe;QACvE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAC,CAAC;QAC5E,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAChF,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAChF,OAAO,MAAM,GAAG,EAAE,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,MAAc;QACrD,OAAO;YACH,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB;YACvD,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB;YAC3D,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB;SAC9D,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,MAAc,EAAE,MAAc,EAAE,KAAa;QACrE,wCAAwC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtC,OAAO,MAAM,GAAG,CAAC,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,MAAc;QACrD,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;IAChE,CAAC;;AApIL,8CAqIC;AApIG,8BAA8B;AACN,oCAAkB,GAAG,GAAG,CAAC,CAAC,mBAAmB;AAC7C,oCAAkB,GAAG,IAAI,CAAC,CAAC,mBAAmB;AAC9C,oCAAkB,GAAG,GAAG,CAAC,CAAC,mBAAmB"}