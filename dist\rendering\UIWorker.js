"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class UIWorker {
    constructor() {
        this.offscreenCanvas = new OffscreenCanvas(1920, 1080);
        this.ctx = this.offscreenCanvas.getContext('2d');
        this.elementCache = new Map();
    }
    handleMessage(event) {
        const { type, element, data } = event.data;
        switch (type) {
            case 'bakeElement':
                this.bakeElement(element);
                break;
            case 'updateElement':
                this.updateElement(data.elementId, data);
                break;
            case 'invalidateCache':
                this.invalidateCache(data.elementIds);
                break;
        }
    }
    async bakeElement(element) {
        // Limpa o canvas
        this.ctx.clearRect(0, 0, this.offscreenCanvas.width, this.offscreenCanvas.height);
        // Renderiza o elemento
        switch (element.id) {
            case 'healthBar':
                await this.renderHealthBar(element);
                break;
            case 'ammoCounter':
                await this.renderAmmoCounter(element);
                break;
            case 'minimapBorder':
                await this.renderMinimapBorder(element);
                break;
        }
        // Cria um bitmap do elemento renderizado
        const bitmap = await createImageBitmap(this.offscreenCanvas);
        this.elementCache.set(element.id, bitmap);
        // Notifica o thread principal
        self.postMessage({
            type: 'elementUpdate',
            elementId: element.id,
            cache: bitmap
        }, [bitmap]);
    }
    async updateElement(elementId, data) {
        // Limpa área do elemento
        this.ctx.clearRect(0, 0, this.offscreenCanvas.width, this.offscreenCanvas.height);
        // Atualiza elemento com novos dados
        switch (elementId) {
            case 'hitmarker':
                await this.renderHitmarker(data);
                break;
            case 'damageIndicator':
                await this.renderDamageIndicator(data);
                break;
            case 'minimap':
                await this.renderMinimap(data);
                break;
            case 'notification':
                await this.renderNotification(data);
                break;
        }
        // Cria novo bitmap e atualiza cache
        const bitmap = await createImageBitmap(this.offscreenCanvas);
        this.elementCache.set(elementId, bitmap);
        // Notifica o thread principal
        self.postMessage({
            type: 'elementUpdate',
            elementId,
            cache: bitmap
        }, [bitmap]);
    }
    async renderHealthBar(element) {
        this.ctx.save();
        // Configurações de estilo
        this.ctx.fillStyle = '#333333';
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        // Desenha borda
        this.ctx.beginPath();
        this.ctx.roundRect(0, 0, 200, 20, 5);
        this.ctx.stroke();
        this.ctx.restore();
    }
    async renderAmmoCounter(element) {
        this.ctx.save();
        // Configurações de fonte
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.textAlign = 'right';
        // Desenha background
        this.ctx.fillStyle = '#000000aa';
        this.ctx.roundRect(0, 0, 100, 40, 5);
        this.ctx.fill();
        this.ctx.restore();
    }
    async renderMinimapBorder(element) {
        this.ctx.save();
        // Desenha borda do minimapa
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(100, 100, 98, 0, Math.PI * 2);
        this.ctx.stroke();
        this.ctx.restore();
    }
    async renderHitmarker(data) {
        this.ctx.save();
        // Configurações de estilo
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        // Desenha hitmarker
        this.ctx.beginPath();
        this.ctx.moveTo(-10, 0);
        this.ctx.lineTo(10, 0);
        this.ctx.moveTo(0, -10);
        this.ctx.lineTo(0, 10);
        this.ctx.stroke();
        this.ctx.restore();
    }
    async renderDamageIndicator(data) {
        this.ctx.save();
        // Configurações de estilo
        this.ctx.fillStyle = '#ff0000aa';
        // Desenha indicador de dano
        this.ctx.beginPath();
        this.ctx.arc(0, 0, 100, data.direction - 0.2, data.direction + 0.2);
        this.ctx.lineTo(0, 0);
        this.ctx.fill();
        this.ctx.restore();
    }
    async renderMinimap(data) {
        this.ctx.save();
        // Desenha fundo do minimapa
        this.ctx.fillStyle = '#000000aa';
        this.ctx.beginPath();
        this.ctx.arc(100, 100, 95, 0, Math.PI * 2);
        this.ctx.fill();
        // Desenha elementos do minimapa
        this.drawMinimapElements(data);
        this.ctx.restore();
    }
    drawMinimapElements(data) {
        // Desenha jogador
        this.ctx.fillStyle = '#00ff00';
        this.ctx.beginPath();
        this.ctx.arc(data.playerPosition.x, data.playerPosition.y, 3, 0, Math.PI * 2);
        this.ctx.fill();
        // Desenha inimigos
        this.ctx.fillStyle = '#ff0000';
        data.enemies.forEach(pos => {
            this.ctx.beginPath();
            this.ctx.arc(pos.x, pos.y, 3, 0, Math.PI * 2);
            this.ctx.fill();
        });
        // Desenha zona segura
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(data.safeZone.center.x, data.safeZone.center.y, data.safeZone.radius, 0, Math.PI * 2);
        this.ctx.stroke();
    }
    async renderNotification(data) {
        this.ctx.save();
        // Configurações de estilo
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.textAlign = 'center';
        // Desenha background
        this.ctx.fillStyle = '#000000aa';
        this.ctx.roundRect(0, 0, 200, 40, 5);
        this.ctx.fill();
        // Desenha texto
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText(data.message, 100, 25);
        this.ctx.restore();
    }
    invalidateCache(elementIds) {
        elementIds.forEach(id => {
            this.elementCache.delete(id);
        });
    }
}
// Inicializa o worker
const worker = new UIWorker();
self.onmessage = (e) => worker.handleMessage(e);
//# sourceMappingURL=UIWorker.js.map