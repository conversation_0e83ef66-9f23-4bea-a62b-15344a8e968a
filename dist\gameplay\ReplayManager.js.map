{"version": 3, "file": "ReplayManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/ReplayManager.ts"], "names": [], "mappings": ";;;AAkCA,MAAa,aAAa;IAYtB;QAPQ,cAAS,GAAY,KAAK,CAAC;QAC3B,WAAM,GAAkB,EAAE,CAAC;QAC3B,WAAM,GAAgB,EAAE,CAAC;QACzB,aAAQ,GAA0B,IAAI,CAAC;QAEvC,gBAAW,GAAW,CAAC,CAAC;QAG5B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAEM,cAAc,CAAC,eAAwC;QAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAErB,IAAI,CAAC,QAAQ,GAAG;YACZ,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,EAAE;YACtC,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,EAAE;YACtC,QAAQ,EAAE,eAAe,CAAC,QAAQ,IAAI,UAAU;YAChD,QAAQ,EAAE,EAAE;SACf,CAAC;QAEF,2CAA2C;QAC3C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACpB,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC,CAAC;IACP,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAAC,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACpB,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE;oBACN,GAAG,IAAI,CAAC,QAAS;oBACjB,QAAQ,EAAE,IAAI,CAAC,WAAW;iBAC7B;aACJ,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,WAAW,CAAC,YAAsC,EAAE,YAAsC,EAAE,QAAa;QAC5G,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,MAAM,KAAK,GAAgB;YACvB,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE;YACxB,YAAY,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC;YACnC,YAAY,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC;YACnC,MAAM,EAAE,EAAE;YACV,QAAQ;SACX,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAExB,kEAAkE;QAClE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;YAC5E,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACL,CAAC;IAEM,WAAW,CAAC,KAAgB;QAC/B,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,GAAG,KAAK;YACR,IAAI,EAAE,IAAI,CAAC,WAAW;SACzB,CAAC,CAAC;IACP,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACpB,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SAChC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB;QACrB,wDAAwD;QACxD,OAAO,IAAI,CAAC,CAAC,mDAAmD;IACpE,CAAC;IAAW,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,aAAsB;QACtG,MAAM,MAAM,GAAG,GAAG,QAAQ,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;QAErD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE;gBACxC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBAC7D,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7B,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACpB,IAAI,EAAE,aAAa;gBACnB,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,aAAa;gBACb,MAAM;aACT,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAAW,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,QAAgB,EAAE;QACzE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE;gBACxC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBAClC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACpB,IAAI,EAAE,kBAAkB;gBACxB,QAAQ;gBACR,KAAK;aACR,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAAW,KAAK,CAAC,UAAU,CAAC,QAAgB;QACzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE;gBACxC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACjE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC/B,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACpB,IAAI,EAAE,YAAY;gBAClB,QAAQ;aACX,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,CAAe;QACvC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,OAAO;gBACR,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,YAAY;gBACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YACV,KAAK,qBAAqB;gBACtB,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM;QACd,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,gCAAgC;QAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACpB,IAAI,EAAE,gBAAgB;YACtB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACnE,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IAEO,yBAAyB,CAAC,QAAgB;QAC9C,+DAA+D;QAC/D,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;;AAlLL,sCAsLC;AArL2B,yBAAW,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,AAAnB,CAAoB,CAAC,cAAc;AAC9C,+BAAiB,GAAG,CAAC,AAAJ,CAAK;AACtB,qBAAO,GAAG,OAAO,AAAV,CAAW"}