{"version": 3, "file": "POIGenerator.js", "sourceRoot": "", "sources": ["../../src/gameplay/POIGenerator.ts"], "names": [], "mappings": ";;;AACA,gFAA6E;AAsB7E,MAAa,YAAY;IAGrB;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,iDAAuB,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,WAAW,CACb,IAAoD,EACpD,QAAiB,EACjB,QAAqB;QAErB,4DAA4D;QAC5D,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAE5D,4BAA4B;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEhD,6BAA6B;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAE5D,wCAAwC;QACxC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAErC,8BAA8B;QAC9B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAExD,OAAO;YACH,MAAM;YACN,MAAM;YACN,eAAe,EAAE,kBAAkB;SACtC,CAAC;IACN,CAAC;IAEO,qBAAqB,CAAC,IAAY;QACtC,MAAM,eAAe,GAKhB;YACD,KAAK,EAAE;gBACH,aAAa,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;gBAC1D,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;gBACpD,UAAU,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,CAAC;gBAC/C,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,cAAc,CAAC;aACpD;YACD,QAAQ,EAAE;gBACN,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC;gBAC5D,SAAS,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;gBACzD,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,CAAC;gBAClD,UAAU,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,iBAAiB,CAAC;aAChE;YACD,UAAU,EAAE;gBACR,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;gBAC5D,SAAS,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;gBAC1D,UAAU,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;gBACrD,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC;aACrD;YACD,MAAM,EAAE;gBACJ,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;gBACrD,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;gBACjD,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;gBACzC,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;aACnD;SACJ,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC;IAC1D,CAAC;IAEO,iBAAiB,CAAC,QAAqB;QAC3C,MAAM,MAAM,GAAG;YACX,IAAI,EAAE,EAAa;YACnB,WAAW,EAAE,EAAgC;YAC7C,WAAW,EAAE,EAA4E;YACzF,MAAM,EAAE,EAAW;SACtB,CAAC;QAEF,6DAA6D;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,iBAAiB;QACxE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAEvD,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAErE,2BAA2B;QAC3B,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAErE,qBAAqB;QACrB,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAE7E,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,QAAqB,EAAE,EAAU,EAAE,EAAU;QAClE,MAAM,IAAI,GAAG;YACT,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,CAAC;YACT,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,KAAK;SACf,CAAC;QAEF,gEAAgE;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE7B,IAAI,MAAM,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACvD,CAAC;aAAM,IAAI,MAAM,GAAG,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YAClE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,mBAAmB,CAAC,IAAa,EAAE,SAAsB;QAC7D,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEjC,2DAA2D;QAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC9D,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAC/C,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,mBAAmB,CAAC,CAAS,EAAE,CAAS,EAAE,IAAa;QAC3D,kFAAkF;QAClF,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YAC9B,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK;wBAAE,WAAW,GAAG,IAAI,CAAC;oBACnD,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ;wBAAE,cAAc,EAAE,CAAC;gBACxD,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,WAAW,IAAI,cAAc,IAAI,EAAE,CAAC,CAAC,gCAAgC;IAChF,CAAC;IAEO,mBAAmB,CAAC,IAAa,EAAE,SAAsB;QAC7D,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEjC,yCAAyC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACnB,WAAW,CAAC,IAAI,CAAC;wBACb,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE;wBAClC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;wBACrB,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;qBAC5B,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,sBAAsB,CAAC,IAAa,EAAE,WAAkB;QAC5D,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,uCAAuC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAChC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EACvB,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EACvB,IAAI,CACP,CAAC;gBACF,IAAI,KAAK;oBAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,MAAe,EAAE,IAAa,EAAE,KAAc;QACpE,oEAAoE;QACpE,0CAA0C;QAC1C,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,QAAqB;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAEzD,gEAAgE;QAChE,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,YAAoB,EAAE,EAAE,CAAC,CAAC;YAC3E,EAAE,EAAE,GAAG,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YACnC,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,KAAK;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAgB,EAAE,EAAE,CAAC,CAAC;YAC/D,EAAE,EAAE,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YACxC,SAAS,EAAE,KAAK;SACnB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACH,SAAS;YACT,KAAK;YACL,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,eAAe,CAAC,UAAU;SAC5C,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAiB;QAC7C,qDAAqD;QACrD,MAAM,OAAO,CAAC,GAAG,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;gBAC1C,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC9C,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,aAAa,EAAE,MAAM,CAAC,aAAa;aACtC,CAAC;SACL,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,QAAiB,EAAE,IAAS;QAClE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAEzD,oDAAoD;QACpD,OAAO,CAAC,GAAG,CAAC,yCAAyC,IAAI,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE9D,4FAA4F;QAC5F,MAAM,WAAW,GAAG;YAChB,QAAQ;YACR,IAAI;YACJ,MAAM,EAAE,eAAe,CAAC,UAAU;YAClC,MAAM,EAAE,GAAG,IAAI,SAAS;YACxB,kBAAkB,EAAE,EAAE;SACzB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,yCAAyC,IAAI,GAAG,EAAE,WAAW,CAAC,CAAC;IAC/E,CAAC;CACJ;AApQD,oCAoQC"}