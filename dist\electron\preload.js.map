{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../../src/electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAEtD,6CAA6C;AAC7C,MAAM,WAAW,GAAG;IAChB,yBAAyB;IACzB,aAAa,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAC1D,sBAAsB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC;IAC5E,YAAY,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAExD,sBAAsB;IACtB,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACzD,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACzD,WAAW,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,cAAc,CAAC;IAEnD,oBAAoB;IACpB,YAAY,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC;IAErE,yCAAyC;IACzC,WAAW,EAAE,CAAC,QAAoB,EAAE,EAAE;QAClC,sBAAW,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,WAAW,EAAE,CAAC,QAAoB,EAAE,EAAE;QAClC,sBAAW,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,WAAW,EAAE,CAAC,QAAoB,EAAE,EAAE;QAClC,sBAAW,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,cAAc,EAAE,CAAC,QAAoB,EAAE,EAAE;QACrC,sBAAW,CAAC,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,cAAc,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED,gBAAgB,EAAE,CAAC,QAAiC,EAAE,EAAE;QACpD,sBAAW,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvE,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,cAAc,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;IAED,cAAc;IACd,IAAI,EAAE;QACF,cAAc;QACd,WAAW,EAAE,CAAC,QAAgB,EAAE,MAAc,EAAE,EAAE;YAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3B,2BAA2B;gBAC3B,UAAU,CAAC,GAAG,EAAE;oBACZ,OAAO,CAAC;wBACJ,OAAO,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;wBAC9B,QAAQ;wBACR,MAAM;wBACN,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,iBAAiB;qBACvE,CAAC,CAAC;gBACP,CAAC,EAAE,IAAI,CAAC,CAAC;YACb,CAAC,CAAC,CAAC;QACP,CAAC;QAED,0BAA0B;QAC1B,cAAc,EAAE,GAAG,EAAE;YACjB,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,KAAK;gBACjB,gBAAgB,EAAE,IAAI;gBACtB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,EAAE;gBACV,GAAG,EAAE,IAAI;gBACT,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,MAAM,CAAC,WAAW;aAC/B,CAAC,CAAC;QACP,CAAC;QAED,sBAAsB;QACtB,gBAAgB,EAAE,GAAG,EAAE;YACnB,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,KAAK,EAAE,KAAK;gBACZ,eAAe,EAAE,GAAG;gBACpB,eAAe,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;gBAC/C,aAAa,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,aAAa,CAAC;gBAC3D,YAAY,EAAE;oBACV;wBACI,IAAI,EAAE,IAAI;wBACV,UAAU,EAAE,GAAG;wBACf,aAAa,EAAE,IAAI,CAAC,SAAS;qBAChC;iBACJ;aACJ,CAAC,CAAC;QACP,CAAC;QAED,wBAAwB;QACxB,eAAe,EAAE,GAAG,EAAE;YAClB,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,QAAQ,EAAE;oBACN,OAAO,EAAE,OAAO;oBAChB,UAAU,EAAE,WAAW;oBACvB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,KAAK;oBACZ,YAAY,EAAE,SAAS;oBACvB,OAAO,EAAE,OAAO;oBAChB,QAAQ,EAAE,OAAO;iBACpB;gBACD,KAAK,EAAE;oBACH,YAAY,EAAE,EAAE;oBAChB,WAAW,EAAE,EAAE;oBACf,SAAS,EAAE,EAAE;oBACb,WAAW,EAAE,EAAE;oBACf,YAAY,EAAE,IAAI;iBACrB;gBACD,QAAQ,EAAE;oBACN,gBAAgB,EAAE,GAAG;oBACrB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE;wBACT,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,GAAG;wBACjB,QAAQ,EAAE,GAAG;wBACb,SAAS,EAAE,GAAG;wBACd,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE,GAAG;qBAChB;iBACJ;aACJ,CAAC,CAAC;QACP,CAAC;QAED,uBAAuB;QACvB,gBAAgB,EAAE,CAAC,QAAa,EAAE,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,eAAe;QACf,cAAc,EAAE,CAAC,QAAgB,EAAE,QAAgB,EAAE,EAAE,EAAE;YACrD,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,CAAC;oBACP,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;oBAClD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;oBAC1C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;iBACjD,CAAC,CAAC;YACP,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QAED,OAAO;QACP,aAAa,EAAE,CAAC,QAAiB,EAAE,EAAE;YACjC,MAAM,KAAK,GAAG;gBACV;oBACI,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,2BAA2B;oBACxC,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;oBACtB,KAAK,EAAE,KAAK;iBACf;gBACD;oBACI,EAAE,EAAE,kBAAkB;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,gCAAgC;oBAC7C,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,EAAE,eAAe,EAAE,GAAG,EAAE;oBAC/B,KAAK,EAAE,KAAK;iBACf;gBACD;oBACI,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,uBAAuB;oBACpC,QAAQ,EAAE,OAAO;oBACjB,MAAM,EAAE,QAAQ;oBAChB,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;oBACtB,KAAK,EAAE,KAAK;iBACf;aACJ,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC7E,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,eAAe;QACf,YAAY,EAAE,CAAC,MAAc,EAAE,aAAqB,EAAE,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ,aAAa,EAAE,CAAC,CAAC;YAChE,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,4BAA4B;gBACrC,UAAU,EAAE;oBACR,KAAK,EAAE,KAAK;oBACZ,eAAe,EAAE,GAAG;iBACvB;aACJ,CAAC,CAAC;QACP,CAAC;KACJ;IAED,cAAc;IACd,KAAK,EAAE;QACH,UAAU,EAAE,CAAC,OAAe,EAAE,EAAE;YAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC;YAE1B,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACZ,OAAO,GAAG,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;YACjG,CAAC;YACD,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAC5D,CAAC;QAED,YAAY,EAAE,CAAC,GAAW,EAAE,EAAE;YAC1B,OAAO,GAAG,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,cAAc,EAAE,CAAC,MAAc,EAAE,IAAyB,EAAE,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAC9C,OAAO,GAAG,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC;QAClD,CAAC;KACJ;CACJ,CAAC;AAEF,8BAA8B;AAC9B,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC"}