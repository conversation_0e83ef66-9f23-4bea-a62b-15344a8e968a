"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotAI = void 0;
class BotAI {
    constructor(id, position, mapManager) {
        this.id = id;
        this.position = position;
        this.mapManager = mapManager;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.rotation = 0;
        this.health = 100;
        this.state = 'idle';
        this.targetPosition = null;
        this.currentPath = [];
        this.lastPerceptionUpdate = 0;
        this.lastDecisionUpdate = 0;
        this.lastPathUpdate = 0;
        this.perception = {
            lastKnownEnemies: new Map(),
            visibleAllies: [],
            heardSounds: [],
            nearbyLoot: []
        };
    }
    update(deltaTime, lodLevel) {
        const now = performance.now();
        // Atualiza percepção com frequência baseada no LOD
        if (now - this.lastPerceptionUpdate >= BotAI.PERCEPTION_UPDATE_INTERVAL * (lodLevel + 1)) {
            this.updatePerception(lodLevel);
            this.lastPerceptionUpdate = now;
        }
        // Atualiza decisões com frequência baseada no LOD
        if (now - this.lastDecisionUpdate >= BotAI.DECISION_UPDATE_INTERVAL * (lodLevel + 1)) {
            this.updateDecision(lodLevel);
            this.lastDecisionUpdate = now;
        }
        // Atualiza pathfinding com frequência baseada no LOD
        if (now - this.lastPathUpdate >= BotAI.PATH_UPDATE_INTERVAL * (lodLevel + 1)) {
            this.updatePath();
            this.lastPathUpdate = now;
        }
        // Atualiza movimento e ações
        this.updateMovement(deltaTime);
        this.updateActions(deltaTime, lodLevel);
    }
    updatePerception(lodLevel) {
        if (lodLevel > 2)
            return; // Bots muito distantes não atualizam percepção
        // Simula visão do bot
        this.updateVision();
        // Simula audição do bot
        this.updateHearing();
        // Atualiza conhecimento do ambiente
        this.updateEnvironmentKnowledge();
    }
    updateVision() {
        // Implementa ray casting para verificar linha de visão
        // Simula cone de visão do bot
        // Detecta jogadores e outros bots
        // Aplica incerteza baseada em distância e condições
    }
    updateHearing() {
        // Processa sons próximos
        // Atualiza mapa de sons
        // Aplica atenuação por distância
    }
    updateEnvironmentKnowledge() {
        // Atualiza conhecimento do mapa
        // Detecta mudanças no ambiente
        // Marca posições de interesse
    }
    updateDecision(lodLevel) {
        if (lodLevel > 3) {
            // Bots muito distantes só fazem decisões básicas
            this.makeSimpleDecision();
            return;
        }
        switch (this.state) {
            case 'idle':
                this.decideNextAction();
                break;
            case 'moving':
                this.evaluateMovement();
                break;
            case 'attacking':
                this.evaluateCombat();
                break;
            case 'covering':
                this.evaluateCover();
                break;
            case 'healing':
                this.evaluateHealing();
                break;
            case 'looting':
                this.evaluateLooting();
                break;
        }
    }
    makeSimpleDecision() {
        // Decisões simplificadas para bots distantes
        // Apenas movimento básico e reações simples
    }
    decideNextAction() {
        // Avalia ameaças
        if (this.hasNearbyThreats()) {
            this.engageCombat();
            return;
        }
        // Avalia necessidade de cura
        if (this.needsHealing()) {
            this.startHealing();
            return;
        }
        // Avalia oportunidades de loot
        if (this.hasValueableLootNearby()) {
            this.startLooting();
            return;
        }
        // Move para próximo objetivo
        this.moveToNextObjective();
    }
    updatePath() {
        if (!this.targetPosition)
            return;
        // Implementa A* pathfinding
        this.currentPath = this.findPath(this.position, this.targetPosition);
    }
    findPath(start, end) {
        // Implementa algoritmo A* para pathfinding
        // Usa o MapManager para informações de navegação
        return [];
    }
    updateMovement(deltaTime) {
        if (this.currentPath.length === 0)
            return;
        // Calcula próximo ponto do caminho
        const nextPoint = this.currentPath[0];
        const direction = this.calculateDirection(this.position, nextPoint);
        // Atualiza velocidade e posição
        this.velocity = {
            x: direction.x * 5, // 5 m/s
            y: 0,
            z: direction.z * 5
        };
        this.position = {
            x: this.position.x + this.velocity.x * deltaTime,
            y: this.position.y + this.velocity.y * deltaTime,
            z: this.position.z + this.velocity.z * deltaTime
        };
        // Remove pontos alcançados do caminho
        if (this.hasReachedPoint(nextPoint)) {
            this.currentPath.shift();
        }
    }
    updateActions(deltaTime, lodLevel) {
        if (lodLevel > 2)
            return; // Bots distantes não executam ações complexas
        switch (this.state) {
            case 'attacking':
                this.updateCombat(deltaTime);
                break;
            case 'healing':
                this.updateHealing(deltaTime);
                break;
            case 'looting':
                this.updateLooting(deltaTime);
                break;
        }
    }
    calculateDirection(from, to) {
        const dx = to.x - from.x;
        const dz = to.z - from.z;
        const length = Math.sqrt(dx * dx + dz * dz);
        return {
            x: dx / length,
            y: 0,
            z: dz / length
        };
    }
    hasReachedPoint(point) {
        const distance = this.calculateDistance(this.position, point);
        return distance < 1; // 1 metro de tolerância
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    // Métodos auxiliares para tomada de decisão
    hasNearbyThreats() {
        return this.perception.lastKnownEnemies.size > 0;
    }
    needsHealing() {
        return this.health < 75;
    }
    hasValueableLootNearby() {
        return this.perception.nearbyLoot.length > 0;
    }
    // Métodos de ação
    engageCombat() {
        this.state = 'attacking';
        // Implementa lógica de combate
    }
    startHealing() {
        this.state = 'healing';
        // Implementa lógica de cura
    }
    startLooting() {
        this.state = 'looting';
        // Implementa lógica de loot
    }
    moveToNextObjective() {
        this.state = 'moving';
        // Implementa lógica de movimento para objetivo
    }
    // Métodos de atualização de estado
    updateCombat(deltaTime) {
        // Implementa lógica de combate
    }
    updateHealing(deltaTime) {
        // Implementa lógica de cura
    }
    updateLooting(deltaTime) {
        // Implementa lógica de loot
    }
    evaluateMovement() {
        // Avalia necessidade de mudar rota
    }
    evaluateCombat() {
        // Avalia situação de combate
    }
    evaluateCover() {
        // Avalia necessidade de cobertura
    }
    evaluateHealing() {
        // Avalia necessidade de continuar curando
    }
    evaluateLooting() {
        // Avalia se deve continuar lootando
    }
}
exports.BotAI = BotAI;
BotAI.PERCEPTION_UPDATE_INTERVAL = 100; // ms
BotAI.DECISION_UPDATE_INTERVAL = 250; // ms
BotAI.PATH_UPDATE_INTERVAL = 500; // ms
//# sourceMappingURL=BotAI.js.map