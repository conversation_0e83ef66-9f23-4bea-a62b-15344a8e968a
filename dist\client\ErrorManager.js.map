{"version": 3, "file": "ErrorManager.js", "sourceRoot": "", "sources": ["../../src/client/ErrorManager.ts"], "names": [], "mappings": ";;;AAoDA,MAAa,YAAY;IAUrB;QAFQ,+BAA0B,GAA0B,IAAI,CAAC;QAG7D,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEO,wBAAwB;QAC5B,kCAAkC;QAClC,MAAM,CAAC,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;gBACxB,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,KAAK,EAAE,KAAK,EAAE,KAAK;aACtB,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,iDAAiD;QACjD,MAAM,CAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBACtB,OAAO,EAAE,gCAAgC,KAAK,CAAC,MAAM,EAAE;gBACvD,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK;aAC7B,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,uDAAuD;QACvD,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB,CAAC;YAC3C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;oBACtB,OAAO,EAAE,kCAAkC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;oBACzE,MAAM,EAAG,MAA+C,CAAC,GAAG;iBAC/D,CAAC,CAAC;YACP,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,0BAA0B;QAC9B,IAAI,CAAC,0BAA0B,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACnC,MAAM,OAAO,GAAG;YACZ,GAAG,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE;YAC5B,MAAM,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE;YACnC,GAAG,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE;YAC/B,GAAG,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;SAC1C,CAAC;QAEF,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,kBAAkB,CAAC,OAAY;QACnC,eAAe;QACf,IAAI,OAAO,CAAC,GAAG,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;YAC3C,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE;gBACpC,OAAO,EAAE,qBAAqB,OAAO,CAAC,GAAG,EAAE;gBAC3C,OAAO;aACV,CAAC,CAAC;QACP,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAC7E,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE;gBACvC,OAAO,EAAE,sBAAsB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG;gBAC9F,OAAO;aACV,CAAC,CAAC;QACP,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,YAAY,CAAC,qBAAqB;YAC5D,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAC/D,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE;gBAC5C,OAAO,EAAE,2BAA2B;gBACpC,OAAO;aACV,CAAC,CAAC;QACP,CAAC;QAED,gBAAgB;QAChB,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAClE,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE;gBAC1C,OAAO,EAAE,qBAAqB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG;gBAC7E,OAAO;aACV,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEM,WAAW,CAAC,IAAuB,EAAE,IAAS;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAEO,sBAAsB,CAAC,KAAa,EAAE,IAAS;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;YAClD,OAAO,EAAE,sBAAsB,KAAK,EAAE;YACtC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAEO,YAAY,CAAC,IAAyB,EAAE,IAA0B;QACtE,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;YACJ,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,eAAe;YACxC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE;gBACL,GAAG,IAAI,CAAC,OAAO;gBACf,SAAS,EAAE,IAAI,CAAC,mBAAmB,EAAE;aACxC;YACD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;SACnC,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,MAAmB;QACrC,uBAAuB;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAEpC,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAClC,IAAI,CAAC;gBACD,UAAU,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,MAAM,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAEO,iBAAiB,CAAC,IAAS;QAC/B,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACnE,OAAO,UAAU,CAAC;QACtB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACpE,OAAO,MAAM,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACzE,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,4BAA4B,CAAC,OAAY;QAC7C,IAAI,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC;YACxE,OAAO,UAAU,CAAC;QACtB,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACvE,OAAO,MAAM,CAAC;QAClB,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACxE,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,SAAS,CAAC,QAAuC;QACpD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAmB;QAC1C,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,oBAAoB,EAAE;gBAC/C,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,kBAAkB;iBACrC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC/B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,MAAmB;QACjC,OAAO,CAAC,KAAK,CAAC,iBAAiB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAC/C,OAAO,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;IAEO,UAAU;QACd,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACtE,CAAC;IAEO,mBAAmB;QACvB,2DAA2D;QAC3D,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,aAAa;QACjB,OAAO;YACH,OAAO,EAAE,OAAO,EAAE,iBAAiB;YACnC,EAAE,EAAE,SAAS,CAAC,QAAQ;YACtB,GAAG,EAAE,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,GAAG,QAAQ;YACxD,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE;YACtB,GAAG,EAAE,IAAI,CAAC,eAAe,EAAE;YAC3B,OAAO,EAAE;gBACL,GAAG,EAAE,SAAS,EAAE,iCAAiC;gBACjD,OAAO,EAAE,SAAS,CAAC,iCAAiC;aACvD;SACJ,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,qBAAqB,CAAC,EAAE,CAAC,EAAE;gBACvB,qBAAqB,CAAC,EAAE,CAAC,EAAE;oBACvB,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,+CAA+C;QAC/C,OAAO;YACH,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,eAAe,IAAI,CAAC;YAC/C,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,cAAc,IAAI,CAAC;YAC7C,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,eAAe,IAAI,CAAC;SACjD,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,+CAA+C;QAC/C,OAAO;YACH,KAAK,EAAE,CAAC;YACR,WAAW,EAAE,CAAC;SACjB,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,+CAA+C;QAC/C,OAAO;YACH,KAAK,EAAE,CAAC;YACR,WAAW,EAAE,CAAC;YACd,IAAI,EAAE;gBACF,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;aACV;SACJ,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,6CAA6C;QAC7C,OAAO;YACH,IAAI,EAAE,CAAC;YACP,UAAU,EAAE,CAAC;YACb,SAAS,EAAE;gBACP,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,CAAC;aACV;SACJ,CAAC;IACN,CAAC;IAEO,UAAU;QACd,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,EAAE;YAAE,OAAO,SAAS,CAAC;QAE1B,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAC;QAEjC,OAAO,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IAC9D,CAAC;IAEO,eAAe;QACnB,OAAO,SAAS,CAAC,YAAY,IAAI,CAAC,CAAC;IACvC,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,aAAa,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;;AA7TL,oCA8TC;AA7T2B,6BAAgB,GAAG,GAAG,AAAN,CAAO,CAAC,qBAAqB;AAC7C,0BAAa,GAAG,EAAE,AAAL,CAAM;AACnB,kCAAqB,GAAG,EAAE,AAAL,CAAM,CAAC,OAAO;AACnC,kCAAqB,GAAG,IAAI,AAAP,CAAQ,CAAC,sBAAsB"}