"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EconomySystem = void 0;
class EconomySystem {
    constructor() {
        this.playerMoney = new Map();
        this.buyZones = new Map();
        this.itemCatalog = new Map();
        this.initializeItemCatalog();
    }
    initializeItemCatalog() {
        const items = [
            { id: 'rifle_m4', name: 'M4 Rifle', type: 'weapon', price: 3000, tier: 2 },
            { id: 'smg_mp5', name: 'MP5 SMG', type: 'weapon', price: 2000, tier: 1 },
            { id: 'sniper_awp', name: 'AWP Sniper', type: 'weapon', price: 4500, tier: 3 },
            { id: 'armor_1', name: 'Light Armor', type: 'armor', price: 1000, tier: 1 },
            { id: 'armor_3', name: 'Heavy Armor', type: 'armor', price: 2500, tier: 3 },
            { id: 'grenade_smoke', name: 'Smoke Grenade', type: 'grenade', price: 300, tier: 1 },
            { id: 'grenade_flash', name: 'Flash Grenade', type: 'grenade', price: 200, tier: 1 },
            { id: 'healkit_basic', name: 'Basic Heal Kit', type: 'healkit', price: 500, tier: 1 },
            { id: 'utility_drone', name: 'Recon Drone', type: 'utility', price: 1500, tier: 2 }
        ];
        items.forEach(item => this.itemCatalog.set(item.id, item));
    }
    initializePlayerMoney(playerId) {
        this.playerMoney.set(playerId, EconomySystem.INITIAL_MONEY);
    }
    addKillReward(playerId) {
        const currentMoney = this.playerMoney.get(playerId) || 0;
        this.playerMoney.set(playerId, currentMoney + EconomySystem.KILL_REWARD);
    }
    addContractReward(playerId, contractTier) {
        const reward = EconomySystem.CONTRACT_REWARD_BASE * contractTier;
        const currentMoney = this.playerMoney.get(playerId) || 0;
        this.playerMoney.set(playerId, currentMoney + reward);
    }
    transferMoney(fromPlayerId, toPlayerId, amount) {
        const fromPlayerMoney = this.playerMoney.get(fromPlayerId) || 0;
        if (fromPlayerMoney < amount)
            return false;
        this.playerMoney.set(fromPlayerId, fromPlayerMoney - amount);
        const toPlayerMoney = this.playerMoney.get(toPlayerId) || 0;
        this.playerMoney.set(toPlayerId, toPlayerMoney + amount);
        return true;
    }
    canBuyItem(playerId, itemId) {
        const playerMoney = this.playerMoney.get(playerId) || 0;
        const item = this.itemCatalog.get(itemId);
        return item ? playerMoney >= item.price : false;
    }
    buyItem(playerId, itemId) {
        if (!this.canBuyItem(playerId, itemId))
            return false;
        const item = this.itemCatalog.get(itemId);
        const currentMoney = this.playerMoney.get(playerId);
        this.playerMoney.set(playerId, currentMoney - item.price);
        return true;
    }
    registerBuyZone(buyZone) {
        this.buyZones.set(buyZone.id, buyZone);
    }
    deactivateBuyZone(buyZoneId) {
        this.buyZones.delete(buyZoneId);
    }
    isInBuyZone(position) {
        for (const [id, zone] of this.buyZones) {
            const distance = this.calculateDistance(position, zone.position);
            if (distance <= zone.radius)
                return id;
        }
        return null;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
}
exports.EconomySystem = EconomySystem;
EconomySystem.INITIAL_MONEY = 500;
EconomySystem.KILL_REWARD = 300;
EconomySystem.CONTRACT_REWARD_BASE = 1000;
//# sourceMappingURL=EconomySystem.js.map