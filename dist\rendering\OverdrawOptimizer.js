"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OverdrawOptimizer = void 0;
class OverdrawOptimizer {
    constructor(metrics) {
        this.renderableObjects = [];
        this.metrics = metrics;
        this.viewFrustum = { planes: [] };
        this.cameraPosition = { x: 0, y: 0, z: 0 };
    }
    updateCamera(position, frustum) {
        this.cameraPosition = position;
        this.viewFrustum = frustum;
    }
    addRenderableObject(object) {
        this.renderableObjects.push(object);
    }
    optimizeRenderOrder() {
        // Separa objetos transparentes e opacos
        const transparentObjects = this.renderableObjects.filter(obj => obj.isTransparent);
        const opaqueObjects = this.renderableObjects.filter(obj => !obj.isTransparent);
        // Ordena objetos opacos do mais próximo ao mais distante (front-to-back)
        opaqueObjects.sort((a, b) => {
            const distA = this.calculateDistance(this.cameraPosition, a.position);
            const distB = this.calculateDistance(this.cameraPosition, b.position);
            return distA - distB;
        });
        // Ordena objetos transparentes do mais distante ao mais próximo (back-to-front)
        transparentObjects.sort((a, b) => {
            const distA = this.calculateDistance(this.cameraPosition, a.position);
            const distB = this.calculateDistance(this.cameraPosition, b.position);
            return distB - distA;
        });
        // Atualiza métricas
        this.metrics.updateOverdrawMetrics({
            transparentObjectCount: transparentObjects.length,
            opaqueObjectCount: opaqueObjects.length
        });
        return [...opaqueObjects, ...transparentObjects];
    }
    optimizeOcclusion() {
        const visibleObjects = [];
        let pixelsOverdrawn = 0;
        let maxOverdraw = 0;
        // Calcula área em tela de cada objeto
        this.renderableObjects.forEach(obj => {
            if (this.isVisible(obj)) {
                const screenArea = this.calculateScreenSpaceArea(obj);
                obj.screenSpaceArea = screenArea;
                visibleObjects.push(obj);
                // Estima pixels sobrepostos
                const objOverdraw = this.estimateOverdrawnPixels(obj);
                pixelsOverdrawn += objOverdraw;
                maxOverdraw = Math.max(maxOverdraw, objOverdraw);
            }
        });
        const screenPixels = 1920 * 1080; // Resolução base
        const averageOverdraw = pixelsOverdrawn / screenPixels;
        const gpuOverdrawTime = this.estimateGPUOverdrawTime(pixelsOverdrawn);
        this.metrics.updateOverdrawMetrics({
            pixelsOverdrawn,
            averageOverdraw,
            maxOverdraw,
            gpuOverdrawTime
        });
        return visibleObjects;
    }
    async getOverdrawMetrics() {
        const visibleObjects = this.optimizeOcclusion();
        let totalOverdraw = 0;
        let screenPixels = 1920 * 1080; // Base resolution
        for (const obj of visibleObjects) {
            if (obj.screenSpaceArea) {
                totalOverdraw += this.estimateOverdrawnPixels(obj);
            }
        }
        return totalOverdraw / screenPixels;
    }
    calculateDistance(a, b) {
        const dx = b.x - a.x;
        const dy = b.y - a.y;
        const dz = b.z - a.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    isVisible(obj) {
        // Frustum culling
        for (const plane of this.viewFrustum.planes) {
            const distance = this.distanceToPlane(obj.position, plane);
            const radius = this.calculateBoundingSphereRadius(obj);
            if (distance < -radius) {
                return false;
            }
        }
        return true;
    }
    distanceToPlane(point, plane) {
        return point.x * plane.normal.x +
            point.y * plane.normal.y +
            point.z * plane.normal.z +
            plane.distance;
    }
    calculateScreenSpaceArea(obj) {
        const boundingVolume = this.calculateBoundingVolume(obj);
        const distance = this.calculateDistance(this.cameraPosition, obj.position);
        return boundingVolume / (distance * distance);
    }
    estimateOverdrawnPixels(obj) {
        if (!obj.screenSpaceArea) {
            obj.screenSpaceArea = this.calculateScreenSpaceArea(obj);
        }
        const basePixels = obj.screenSpaceArea * (1920 * 1080);
        return obj.isTransparent ? basePixels * 1.5 : basePixels;
    }
    calculateBoundingVolume(obj) {
        const width = obj.boundingBox.max.x - obj.boundingBox.min.x;
        const height = obj.boundingBox.max.y - obj.boundingBox.min.y;
        const depth = obj.boundingBox.max.z - obj.boundingBox.min.z;
        return width * height * depth;
    }
    calculateBoundingSphereRadius(obj) {
        const width = obj.boundingBox.max.x - obj.boundingBox.min.x;
        const height = obj.boundingBox.max.y - obj.boundingBox.min.y;
        const depth = obj.boundingBox.max.z - obj.boundingBox.min.z;
        return Math.sqrt(width * width + height * height + depth * depth) * 0.5;
    }
    estimateGPUOverdrawTime(pixelsOverdrawn) {
        // Estimativa simplificada: assume 1ns por pixel overdraw
        return pixelsOverdrawn * 0.000001; // Converte para milissegundos
    }
}
exports.OverdrawOptimizer = OverdrawOptimizer;
//# sourceMappingURL=OverdrawOptimizer.js.map