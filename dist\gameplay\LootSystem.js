"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LootSystem = void 0;
class LootSystem {
    constructor() {
        this.lootItems = new Map();
        this.lootZones = new Map();
        this.initializeLootZones();
    }
    initializeLootZones() {
        // Urban zone com alta densidade de loot
        const urbanZone = {
            id: 'urban_center',
            position: { x: 0, y: 0, z: 0 },
            radius: 100,
            density: 1.5,
            tierWeights: { 1: 0.5, 2: 0.3, 3: 0.2 },
            typeWeights: {
                weapon: 0.2,
                ammo: 0.3,
                armor: 0.15,
                healkit: 0.15,
                money: 0.1,
                utility: 0.1
            }
        };
        // Rural zone com menor densidade de loot
        const ruralZone = {
            id: 'rural_area',
            position: { x: 200, y: 0, z: 200 },
            radius: 150,
            density: 0.7,
            tierWeights: { 1: 0.7, 2: 0.2, 3: 0.1 },
            typeWeights: {
                weapon: 0.15,
                ammo: 0.35,
                armor: 0.1,
                healkit: 0.2,
                money: 0.1,
                utility: 0.1
            }
        };
        this.lootZones.set(urbanZone.id, urbanZone);
        this.lootZones.set(ruralZone.id, ruralZone);
    }
    spawnLoot(zoneId) {
        const zone = this.lootZones.get(zoneId);
        if (!zone)
            return;
        const itemCount = Math.floor(zone.radius * zone.density);
        for (let i = 0; i < itemCount; i++) {
            const position = this.getRandomPositionInZone(zone);
            const type = this.getRandomType(zone.typeWeights);
            const tier = this.getRandomTier(zone.tierWeights);
            const lootItem = {
                id: `loot_${zoneId}_${i}`,
                type,
                tier,
                position,
                quantity: type === 'ammo' ? this.getRandomAmmoAmount(tier) : 1
            };
            this.lootItems.set(lootItem.id, lootItem);
        }
    }
    spawnMoneyCache(position) {
        const amount = Math.floor(LootSystem.MONEY_CACHE_MIN +
            Math.random() * (LootSystem.MONEY_CACHE_MAX - LootSystem.MONEY_CACHE_MIN));
        const cacheId = `money_cache_${Date.now()}`;
        const cache = {
            id: cacheId,
            type: 'money',
            tier: 1,
            quantity: amount,
            position
        };
        this.lootItems.set(cacheId, cache);
        return cacheId;
    }
    collectLoot(lootId) {
        const item = this.lootItems.get(lootId);
        if (item) {
            this.lootItems.delete(lootId);
            return item;
        }
        return null;
    }
    getLootInRadius(position, radius) {
        const nearbyLoot = [];
        this.lootItems.forEach(item => {
            if (this.calculateDistance(position, item.position) <= radius) {
                nearbyLoot.push(item);
            }
        });
        return nearbyLoot;
    }
    getRandomPositionInZone(zone) {
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.sqrt(Math.random()) * zone.radius;
        return {
            x: zone.position.x + radius * Math.cos(angle),
            y: zone.position.y,
            z: zone.position.z + radius * Math.sin(angle)
        };
    }
    getRandomType(weights) {
        const total = Object.values(weights).reduce((a, b) => a + b, 0);
        let random = Math.random() * total;
        for (const [type, weight] of Object.entries(weights)) {
            random -= weight;
            if (random <= 0)
                return type;
        }
        return Object.keys(weights)[0];
    }
    getRandomTier(weights) {
        const total = Object.values(weights).reduce((a, b) => a + b, 0);
        let random = Math.random() * total;
        for (const [tier, weight] of Object.entries(weights)) {
            random -= weight;
            if (random <= 0)
                return parseInt(tier);
        }
        return 1;
    }
    getRandomAmmoAmount(tier) {
        const baseAmount = 30;
        return baseAmount * tier;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
}
exports.LootSystem = LootSystem;
LootSystem.MONEY_CACHE_MIN = 100;
LootSystem.MONEY_CACHE_MAX = 250;
//# sourceMappingURL=LootSystem.js.map