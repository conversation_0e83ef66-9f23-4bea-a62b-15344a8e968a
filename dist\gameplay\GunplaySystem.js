"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GunplaySystem = void 0;
class GunplaySystem {
    constructor() {
        this.weapons = new Map();
        this.currentRecoil = { x: 0, y: 0, z: 0 };
        this.shotsInPattern = 0;
        this.lastShotTime = 0;
        this.currentSpread = 0;
        this.initializeWeapons();
    }
    initializeWeapons() {
        // Configura armas padrão com padrões de recuo realistas
        this.setupAK47();
        this.setupM4A4();
        this.setupAWP();
    }
    setupAK47() {
        const ak47 = {
            name: "AK-47",
            damage: 36,
            fireRate: 600,
            reloadTime: 2.5,
            magazineSize: 30,
            recoilPattern: {
                vertical: [2.8, 2.7, 2.5, 2.3, 2.0, 1.8, 1.5],
                horizontal: [0.5, -0.5, 0.7, -0.7, 0.6, -0.6],
                recovery: 0.85,
                firstShotMultiplier: 1.2
            },
            spread: {
                base: 0.02,
                moving: 2.5,
                jumping: 5.0,
                running: 3.0,
                recovery: 0.3
            }
        };
        this.weapons.set("ak47", ak47);
    }
    setupM4A4() {
        const m4a4 = {
            name: "M4A4",
            damage: 33,
            fireRate: 666,
            reloadTime: 3.0,
            magazineSize: 30,
            recoilPattern: {
                vertical: [2.5, 2.4, 2.2, 2.0, 1.8, 1.5, 1.3],
                horizontal: [0.4, -0.4, 0.6, -0.6, 0.5, -0.5],
                recovery: 0.9,
                firstShotMultiplier: 1.1
            },
            spread: {
                base: 0.015,
                moving: 2.3,
                jumping: 4.8,
                running: 2.8,
                recovery: 0.35
            }
        };
        this.weapons.set("m4a4", m4a4);
    }
    setupAWP() {
        const awp = {
            name: "AWP",
            damage: 115,
            fireRate: 41,
            reloadTime: 3.7,
            magazineSize: 5,
            recoilPattern: {
                vertical: [8.0],
                horizontal: [0.1],
                recovery: 0.5,
                firstShotMultiplier: 1.0
            },
            spread: {
                base: 0.0,
                moving: 6.0,
                jumping: 10.0,
                running: 7.0,
                recovery: 0.2
            }
        };
        this.weapons.set("awp", awp);
    }
    fireWeapon(weaponId, playerState, currentTime) {
        const weapon = this.weapons.get(weaponId);
        if (!weapon)
            return { recoil: { x: 0, y: 0, z: 0 }, spread: 0 };
        // Verifica rate of fire
        const timeSinceLastShot = currentTime - this.lastShotTime;
        if (timeSinceLastShot < (60 / weapon.fireRate)) {
            return { recoil: this.currentRecoil, spread: this.currentSpread };
        }
        // Calcula recuo
        this.calculateRecoil(weapon, playerState);
        // Calcula spread
        this.calculateSpread(weapon, playerState);
        // Atualiza estado
        this.lastShotTime = currentTime;
        this.shotsInPattern = (this.shotsInPattern + 1) % GunplaySystem.MAX_RECOIL_SHOTS;
        return {
            recoil: this.currentRecoil,
            spread: this.currentSpread
        };
    }
    calculateRecoil(weapon, playerState) {
        const pattern = weapon.recoilPattern;
        const shotIndex = this.shotsInPattern % pattern.vertical.length;
        // Aplica padrão de recuo
        const verticalRecoil = pattern.vertical[shotIndex];
        const horizontalRecoil = pattern.horizontal[shotIndex % pattern.horizontal.length];
        // Aplica multiplicador do primeiro tiro se necessário
        const multiplier = this.shotsInPattern === 0 ? pattern.firstShotMultiplier : 1.0;
        this.currentRecoil = {
            x: horizontalRecoil * multiplier,
            y: verticalRecoil * multiplier,
            z: 0
        };
    }
    calculateSpread(weapon, playerState) {
        const spread = weapon.spread;
        let totalSpread = spread.base;
        // Aplica multiplicadores baseados no estado do jogador
        if (playerState.isMoving)
            totalSpread *= spread.moving;
        if (playerState.isJumping)
            totalSpread *= spread.jumping;
        if (playerState.isRunning)
            totalSpread *= spread.running;
        this.currentSpread = totalSpread;
    }
    updateSpreadRecovery(deltaTime) {
        // Recupera precisão gradualmente quando não atirando
        this.currentSpread = Math.max(0, this.currentSpread - (deltaTime * GunplaySystem.SPREAD_RECOVERY_RATE));
    }
    simulateProjectile(origin, direction, weaponId) {
        // Simula trajetória da bala com física simplificada
        // para performance em alta taxa de quadros
        return {
            position: origin,
            velocity: {
                x: direction.x * 1000,
                y: direction.y * 1000,
                z: direction.z * 1000
            }
        };
    }
}
exports.GunplaySystem = GunplaySystem;
GunplaySystem.MAX_RECOIL_SHOTS = 30;
GunplaySystem.SPREAD_RECOVERY_RATE = 0.1;
GunplaySystem.MAX_BULLET_DISTANCE = 8192;
//# sourceMappingURL=GunplaySystem.js.map