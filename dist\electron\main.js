"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const electron_updater_1 = require("electron-updater");
const path = __importStar(require("path"));
class TacticalNexusApp {
    constructor() {
        this.mainWindow = null;
        this.splashWindow = null;
        this.initializeApp();
    }
    initializeApp() {
        // Configurações do app
        electron_1.app.setName('Tactical Nexus');
        electron_1.app.setVersion('1.0.0');
        // Event listeners do app
        electron_1.app.whenReady().then(() => {
            this.createSplashWindow();
            this.setupAutoUpdater();
            this.createMenu();
            this.setupIpcHandlers();
        });
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });
        // Configurações de segurança
        electron_1.app.on('web-contents-created', (_, contents) => {
            contents.on('new-window', (navigationEvent, navigationUrl) => {
                navigationEvent.preventDefault();
                electron_1.shell.openExternal(navigationUrl);
            });
        });
    }
    createSplashWindow() {
        this.splashWindow = new electron_1.BrowserWindow({
            width: 800,
            height: 600,
            frame: false,
            alwaysOnTop: true,
            transparent: true,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true
            }
        });
        // Carrega tela de splash
        this.splashWindow.loadFile(path.join(__dirname, '../assets/splash.html'));
        // Simula carregamento do jogo
        setTimeout(() => {
            this.createMainWindow();
            if (this.splashWindow) {
                this.splashWindow.close();
                this.splashWindow = null;
            }
        }, 3000);
    }
    createMainWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1920,
            height: 1080,
            minWidth: 1280,
            minHeight: 720,
            show: false,
            icon: path.join(__dirname, '../assets/icon.ico'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            }
        });
        // Carrega a aplicação principal
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.loadURL('http://localhost:3000');
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
        }
        // Mostra janela quando pronta
        this.mainWindow.once('ready-to-show', () => {
            if (this.mainWindow) {
                this.mainWindow.show();
                this.mainWindow.focus();
            }
        });
        // Maximiza por padrão para melhor experiência de jogo
        this.mainWindow.maximize();
        // Event listeners da janela
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // Previne navegação externa
        this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            if (parsedUrl.origin !== 'http://localhost:3000' && parsedUrl.origin !== 'file://') {
                event.preventDefault();
            }
        });
    }
    createMenu() {
        const template = [
            {
                label: 'Arquivo',
                submenu: [
                    {
                        label: 'Configurações',
                        accelerator: 'Ctrl+,',
                        click: () => {
                            this.mainWindow?.webContents.send('open-settings');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Sair',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        }
                    }
                ]
            },
            {
                label: 'Jogo',
                submenu: [
                    {
                        label: 'Jogo Rápido',
                        accelerator: 'F1',
                        click: () => {
                            this.mainWindow?.webContents.send('quick-play');
                        }
                    },
                    {
                        label: 'Buscar Partida',
                        accelerator: 'F2',
                        click: () => {
                            this.mainWindow?.webContents.send('find-match');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Estatísticas',
                        accelerator: 'F3',
                        click: () => {
                            this.mainWindow?.webContents.send('show-stats');
                        }
                    }
                ]
            },
            {
                label: 'Visualizar',
                submenu: [
                    { role: 'reload', label: 'Recarregar' },
                    { role: 'forceReload', label: 'Forçar Recarga' },
                    { role: 'toggleDevTools', label: 'Ferramentas do Desenvolvedor' },
                    { type: 'separator' },
                    { role: 'resetZoom', label: 'Zoom Padrão' },
                    { role: 'zoomIn', label: 'Aumentar Zoom' },
                    { role: 'zoomOut', label: 'Diminuir Zoom' },
                    { type: 'separator' },
                    { role: 'togglefullscreen', label: 'Tela Cheia' }
                ]
            },
            {
                label: 'Ajuda',
                submenu: [
                    {
                        label: 'Sobre o Tactical Nexus',
                        click: () => {
                            electron_1.dialog.showMessageBox(this.mainWindow, {
                                type: 'info',
                                title: 'Sobre',
                                message: 'Tactical Nexus v1.0.0',
                                detail: 'O futuro dos jogos táticos está aqui!\n\nDesenvolvido com tecnologia de ponta para 999 FPS.',
                                buttons: ['OK']
                            });
                        }
                    },
                    {
                        label: 'Verificar Atualizações',
                        click: () => {
                            electron_updater_1.autoUpdater.checkForUpdatesAndNotify();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Relatório de Bug',
                        click: () => {
                            electron_1.shell.openExternal('https://github.com/tactical-nexus/tactical-nexus/issues');
                        }
                    },
                    {
                        label: 'Suporte',
                        click: () => {
                            electron_1.shell.openExternal('https://tactical-nexus.com/support');
                        }
                    }
                ]
            }
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupAutoUpdater() {
        electron_updater_1.autoUpdater.checkForUpdatesAndNotify();
        electron_updater_1.autoUpdater.on('checking-for-update', () => {
            console.log('🔍 Verificando atualizações...');
        });
        electron_updater_1.autoUpdater.on('update-available', (info) => {
            console.log('📦 Atualização disponível:', info.version);
            electron_1.dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'Atualização Disponível',
                message: `Uma nova versão (${info.version}) está disponível!`,
                detail: 'A atualização será baixada automaticamente.',
                buttons: ['OK']
            });
        });
        electron_updater_1.autoUpdater.on('update-not-available', () => {
            console.log('✅ Jogo atualizado');
        });
        electron_updater_1.autoUpdater.on('error', (err) => {
            console.error('❌ Erro na atualização:', err);
        });
        electron_updater_1.autoUpdater.on('download-progress', (progressObj) => {
            const logMessage = `📥 Baixando: ${progressObj.percent.toFixed(2)}%`;
            console.log(logMessage);
            this.mainWindow?.webContents.send('update-progress', {
                percent: progressObj.percent,
                transferred: progressObj.transferred,
                total: progressObj.total
            });
        });
        electron_updater_1.autoUpdater.on('update-downloaded', () => {
            console.log('✅ Atualização baixada');
            electron_1.dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'Atualização Pronta',
                message: 'A atualização foi baixada e será instalada ao reiniciar o jogo.',
                detail: 'Deseja reiniciar agora?',
                buttons: ['Reiniciar Agora', 'Mais Tarde']
            }).then((result) => {
                if (result.response === 0) {
                    electron_updater_1.autoUpdater.quitAndInstall();
                }
            });
        });
    }
    setupIpcHandlers() {
        // Handler para obter informações do sistema
        electron_1.ipcMain.handle('get-system-info', () => {
            return {
                platform: process.platform,
                arch: process.arch,
                version: electron_1.app.getVersion(),
                electronVersion: process.versions.electron,
                nodeVersion: process.versions.node
            };
        });
        // Handler para configurações de performance
        electron_1.ipcMain.handle('get-performance-settings', () => {
            return {
                targetFPS: 999,
                resolution: '2560x1440',
                graphicsQuality: 'ultra',
                vsync: false
            };
        });
        // Handler para estatísticas do jogo
        electron_1.ipcMain.handle('get-game-stats', () => {
            return {
                fps: Math.floor(Math.random() * 50) + 950, // 950-999 FPS
                ping: Math.floor(Math.random() * 30) + 10, // 10-40ms
                players: Math.floor(Math.random() * 500) + 1000 // 1000-1500 players
            };
        });
        // Handler para minimizar/maximizar janela
        electron_1.ipcMain.on('window-minimize', () => {
            this.mainWindow?.minimize();
        });
        electron_1.ipcMain.on('window-maximize', () => {
            if (this.mainWindow?.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            else {
                this.mainWindow?.maximize();
            }
        });
        electron_1.ipcMain.on('window-close', () => {
            this.mainWindow?.close();
        });
        // Handler para abrir links externos
        electron_1.ipcMain.on('open-external', (_, url) => {
            electron_1.shell.openExternal(url);
        });
    }
    getMainWindow() {
        return this.mainWindow;
    }
}
// Inicializa a aplicação
const tacticalNexusApp = new TacticalNexusApp();
exports.default = tacticalNexusApp;
//# sourceMappingURL=main.js.map