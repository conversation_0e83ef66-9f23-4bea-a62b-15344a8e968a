"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const util_1 = require("util");
const fs_1 = require("fs");
const path_1 = require("path");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
async function setupTextureTools() {
    console.log('Configurando ferramentas de otimização de textura...');
    const toolsDir = (0, path_1.join)(__dirname, '..', 'tools');
    const ktxToolsDir = (0, path_1.join)(toolsDir, 'ktx-tools');
    // Cria diretórios se não existirem
    if (!(0, fs_1.existsSync)(toolsDir)) {
        (0, fs_1.mkdirSync)(toolsDir);
    }
    if (!(0, fs_1.existsSync)(ktxToolsDir)) {
        (0, fs_1.mkdirSync)(ktxToolsDir);
    }
    try {
        // Instala KTX-Software via npm
        console.log('Instalando KTX-Software...');
        await execAsync('npm install @khronosgroup/ktx-software');
        // Configura ambiente para processamento de texturas
        console.log('Configurando ambiente...');
        const config = {
            textureFormats: {
                desktop: {
                    primary: 'BC7',
                    fallback: 'BC3'
                },
                mobile: {
                    primary: 'ASTC',
                    fallback: 'ETC2'
                }
            },
            compressionSettings: {
                BC7: {
                    quality: 'max',
                    mipmaps: true
                },
                ASTC: {
                    blockSize: '4x4',
                    quality: 'medium'
                }
            }
        };
        // Salva configuração
        const fs = require('fs');
        fs.writeFileSync((0, path_1.join)(toolsDir, 'texture-config.json'), JSON.stringify(config, null, 2));
        console.log('Configuração concluída com sucesso!');
        console.log('Diretório de ferramentas:', toolsDir);
        console.log('Arquivo de configuração criado em:', (0, path_1.join)(toolsDir, 'texture-config.json'));
    }
    catch (error) {
        console.error('Erro durante a configuração:', error);
        process.exit(1);
    }
}
// Executa setup se chamado diretamente
if (require.main === module) {
    setupTextureTools();
}
//# sourceMappingURL=setupTextureTools.js.map