"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextureOptimizer = void 0;
const OptimizationMetrics_1 = require("./OptimizationMetrics");
const path_1 = require("path");
const fs_1 = require("fs");
const perf_hooks_1 = require("perf_hooks");
class TextureOptimizer {
    constructor() {
        this.ktxConfig = {
            textureFormats: {
                desktop: { primary: 'BC7', fallback: 'BC3' },
                mobile: { primary: 'ASTC', fallback: 'ETC2' }
            },
            compressionSettings: {
                BC7: { quality: 'max', mipmaps: true },
                BC3: { quality: 'high', mipmaps: true },
                ASTC: { blockSize: '4x4', quality: 'medium', mipmaps: true },
                ETC2: { quality: 'medium', mipmaps: true }
            }
        };
        this.metricsCollector = new OptimizationMetrics_1.OptimizationMetricsCollector();
        this.loadConfig();
    }
    loadConfig() {
        try {
            const configPath = (0, path_1.join)(__dirname, '..', 'tools', 'texture-config.json');
            const configData = (0, fs_1.readFileSync)(configPath, 'utf8');
            this.ktxConfig = JSON.parse(configData);
        }
        catch (error) {
            console.warn('Erro ao carregar configuração KTX, usando padrões:', error);
        }
    }
    async optimizeTexture(textureData, width, height, config = {}) {
        const operationId = `texture-${Date.now()}`;
        this.metricsCollector.startOperation(operationId);
        const defaultConfig = {
            format: this.ktxConfig.textureFormats.desktop.primary,
            quality: 128,
            generateMipmaps: true,
            platform: 'desktop'
        };
        const finalConfig = { ...defaultConfig, ...config };
        try {
            const ktx = require('@khronosgroup/ktx-software');
            const startTime = perf_hooks_1.performance.now();
            // Converte dados para o formato esperado
            const inputBuffer = textureData instanceof Uint8Array ?
                textureData :
                new Uint8Array(textureData);
            // Seleciona configurações de compressão
            const formatConfig = this.ktxConfig.compressionSettings[finalConfig.format];
            const compressionOptions = {
                quality: formatConfig.quality,
                mipmaps: formatConfig.mipmaps,
                blockSize: formatConfig.blockSize
            };
            // Comprime a textura
            const compressedData = await ktx.compress(inputBuffer, {
                width,
                height,
                format: finalConfig.format,
                ...compressionOptions
            });
            const endTime = perf_hooks_1.performance.now();
            const processingTime = endTime - startTime;
            // Calcula métricas
            const originalSize = textureData.byteLength;
            const compressedSize = compressedData.byteLength;
            const compressionRatio = originalSize / compressedSize;
            // Análise de qualidade
            const metrics = await this.analyzeQuality(inputBuffer, compressedData);
            // Registra métricas finais
            this.metricsCollector.endOperation(operationId, {
                originalSize,
                optimizedSize: compressedSize,
                compressionRatio,
                processingTimeMs: processingTime,
                memoryReduction: ((originalSize - compressedSize) / originalSize) * 100,
                format: finalConfig.format,
                quality: finalConfig.quality,
                psnr: metrics.psnr
            });
            return {
                data: new Uint8Array(compressedData),
                width,
                height,
                format: finalConfig.format,
                originalSize,
                compressedSize,
                metrics: {
                    compressionRatio,
                    processingTimeMs: processingTime,
                    psnr: metrics.psnr,
                    ssim: metrics.ssim
                }
            };
        }
        catch (error) {
            console.error('Erro na otimização de textura:', error);
            throw error;
        }
    }
    async analyzeQuality(original, compressed) {
        const psnr = this.calculatePSNR(original, compressed);
        const ssim = await this.calculateSSIM(original, compressed);
        return { psnr, ssim };
    }
    calculatePSNR(original, compressed) {
        let mse = 0;
        const len = Math.min(original.length, compressed.length);
        for (let i = 0; i < len; i++) {
            const diff = original[i] - compressed[i];
            mse += diff * diff;
        }
        mse /= len;
        if (mse === 0)
            return Infinity;
        const maxVal = 255;
        return 20 * Math.log10(maxVal) - 10 * Math.log10(mse);
    }
    async calculateSSIM(original, compressed) {
        // Implementação simplificada do SSIM
        // TODO: Implementar versão completa com janelas deslizantes
        let ssim = 0;
        const windowSize = 8;
        const len = Math.min(original.length, compressed.length);
        const windows = Math.floor(len / windowSize);
        for (let i = 0; i < windows; i++) {
            const start = i * windowSize;
            const end = start + windowSize;
            const origWindow = original.slice(start, end);
            const compWindow = compressed.slice(start, end);
            const muX = this.mean(origWindow);
            const muY = this.mean(compWindow);
            const sigmaX = this.variance(origWindow, muX);
            const sigmaY = this.variance(compWindow, muY);
            const sigmaXY = this.covariance(origWindow, compWindow, muX, muY);
            const c1 = Math.pow(0.01 * 255, 2);
            const c2 = Math.pow(0.03 * 255, 2);
            const numerator = (2 * muX * muY + c1) * (2 * sigmaXY + c2);
            const denominator = (muX * muX + muY * muY + c1) * (sigmaX + sigmaY + c2);
            ssim += numerator / denominator;
        }
        return ssim / windows;
    }
    mean(data) {
        return data.reduce((sum, val) => sum + val, 0) / data.length;
    }
    variance(data, mean) {
        return data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    }
    covariance(data1, data2, mean1, mean2) {
        let sum = 0;
        for (let i = 0; i < data1.length; i++) {
            sum += (data1[i] - mean1) * (data2[i] - mean2);
        }
        return sum / data1.length;
    }
    async getMemoryUsage() {
        let totalMemory = 0;
        const textures = await this.getLoadedTextures();
        for (const texture of textures) {
            totalMemory += texture.compressedSize;
        }
        return totalMemory;
    }
    async getLoadedTextures() {
        // Implementação real para obter texturas carregadas
        return [];
    }
}
exports.TextureOptimizer = TextureOptimizer;
//# sourceMappingURL=TextureOptimizer.js.map