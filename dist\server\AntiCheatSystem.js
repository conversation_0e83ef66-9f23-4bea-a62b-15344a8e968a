"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AntiCheatSystem = void 0;
const ReplayAnalyzer_1 = require("./ReplayAnalyzer");
class AntiCheatSystem {
    constructor(config) {
        this.config = config;
        this.playerMetrics = new Map();
        this.suspiciousPlayers = new Map();
        this.initializeMLModel();
        this.replayAnalyzer = new ReplayAnalyzer_1.ReplayAnalyzer(replayManager, this.mlModel);
    }
    initializeMLModel() {
        // Inicialização do modelo de ML usando TensorFlow.js
        // Este é um placeholder - a implementação real usaria um modelo treinado
        this.mlModel = {
            predict: (data) => ({
                dataSync: () => [Math.random()], // Simulação de predição
            })
        };
    }
    processPlayerInput(playerId, input) {
        let metrics = this.playerMetrics.get(playerId);
        if (!metrics) {
            metrics = this.initializePlayerMetrics();
            this.playerMetrics.set(playerId, metrics);
        }
        metrics.inputStats.inputs.push(input);
        metrics.inputStats.timestamps.push(Date.now());
        // Análise em tempo real de inputs suspeitos
        this.detectAimbot(playerId, input);
        this.detectSpeedhack(playerId, input);
    }
    processPlayerPosition(playerId, position, timestamp) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        metrics.movementStats.positions.push(position);
        metrics.movementStats.timestamps.push(timestamp);
        // Limpa dados antigos
        this.cleanOldMetrics(metrics, timestamp);
        // Analisa movimentos suspeitos
        this.detectWallhack(playerId, position);
    }
    processHitRegistration(playerId, hit) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        // Analisa precisão e padrões de tiro
        this.detectAimAssist(playerId, hit);
        this.detectRecoilHack(playerId, hit);
    }
    async queueReplayAnalysis(replayId, priority = 0.5) {
        await this.replayAnalyzer.queueReplayForAnalysis(replayId, priority, 'routine_analysis');
    }
    async handleReportedPlayer(playerId, reportType) {
        const playerReplays = await this.replayManager.getPlayerRecentReplays(playerId);
        // Prioriza análise de replays de jogadores reportados
        for (const replay of playerReplays) {
            await this.replayAnalyzer.queueReplayForAnalysis(replay.id, 0.8, // Alta prioridade para jogadores reportados
            'player_reported');
        }
    }
    async updateFromReplayEvidence(playerId, evidence) {
        const record = this.suspiciousPlayers.get(playerId) || {
            flags: new Set(),
            detectionCount: 0,
            lastDetectionTime: Date.now()
        };
        // Atualiza flags com evidências do replay
        record.flags.add(evidence.event.eventType);
        record.detectionCount++;
        // Se confiança alta no replay, aumenta prioridade de monitoramento
        if (evidence.metadata.confidence > 0.85) {
            await this.increaseSurveillance(playerId);
        }
        this.suspiciousPlayers.set(playerId, record);
    }
    async increaseSurveillance(playerId) {
        // Aumenta frequência de checks em tempo real
        this.addToHighPriorityMonitoring(playerId);
        // Analisa replays mais recentes
        const recentReplays = await this.replayManager.getPlayerRecentReplays(playerId);
        for (const replay of recentReplays) {
            await this.replayAnalyzer.queueReplayForAnalysis(replay.id, 0.9, // Prioridade muito alta
            'suspicious_activity_followup');
        }
    }
    // Funções utilitárias para cálculos estatísticos
    calculateAverage(arr) {
        if (arr.length === 0)
            return 0;
        return arr.reduce((a, b) => a + b, 0) / arr.length;
    }
    calculateStdDev(arr) {
        if (arr.length === 0)
            return 0;
        const avg = this.calculateAverage(arr);
        const squareDiffs = arr.map(value => Math.pow(value - avg, 2));
        return Math.sqrt(this.calculateAverage(squareDiffs));
    }
    detectAimbot(playerId, input) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        const recentSnapAngles = metrics.aimStats.snapAngles.slice(-10);
        const averageSnapAngle = this.calculateAverage(recentSnapAngles);
        const snapStdDev = this.calculateStdDev(recentSnapAngles);
        if (averageSnapAngle > this.config.aimSnapThreshold) {
            this.flagSuspiciousActivity(playerId, 'aimbot', 0.6);
        }
        // Análise baseada em ML dos padrões de mira
        const mlFeatures = this.extractMLFeatures(metrics);
        const prediction = this.mlModel.predict(mlFeatures);
        const confidence = prediction.dataSync()[0];
        if (confidence > AntiCheatSystem.SUSPICIOUS_THRESHOLD) {
            this.flagSuspiciousActivity(playerId, 'ml_aimbot', confidence);
        }
    }
    extractMLFeatures(metrics) {
        const features = [
            metrics.aimStats.accuracy,
            metrics.aimStats.headshots / Math.max(1, metrics.inputStats.inputs.length),
            this.calculateAverage(metrics.movementStats.speeds),
            metrics.wallStats.suspiciousWallbangs,
            this.calculateStdDev(metrics.aimStats.snapAngles),
            this.calculateAverage(metrics.aimStats.reactionTimes)
        ];
        return new Float32Array(features);
    }
    detectSpeedhack(playerId, input) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        const recentPositions = metrics.movementStats.positions.slice(-5);
        if (recentPositions.length < 2)
            return;
        const speeds = this.calculateSpeeds(recentPositions, metrics.movementStats.timestamps.slice(-5));
        const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
        if (avgSpeed > this.config.speedhackThreshold) {
            this.flagPlayer(playerId, 'speedhack', 0.7);
        }
    }
    detectWallhack(playerId, position) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        // Análise de rastreamento através de paredes
        for (const [targetId, trackCount] of metrics.wallStats.trackedTargets) {
            if (trackCount > this.config.wallhackDetectionInterval) {
                this.flagSuspiciousActivity(playerId, 'wallhack', 0.7);
                break;
            }
        }
    }
    detectAimAssist(playerId, hit) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        // Analisa padrões de mira usando ML
        const features = this.extractAimFeatures(metrics);
        const prediction = this.mlModel.predict(features);
        const probability = prediction.dataSync()[0];
        if (probability > AntiCheatSystem.SUSPICIOUS_THRESHOLD) {
            this.flagPlayer(playerId, 'aimassist', probability);
        }
    }
    detectRecoilHack(playerId, hit) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return;
        // Analisa padrões de recuo
        const recoilPattern = this.analyzeRecoilPattern(metrics);
        if (recoilPattern.deviation < this.config.recoilPatternDeviation) {
            this.flagPlayer(playerId, 'recoilhack', 0.8);
        }
    }
    flagPlayer(playerId, flagType, confidence) {
        let suspicious = this.suspiciousPlayers.get(playerId);
        if (!suspicious) {
            suspicious = {
                flags: new Set(),
                detectionCount: 0,
                lastDetectionTime: Date.now()
            };
            this.suspiciousPlayers.set(playerId, suspicious);
        }
        suspicious.flags.add(flagType);
        suspicious.detectionCount++;
        suspicious.lastDetectionTime = Date.now();
        if (suspicious.detectionCount >= 5) {
            this.issueBan({
                playerId,
                cheatType: flagType,
                confidence,
                duration: this.calculateBanDuration(1),
                startTime: Date.now(),
                evidence: this.collectEvidence(playerId)
            });
        }
    }
    flagSuspiciousActivity(playerId, cheatType, confidence) {
        let record = this.suspiciousPlayers.get(playerId);
        if (!record) {
            record = {
                flags: new Set(),
                detectionCount: 0,
                lastDetectionTime: Date.now()
            };
            this.suspiciousPlayers.set(playerId, record);
        }
        record.flags.add(cheatType);
        record.detectionCount++;
        record.lastDetectionTime = Date.now();
        // Sistema de banimento progressivo
        if (record.detectionCount >= 3) {
            const banDuration = this.calculateBanDuration(record.detectionCount);
            this.issueBan({
                playerId,
                cheatType,
                confidence,
                duration: banDuration,
                startTime: Date.now(),
                evidence: this.collectEvidence(playerId)
            });
        }
    }
    issueBan(banData) {
        // Envia para o sistema de banimento
        this.notifyBanSystem(banData);
        // Limpa os dados do jogador
        this.playerMetrics.delete(banData.playerId);
        this.suspiciousPlayers.delete(banData.playerId);
    }
    calculateBanDuration(detectionCount) {
        // Duração progressiva de banimento
        const baseDuration = 24 * 60 * 60 * 1000; // 24 horas em ms
        return Math.min(baseDuration * Math.pow(2, detectionCount - 3), 30 * 24 * 60 * 60 * 1000); // Máximo de 30 dias
    }
    collectEvidence(playerId) {
        const metrics = this.playerMetrics.get(playerId);
        if (!metrics)
            return null;
        return {
            aimStats: { ...metrics.aimStats },
            movementStats: { ...metrics.movementStats },
            wallStats: { ...metrics.wallStats },
            inputStats: {
                inputs: metrics.inputStats.inputs.slice(-100), // Últimos 100 inputs
                timestamps: metrics.inputStats.timestamps.slice(-100)
            }
        };
    }
    cleanOldMetrics(metrics, currentTimestamp) {
        const maxAge = currentTimestamp - AntiCheatSystem.MAX_METRICS_AGE;
        metrics.aimStats.snapAngles = metrics.aimStats.snapAngles.slice(-100);
        metrics.aimStats.reactionTimes = metrics.aimStats.reactionTimes.slice(-100);
        metrics.movementStats.speeds = metrics.movementStats.speeds.slice(-100);
        metrics.movementStats.positions = metrics.movementStats.positions.slice(-100);
        // Limpa timestamps antigos
        metrics.movementStats.timestamps = metrics.movementStats.timestamps.filter(t => t > maxAge);
        metrics.inputStats.timestamps = metrics.inputStats.timestamps.filter(t => t > maxAge);
    }
    initializePlayerMetrics() {
        return {
            aimStats: {
                snapAngles: [],
                headshots: 0,
                accuracy: 0,
                reactionTimes: []
            },
            movementStats: {
                speeds: [],
                positions: [],
                timestamps: []
            },
            wallStats: {
                wallbangs: 0,
                suspiciousWallbangs: 0,
                trackedTargets: new Map()
            },
            inputStats: {
                inputs: [],
                timestamps: []
            }
        };
    }
    calculateSpeeds(positions, timestamps) {
        const speeds = [];
        for (let i = 1; i < positions.length; i++) {
            const distance = this.calculateDistance(positions[i - 1], positions[i]);
            const time = (timestamps[i] - timestamps[i - 1]) / 1000; // segundos
            speeds.push(distance / time);
        }
        return speeds;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos2.x - pos1.x;
        const dy = pos2.y - pos1.y;
        const dz = pos2.z - pos1.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    extractAimFeatures(metrics) {
        // Extrai características para análise de ML
        return [
            metrics.aimStats.accuracy,
            metrics.aimStats.headshots / Math.max(1, metrics.inputStats.inputs.length),
            Math.average(metrics.aimStats.reactionTimes),
            Math.stddev(metrics.aimStats.snapAngles)
        ];
    }
    analyzeRecoilPattern(metrics) {
        // Análise de padrão de recuo
        return { deviation: 1.0 }; // Placeholder
    }
    notifyBanSystem(banData) {
        // Envia os dados do ban para o sistema central de banimento
        // Implementação dependerá da infraestrutura específica
        console.log('Ban issued:', banData);
    }
}
exports.AntiCheatSystem = AntiCheatSystem;
AntiCheatSystem.DETECTION_INTERVAL = 1000; // 1 segundo
AntiCheatSystem.MAX_METRICS_AGE = 300000; // 5 minutos
AntiCheatSystem.SUSPICIOUS_THRESHOLD = 0.85;
//# sourceMappingURL=AntiCheatSystem.js.map