"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIG = void 0;
exports.DEFAULT_CONFIG = {
    analysisInterval: 1000,
    maxReplayAge: 7 * 24 * 60 * 60 * 1000, // 7 dias
    maxConcurrentAnalysis: 4,
    aimbot: {
        snapAngleThreshold: 45,
        consistencyThreshold: 0.95,
        reactionTimeMin: 150
    },
    wallhack: {
        trackingThreshold: 0.8,
        preAimThreshold: 0.7,
        wallbangRatio: 0.3
    },
    speedhack: {
        velocityThreshold: 800,
        teleportThreshold: 50,
        accelerationMax: 2000
    },
    recoil: {
        patternDeviation: 0.1,
        compensationThreshold: 0.95
    },
    mlModel: {
        confidenceThreshold: 0.85,
        updateInterval: 3600000, // 1 hora
        batchSize: 32
    },
    ban: {
        initialDuration: 24 * 60 * 60 * 1000, // 24 horas
        maxDuration: 30 * 24 * 60 * 60 * 1000, // 30 dias
        warningThreshold: 2,
        escalationFactor: 2
    },
    replay: {
        clipDuration: 30000, // 30 segundos
        preClipBuffer: 5000, // 5 segundos antes
        postClipBuffer: 5000, // 5 segundos depois
        maxClipsPerReplay: 10
    }
};
//# sourceMappingURL=AntiCheatConfig.js.map