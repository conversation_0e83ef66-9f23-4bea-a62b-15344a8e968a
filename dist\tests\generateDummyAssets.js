"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = require("fs");
const path_1 = require("path");
function generateDummyWav(durationSeconds, sampleRate = 48000) {
    // Formato WAV simplificado
    const numChannels = 1;
    const bitsPerSample = 16;
    const blockAlign = numChannels * (bitsPerSample / 8);
    const byteRate = sampleRate * blockAlign;
    const dataSize = sampleRate * durationSeconds * blockAlign;
    const chunkSize = 36 + dataSize;
    const buffer = Buffer.alloc(44 + dataSize);
    // WAV Header
    buffer.write('RIFF', 0);
    buffer.writeUInt32LE(chunkSize, 4);
    buffer.write('WAVE', 8);
    buffer.write('fmt ', 12);
    buffer.writeUInt32LE(16, 16);
    buffer.writeUInt16LE(1, 20);
    buffer.writeUInt16LE(numChannels, 22);
    buffer.writeUInt32LE(sampleRate, 24);
    buffer.writeUInt32LE(byteRate, 28);
    buffer.writeUInt16LE(blockAlign, 32);
    buffer.writeUInt16LE(bitsPerSample, 34);
    buffer.write('data', 36);
    buffer.writeUInt32LE(dataSize, 40);
    // Gera uma onda senoidal simples
    for (let i = 0; i < sampleRate * durationSeconds; i++) {
        const value = Math.sin(2 * Math.PI * 440 * i / sampleRate);
        const sample = Math.floor(value * 32767);
        buffer.writeInt16LE(sample, 44 + i * 2);
    }
    return buffer;
}
// Gera os arquivos de áudio dummy
const assetsPath = (0, path_1.join)(__dirname, '..', '..', 'assets');
// Som de vento (5 segundos)
const windSound = generateDummyWav(5);
(0, fs_1.writeFileSync)((0, path_1.join)(assetsPath, 'audio', 'ambient', 'wind.wav'), windSound);
// Som ambiente de cidade (10 segundos)
const citySound = generateDummyWav(10);
(0, fs_1.writeFileSync)((0, path_1.join)(assetsPath, 'audio', 'ambient', 'city_light.wav'), citySound);
// Música de fundo (30 segundos)
const musicLoop = generateDummyWav(30);
(0, fs_1.writeFileSync)((0, path_1.join)(assetsPath, 'audio', 'music', 'background_loop.wav'), musicLoop);
console.log('Assets de áudio dummy gerados com sucesso!');
//# sourceMappingURL=generateDummyAssets.js.map