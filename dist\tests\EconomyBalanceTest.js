"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const EconomySystem_1 = require("../gameplay/EconomySystem");
const LootSystem_1 = require("../gameplay/LootSystem");
const InventorySystem_1 = require("../gameplay/InventorySystem");
class EconomyBalanceTest {
    constructor() {
        this.economySystem = new EconomySystem_1.EconomySystem();
        this.lootSystem = new LootSystem_1.LootSystem();
        this.inventorySystem = new InventorySystem_1.InventorySystem();
    }
    async runSimulations() {
        const results = [];
        for (let i = 0; i < EconomyBalanceTest.SIMULATION_ROUNDS; i++) {
            const result = await this.simulateMatch();
            results.push(result);
        }
        return results;
    }
    async simulateMatch() {
        const teams = this.initializeTeams();
        const timeSteps = EconomyBalanceTest.ROUND_DURATION;
        let firstPurchaseTime = timeSteps;
        const moneyOverTime = new Array(teams.length).fill(0);
        const itemsPurchased = {};
        // Simula cada segundo do jogo
        for (let time = 0; time < timeSteps; time++) {
            // Simula encontros e combates
            this.simulateCombatEncounters(teams, time);
            // Simula coleta de loot e contratos
            this.simulateLootingAndContracts(teams, time);
            // Simula decisões de compra
            const purchases = this.simulatePurchaseDecisions(teams, time);
            // Registra primeira compra
            if (purchases > 0 && time < firstPurchaseTime) {
                firstPurchaseTime = time;
            }
            // Registra dinheiro de cada time
            teams.forEach((team, index) => {
                moneyOverTime[index] = this.calculateTeamMoney(team);
            });
        }
        // Calcula estatísticas finais
        const finalMoneyValues = teams.map(team => this.calculateTeamMoney(team));
        const averageTeamMoney = finalMoneyValues.reduce((a, b) => a + b, 0) / teams.length;
        const minTeamMoney = Math.min(...finalMoneyValues);
        const maxTeamMoney = Math.max(...finalMoneyValues);
        // Calcula distribuição do dinheiro em faixas
        const moneyDistribution = this.calculateMoneyDistribution(finalMoneyValues);
        return {
            averageTeamMoney,
            minTeamMoney,
            maxTeamMoney,
            moneyDistribution,
            itemsPurchased,
            timeToFirstPurchase: firstPurchaseTime
        };
    }
    initializeTeams() {
        const teams = [];
        for (let t = 0; t < EconomyBalanceTest.TEAMS_PER_MATCH; t++) {
            const team = [];
            for (let p = 0; p < EconomyBalanceTest.PLAYERS_PER_TEAM; p++) {
                const playerId = `player_${t}_${p}`;
                team.push(playerId);
                this.economySystem.initializePlayerMoney(playerId);
                this.inventorySystem.initializePlayerInventory(playerId);
            }
            teams.push(team);
        }
        return teams;
    }
    simulateCombatEncounters(teams, time) {
        // Simula encontros entre times com base no tempo de jogo
        // Quanto mais tempo passa, mais frequentes os encontros
        const encounterChance = Math.min(0.1 + (time / EconomyBalanceTest.ROUND_DURATION) * 0.4, 0.5);
        teams.forEach((teamA, indexA) => {
            teams.forEach((teamB, indexB) => {
                if (indexA < indexB && Math.random() < encounterChance) {
                    this.simulateCombat(teamA, teamB);
                }
            });
        });
    }
    simulateCombat(teamA, teamB) {
        // Simula um combate entre dois times
        // 50% de chance de cada time vencer
        const teamAWins = Math.random() > 0.5;
        const winningTeam = teamAWins ? teamA : teamB;
        const losingTeam = teamAWins ? teamB : teamA;
        // Recompensa o time vencedor
        winningTeam.forEach(playerId => {
            this.economySystem.addKillReward(playerId);
        });
    }
    simulateLootingAndContracts(teams, time) {
        teams.forEach(team => {
            // Chance de encontrar loot baseada no tempo
            const lootChance = Math.max(0.3 - (time / EconomyBalanceTest.ROUND_DURATION) * 0.2, 0.1);
            // Chance de completar contrato baseada no tempo
            const contractChance = Math.max(0.1 - (time / EconomyBalanceTest.ROUND_DURATION) * 0.05, 0.02);
            team.forEach(playerId => {
                if (Math.random() < lootChance) {
                    // Simula encontrar um cache de dinheiro
                    this.economySystem.addContractReward(playerId, 1);
                }
                if (Math.random() < contractChance) {
                    // Simula completar um contrato
                    const contractTier = Math.floor(Math.random() * 3) + 1;
                    this.economySystem.addContractReward(playerId, contractTier);
                }
            });
        });
    }
    simulatePurchaseDecisions(teams, time) {
        let totalPurchases = 0;
        teams.forEach(team => {
            team.forEach(playerId => {
                // Simula decisão de compra baseada no dinheiro disponível
                if (Math.random() < this.calculatePurchaseProbability(playerId, time)) {
                    const purchaseMade = this.simulatePlayerPurchase(playerId);
                    if (purchaseMade)
                        totalPurchases++;
                }
            });
        });
        return totalPurchases;
    }
    calculatePurchaseProbability(playerId, time) {
        // Quanto mais dinheiro o jogador tem, maior a chance de comprar
        // Também aumenta com o tempo de jogo
        const timeModifier = time / EconomyBalanceTest.ROUND_DURATION;
        return Math.min(0.1 + timeModifier * 0.3, 0.4);
    }
    simulatePlayerPurchase(playerId) {
        // Lista de itens que o jogador pode comprar
        const possibleItems = ['rifle_m4', 'smg_mp5', 'armor_3', 'healkit_basic'];
        const randomItem = possibleItems[Math.floor(Math.random() * possibleItems.length)];
        return this.economySystem.buyItem(playerId, randomItem);
    }
    calculateTeamMoney(team) {
        return team.reduce((total, playerId) => {
            return total + (this.economySystem.canBuyItem(playerId, 'rifle_m4') ? 3000 : 0);
        }, 0);
    }
    calculateMoneyDistribution(moneyValues) {
        const distribution = {
            'poor': 0, // < 3000
            'medium': 0, // 3000-6000
            'rich': 0 // > 6000
        };
        moneyValues.forEach(money => {
            if (money < 3000)
                distribution['poor']++;
            else if (money < 6000)
                distribution['medium']++;
            else
                distribution['rich']++;
        });
        return distribution;
    }
    analyzeResults(results) {
        const avgMoneyDistribution = {
            poor: 0,
            medium: 0,
            rich: 0
        };
        let totalAvgMoney = 0;
        let totalMinMoney = 0;
        let totalMaxMoney = 0;
        let totalFirstPurchaseTime = 0;
        results.forEach(result => {
            totalAvgMoney += result.averageTeamMoney;
            totalMinMoney += result.minTeamMoney;
            totalMaxMoney += result.maxTeamMoney;
            totalFirstPurchaseTime += result.timeToFirstPurchase;
            Object.entries(result.moneyDistribution).forEach(([key, value]) => {
                avgMoneyDistribution[key] += value;
            });
        });
        console.log('=== Análise de Balanceamento Econômico ===');
        console.log(`Média de dinheiro por time: $${(totalAvgMoney / results.length).toFixed(2)}`);
        console.log(`Média do dinheiro mínimo: $${(totalMinMoney / results.length).toFixed(2)}`);
        console.log(`Média do dinheiro máximo: $${(totalMaxMoney / results.length).toFixed(2)}`);
        console.log(`Tempo médio para primeira compra: ${(totalFirstPurchaseTime / results.length).toFixed(2)}s`);
        console.log('\nDistribuição média de times por faixa de dinheiro:');
        Object.entries(avgMoneyDistribution).forEach(([key, value]) => {
            console.log(`${key}: ${(value / results.length).toFixed(2)} times`);
        });
    }
}
EconomyBalanceTest.SIMULATION_ROUNDS = 1000;
EconomyBalanceTest.PLAYERS_PER_TEAM = 4;
EconomyBalanceTest.TEAMS_PER_MATCH = 15;
EconomyBalanceTest.ROUND_DURATION = 1200; // 20 minutos em segundos
exports.default = EconomyBalanceTest;
//# sourceMappingURL=EconomyBalanceTest.js.map