{"version": 3, "file": "PerformanceMetrics.js", "sourceRoot": "", "sources": ["../../src/rendering/PerformanceMetrics.ts"], "names": [], "mappings": ";;;AAgBA,MAAa,kBAAkB;IAkB3B;QAhBQ,kBAAa,GAAmC,IAAI,CAAC;QACrD,sBAAiB,GAA6B,IAAI,CAAC;QACnD,qBAAgB,GAA4B,IAAI,CAAC;QAEjD,eAAU,GAA0B;YACxC,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;YAChB,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,IAAI;YAClB,gBAAgB,EAAE,IAAI;YACtB,iBAAiB,EAAE,IAAI;SAC1B,CAAC;QAEM,gBAAW,GAAwC,IAAI,GAAG,EAAE,CAAC;QAC7D,qBAAgB,GAAkC,EAAE,CAAC;QAGzD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YAC/B,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,gBAAgB,CAAC,MAA+B;QAC5C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;IAChC,CAAC;IAED,aAAa,CAAC,QAA2B,EAAE,OAAyB;QAChE,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;IACpC,CAAC;IAEO,oBAAoB;QACxB,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,kBAAkB;QACpB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAc,CAAC,cAAc,EAAE,CAAC;YAEzD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAEvC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;YAED,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,oCAAoC,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,mCAAmC,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAc;QAC3C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,iBAAiB,CAAC,CAAC;QACrD,CAAC;QAED,OAAO;YACH,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE;YACtB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAkB,CAAC,kBAAkB,EAAE;YAC5D,aAAa,EAAE,MAAM,IAAI,CAAC,gBAAiB,CAAC,cAAc,EAAE;YAC5D,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAC7C,aAAa,EAAE,IAAI,CAAC,sBAAsB,EAAE;SAC/C,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,OAA+B;QACnD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,yBAAyB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5E,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,yBAAyB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7F,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,kCAAkC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;YAC7F,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,4BAA4B,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,OAA+B;QACxD,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,IAAI,OAAO,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACvE,WAAW,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,EAAE,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACxD,WAAW,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,GAAG,EAAE,CAAC;YACnE,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,MAAM,GAAsB;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC5C,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACxC,uBAAuB,EAAE,IAAI,CAAC,+BAA+B,EAAE;SAClE,CAAC;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAEO,4BAA4B,CAAC,OAA+B;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,WAAW,GAAG;YAChB,4CAA4C;YAC5C,2BAA2B;YAC3B,4BAA4B;YAC5B,wBAAwB;SAC3B,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACzE,CAAC;IAEO,0BAA0B,CAAC,OAA+B;QAC9D,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,WAAW,GAAG;YAChB,sCAAsC;YACtC,iCAAiC;YACjC,eAAe;YACf,+BAA+B;SAClC,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACzE,CAAC;IAEO,4BAA4B,CAAC,OAA+B;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO;QAEhC,MAAM,WAAW,GAAG;YAChB,mCAAmC;YACnC,wCAAwC;YACxC,8BAA8B;YAC9B,iCAAiC;SACpC,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACzE,CAAC;IAEO,oBAAoB;QACxB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAEvC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;YACxB,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;YAC/B,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO;YACH,UAAU,EAAE,QAAQ,GAAG,UAAU;YACjC,SAAS;YACT,WAAW;YACX,SAAS,EAAE,UAAU;SACxB,CAAC;IACN,CAAC;IAEO,+BAA+B;QACnC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;QAEhD,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,IAAI,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC1C,eAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACjD,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC9C,eAAe,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,UAAU;QACd,OAAO,WAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IACpC,CAAC;IAEO,oBAAoB;QACxB,wCAAwC;QACxC,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,iBAAiB;IACzD,CAAC;IAEO,YAAY;QAChB,+CAA+C;QAC/C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,sBAAsB;QAC1B,wDAAwD;QACxD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,8BAA8B;QAClC,oDAAoD;QACpD,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,wBAAwB;IACxE,CAAC;IAED,SAAS,CAAC,QAAmC;QACzC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEO,WAAW,CAAC,OAAe;QAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,qBAAqB;QACjB,sCAAsC;QACtC,OAAO;YACH,UAAU,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,eAAe;YACtD,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,QAAQ;YAChD,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,YAAY;YAC/D,eAAe,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,UAAU;YACtD,aAAa,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,QAAQ;YACpD,cAAc,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,UAAU;YACtD,aAAa,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,eAAe;SACrE,CAAC;IACN,CAAC;IAED,kBAAkB;QACd,OAAO;YACH,eAAe,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YAC1C,WAAW,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YACtC,eAAe,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;YAC1D,eAAe,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;SAC7C,CAAC;IACN,CAAC;IAED,sBAAsB;QAClB,0CAA0C;QAC1C,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,eAAe;IAC/D,CAAC;IAED,YAAY;QACR,2BAA2B;QAC3B,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ;IAChD,CAAC;CACJ;AAtTD,gDAsTC"}