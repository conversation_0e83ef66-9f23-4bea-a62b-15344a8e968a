{"version": 3, "file": "GunplaySystem.js", "sourceRoot": "", "sources": ["../../src/gameplay/GunplaySystem.ts"], "names": [], "mappings": ";;;AAyBA,MAAa,aAAa;IAWtB;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,iBAAiB;QACrB,wDAAwD;QACxD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAEO,SAAS;QACb,MAAM,IAAI,GAAgB;YACtB,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE;gBACX,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC7C,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBAC7C,QAAQ,EAAE,IAAI;gBACd,mBAAmB,EAAE,GAAG;aAC3B;YACD,MAAM,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,GAAG;gBACZ,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,GAAG;aAChB;SACJ,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAEO,SAAS;QACb,MAAM,IAAI,GAAgB;YACtB,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE;gBACX,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC7C,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBAC7C,QAAQ,EAAE,GAAG;gBACb,mBAAmB,EAAE,GAAG;aAC3B;YACD,MAAM,EAAE;gBACJ,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,GAAG;gBACZ,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,IAAI;aACjB;SACJ,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAEO,QAAQ;QACZ,MAAM,GAAG,GAAgB;YACrB,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,CAAC;YACf,aAAa,EAAE;gBACX,QAAQ,EAAE,CAAC,GAAG,CAAC;gBACf,UAAU,EAAE,CAAC,GAAG,CAAC;gBACjB,QAAQ,EAAE,GAAG;gBACb,mBAAmB,EAAE,GAAG;aAC3B;YACD,MAAM,EAAE;gBACJ,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,GAAG;aAChB;SACJ,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;IAEM,UAAU,CACb,QAAgB,EAChB,WAAgB,EAChB,WAAmB;QAEnB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAEhE,wBAAwB;QACxB,MAAM,iBAAiB,GAAG,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QAC1D,IAAI,iBAAiB,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;QACtE,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAE1C,iBAAiB;QACjB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAE1C,kBAAkB;QAClB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,gBAAgB,CAAC;QAEjF,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,MAAM,EAAE,IAAI,CAAC,aAAa;SAC7B,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,MAAmB,EAAE,WAAgB;QACzD,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAEhE,yBAAyB;QACzB,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACnD,MAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEnF,sDAAsD;QACtD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC;QAEjF,IAAI,CAAC,aAAa,GAAG;YACjB,CAAC,EAAE,gBAAgB,GAAG,UAAU;YAChC,CAAC,EAAE,cAAc,GAAG,UAAU;YAC9B,CAAC,EAAE,CAAC;SACP,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,MAAmB,EAAE,WAAgB;QACzD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC;QAE9B,uDAAuD;QACvD,IAAI,WAAW,CAAC,QAAQ;YAAE,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC;QACvD,IAAI,WAAW,CAAC,SAAS;YAAE,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC;QACzD,IAAI,WAAW,CAAC,SAAS;YAAE,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC;QAEzD,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;IACrC,CAAC;IAEM,oBAAoB,CAAC,SAAiB;QACzC,qDAAqD;QACrD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CACzB,CAAC,EACD,IAAI,CAAC,aAAa,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,oBAAoB,CAAC,CACxE,CAAC;IACN,CAAC;IAEM,kBAAkB,CACrB,MAAe,EACf,SAAkB,EAClB,QAAgB;QAEhB,oDAAoD;QACpD,2CAA2C;QAC3C,OAAO;YACH,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE;gBACN,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI;gBACrB,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI;gBACrB,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI;aACxB;SACJ,CAAC;IACN,CAAC;;AAtLL,sCAuLC;AAtL2B,8BAAgB,GAAG,EAAE,CAAC;AACtB,kCAAoB,GAAG,GAAG,CAAC;AAC3B,iCAAmB,GAAG,IAAI,CAAC"}