{"version": 3, "file": "GPUParticleSystem.js", "sourceRoot": "", "sources": ["../../src/rendering/GPUParticleSystem.ts"], "names": [], "mappings": ";;;AA0BA,0BAA0B;AAC1B,IAAK,SAIJ;AAJD,WAAK,SAAS;IACV,iDAAQ,CAAA;IACR,2CAAK,CAAA;IACL,iDAAQ,CAAA;AACZ,CAAC,EAJI,SAAS,KAAT,SAAS,QAIb;AAsBD,MAAa,iBAAiB;IAmB1B,YAAY,MAAiB;QAdZ,kBAAa,GAAG,KAAK,CAAC;QACtB,mBAAc,GAAG,GAAG,CAAC;QAO9B,kBAAa,GAAmB,EAAE,CAAC;QAGnC,oBAAe,GAAW,CAAC,CAAC;QAIhC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,iBAAiB;QACrB,oDAAoD;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,IAAI,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,gDAAgD;YAC/E,KAAK,EAAE,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,MAAM;YACrD,gBAAgB,EAAE,IAAI;SACzB,CAAC,CAAC;IACP,CAAC;IAEO,eAAe;QACnB,sDAAsD;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACrD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;oBACnC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;iBAChC,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;SACJ,CAAC,CAAC;QAEH,2CAA2C;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACnD,MAAM,EAAE,MAAM;YACd,MAAM,EAAE;gBACJ,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;oBACnC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE;iBAC/B,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;YACD,QAAQ,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;oBACnC,IAAI,EAAE,IAAI,CAAC,iBAAiB,EAAE;iBACjC,CAAC;gBACF,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;aACtC;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,mCAAmC;QACnC,YAAY;QACZ,eAAe;QACf,QAAQ;QACR,YAAY;QACZ,kBAAkB;QAClB,4BAA4B;IAChC,CAAC;IAEM,oBAAoB,CAAC,IAAY,EAAE,MAAsB;QAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAC7D,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,iDAAiD;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,cAAc,CAAC,MAAsB;QACzC,OAAO;YACH,GAAG,MAAM;YACT,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,iBAAiB,CAAC,oBAAoB,CAAC;YACnF,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,EAAE,uBAAuB;YACvF,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,oBAAoB;SACjF,CAAC;IACN,CAAC;IAEM,aAAa,CAAC,UAAkB,EAAE,QAAiB,EAAE,KAAa;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,uCAAuC;QACvC,MAAM,cAAc,GAAG,iBAAiB,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC;QACpF,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAEpD,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACnB,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACnC,OAAO;QACX,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,eAAe,IAAI,WAAW,CAAC;IACxC,CAAC;IAEM,sBAAsB;QACzB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAEM,SAAS,CAAC,MAA0B;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAC1D,MAAM,WAAW,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;QAEtD,kCAAkC;QAClC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;QAE1D,sDAAsD;QACtD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QACjE,WAAW,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,WAAW,CAAC,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAEpD,yCAAyC;QACzC,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,KAAK,CAAC;QACrC,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,CAAC,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEO,eAAe,CAAC,MAA0B;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;YAC/B,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAClD,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE;iBAC5C;gBACD;oBACI,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE;wBACN,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;qBAC3C;iBACJ;aACJ;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,MAA0B;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACpC,IAAI,EAAE,EAAE,EAAE,gEAAgE;YAC1E,KAAK,EAAE,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,QAAQ;YACvD,gBAAgB,EAAE,IAAI;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,GAAG,CAAC;YACL,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI;YACX,MAAM,CAAC,QAAQ;SAClB,CAAC,CAAC;QAEH,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,sBAAsB,CAAC,KAAa;QACxC,uDAAuD;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAC1C,IAAI,CAAC,eAAe,IAAI,YAAY,CAAC;IACzC,CAAC;IAEO,iBAAiB,CAAC,MAAsB,EAAE,QAAiB,EAAE,KAAa;QAC9E,2DAA2D;QAC3D,mDAAmD;QACnD,yCAAyC;QACzC,2CAA2C;IAC/C,CAAC;IAEM,MAAM,CAAC,SAAiB;QAC3B,6DAA6D;QAC7D,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,kBAAkB,CAAC,SAAiB;QACxC,oCAAoC;QACpC,kCAAkC;QAClC,qCAAqC;QACrC,iCAAiC;QACjC,kBAAkB;IACtB,CAAC;IAEO,iBAAiB;QACrB,iDAAiD;QACjD,iCAAiC;QACjC,yBAAyB;QACzB,8BAA8B;IAClC,CAAC;IAEM,eAAe,CAAC,KAAmB;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE/B,yBAAyB;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,uDAAuD;QACvD,MAAM,WAAW,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAEzH,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBACxD,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;aACjC,CAAC,CAAC,CAAC;QACR,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAgCN,CAAC;IACN,CAAC;IAEO,eAAe;QACnB,OAAO;;;;;;;;;;;;;;SAcN,CAAC;IACN,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;SAKN,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,aAAa;gBACd,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;YACjC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAChC,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpC,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACrC,KAAK,aAAa;gBACd,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACtC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAChC,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;YACjC;gBACI,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAEO,oBAAoB;QACxB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAqCN,CAAC;IACN,CAAC;IAEO,cAAc;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAmCN,CAAC;IACN,CAAC;IAEO,aAAa;QACjB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAiCN,CAAC;IACN,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAoCN,CAAC;IACN,CAAC;IAEO,kBAAkB;QACtB,OAAO,0CAA0C,CAAC;IACtD,CAAC;IAEO,mBAAmB;QACvB,OAAO,iDAAiD,CAAC;IAC7D,CAAC;IAEO,aAAa;QACjB,OAAO,yDAAyD,CAAC;IACrE,CAAC;IAEO,gBAAgB;QACpB,OAAO,4CAA4C,CAAC;IACxD,CAAC;IAEO,cAAc;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8DN,CAAC;IACN,CAAC;;AAhjBL,8CAijBC;AAhjB2B,6BAAW,GAAG,EAAE,AAAL,CAAM,CAAS,iCAAiC;AAC3D,sCAAoB,GAAG,IAAI,AAAP,CAAQ,CAAC,qBAAqB;AAClD,qCAAmB,GAAG,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,oBAAoB,AAAzE,CAA0E"}