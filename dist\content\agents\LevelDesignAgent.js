"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelDesignAgent = void 0;
const POIGenerator_1 = require("../../gameplay/POIGenerator");
const TerrainGenerator_1 = require("../../gameplay/TerrainGenerator");
const ContentProductionSystem_1 = require("../ContentProductionSystem");
const PerformanceMetrics_1 = require("../../rendering/PerformanceMetrics");
class LevelDesignAgent {
    constructor() {
        this.designedPOIs = new Map();
        this.tacticalAnalysis = new Map();
        this.poiGenerator = new POIGenerator_1.POIGenerator();
        this.terrainGenerator = new TerrainGenerator_1.TerrainGenerator({
            size: 2048,
            resolution: 2,
            maxHeight: 200,
            minHeight: 0,
            smoothingPasses: 3
        });
        this.contentSystem = new ContentProductionSystem_1.ContentProductionSystem();
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
    }
    async designCompletePOILayout(type, position, size) {
        console.log(`🎯 Level Design Agent: Iniciando design de POI ${type} em`, position);
        const poiId = `poi_${type}_${Date.now()}`;
        // Analisa o terreno ao redor
        const terrainAnalysis = await this.analyzeTerrainContext(position, size);
        // Cria layout tático baseado no tipo de POI
        const tacticalLayout = await this.createTacticalLayout(type, position, size, terrainAnalysis);
        // Define especificações do POI
        const poiSpec = {
            id: poiId,
            type,
            position,
            size,
            tacticalComplexity: this.determineTacticalComplexity(type),
            playerCapacity: this.calculatePlayerCapacity(type, size),
            uniqueFeatures: this.generateUniqueFeatures(type),
            layout: tacticalLayout
        };
        // Valida o design tático
        await this.validateTacticalDesign(poiSpec);
        // Armazena o design
        this.designedPOIs.set(poiId, poiSpec);
        console.log(`✅ POI ${type} projetado com ${tacticalLayout.coverPoints.length} pontos de cobertura`);
        return poiSpec;
    }
    async analyzeTerrainContext(position, size) {
        // Analisa elevações, declives e características do terreno
        const terrainData = {
            averageElevation: position.y,
            maxSlope: 0,
            waterBodies: [],
            naturalCover: [],
            accessRoutes: []
        };
        // Simula análise de terreno (em implementação real, usaria dados do TerrainGenerator)
        for (let x = -size.width / 2; x <= size.width / 2; x += 10) {
            for (let z = -size.length / 2; z <= size.length / 2; z += 10) {
                const samplePoint = {
                    x: position.x + x,
                    y: position.y,
                    z: position.z + z
                };
                // Análise de elevação e declive
                const elevation = this.sampleTerrainHeight(samplePoint);
                const slope = this.calculateSlope(samplePoint);
                terrainData.maxSlope = Math.max(terrainData.maxSlope, slope);
                // Identifica cobertura natural
                if (slope > 15 && slope < 45) {
                    terrainData.naturalCover.push(samplePoint);
                }
            }
        }
        return terrainData;
    }
    async createTacticalLayout(type, position, size, terrainAnalysis) {
        const layout = {
            coverPoints: [],
            sightLines: [],
            chokePoints: [],
            flankingRoutes: [],
            buyZones: [],
            contractZones: [],
            verticalElements: []
        };
        // Gera pontos de cobertura baseados no tipo de POI
        layout.coverPoints = await this.generateCoverPoints(type, position, size);
        // Calcula linhas de visão entre pontos importantes
        layout.sightLines = await this.calculateSightLines(layout.coverPoints);
        // Identifica pontos de estrangulamento (chokepoints)
        layout.chokePoints = await this.identifyChokePoints(type, position, size);
        // Cria rotas de flanqueamento
        layout.flankingRoutes = await this.designFlankingRoutes(layout.coverPoints, layout.chokePoints);
        // Posiciona zonas de compra e contratos
        layout.buyZones = await this.placeBuyZones(type, position, size);
        layout.contractZones = await this.placeContractZones(type, position, size);
        // Adiciona elementos verticais
        layout.verticalElements = await this.designVerticalElements(type, position, size);
        return layout;
    }
    async generateCoverPoints(type, position, size) {
        const coverPoints = [];
        const density = this.getCoverDensityForType(type);
        // Distribui pontos de cobertura de forma tática
        const gridSize = Math.sqrt(size.width * size.length / density);
        for (let x = -size.width / 2; x < size.width / 2; x += gridSize) {
            for (let z = -size.length / 2; z < size.length / 2; z += gridSize) {
                // Adiciona variação aleatória para naturalidade
                const variation = gridSize * 0.3;
                const coverPoint = {
                    x: position.x + x + (Math.random() - 0.5) * variation,
                    y: position.y + this.calculateCoverHeight(type),
                    z: position.z + z + (Math.random() - 0.5) * variation
                };
                // Valida se o ponto é táticamente viável
                if (await this.isValidCoverPoint(coverPoint, type)) {
                    coverPoints.push(coverPoint);
                }
            }
        }
        return coverPoints;
    }
    async calculateSightLines(coverPoints) {
        const sightLines = [];
        // Calcula linhas de visão entre todos os pontos de cobertura
        for (let i = 0; i < coverPoints.length; i++) {
            for (let j = i + 1; j < coverPoints.length; j++) {
                const from = coverPoints[i];
                const to = coverPoints[j];
                const distance = this.calculateDistance(from, to);
                // Só considera linhas de visão relevantes (até 100m)
                if (distance <= 100) {
                    const clear = await this.checkLineOfSight(from, to);
                    sightLines.push({ from, to, clear });
                }
            }
        }
        return sightLines;
    }
    getCoverDensityForType(type) {
        const densities = {
            urban: 25, // Alta densidade de cobertura
            military: 15, // Densidade média com estruturas fortificadas
            industrial: 20, // Densidade média-alta com maquinário
            forest: 30 // Alta densidade natural
        };
        return densities[type] || 20;
    }
    calculateCoverHeight(type) {
        const heights = {
            urban: 1.8, // Altura de paredes/carros
            military: 2.5, // Altura de bunkers/muros
            industrial: 3.0, // Altura de maquinário
            forest: 1.5 // Altura de rochas/troncos
        };
        return heights[type] || 2.0;
    }
    async isValidCoverPoint(point, type) {
        // Valida se o ponto oferece cobertura adequada
        // Verifica acessibilidade, ângulos de proteção, etc.
        return Math.random() > 0.3; // Simulação - 70% dos pontos são válidos
    }
    async checkLineOfSight(from, to) {
        // Implementa raycast para verificar obstruções
        // Em implementação real, usaria dados de geometria do mapa
        const distance = this.calculateDistance(from, to);
        return distance < 50 ? Math.random() > 0.4 : Math.random() > 0.7;
    }
    calculateDistance(a, b) {
        return Math.sqrt(Math.pow(a.x - b.x, 2) +
            Math.pow(a.y - b.y, 2) +
            Math.pow(a.z - b.z, 2));
    }
    sampleTerrainHeight(point) {
        // Simula amostragem de altura do terreno
        return point.y + (Math.random() - 0.5) * 10;
    }
    calculateSlope(point) {
        // Simula cálculo de declive
        return Math.random() * 30;
    }
    async getDesignedPOIs() {
        return Array.from(this.designedPOIs.values());
    }
    async getTacticalAnalysis(poiId) {
        return this.tacticalAnalysis.get(poiId);
    }
    async identifyChokePoints(type, position, size) {
        const chokePoints = [];
        // Identifica pontos naturais de estrangulamento baseados no tipo
        switch (type) {
            case 'urban':
                // Intersecções de ruas, entradas de edifícios
                chokePoints.push({ x: position.x - size.width * 0.3, y: position.y, z: position.z }, { x: position.x + size.width * 0.3, y: position.y, z: position.z }, { x: position.x, y: position.y, z: position.z - size.length * 0.3 }, { x: position.x, y: position.y, z: position.z + size.length * 0.3 });
                break;
            case 'military':
                // Portões, pontes, corredores
                chokePoints.push({ x: position.x, y: position.y, z: position.z - size.length * 0.4 }, { x: position.x, y: position.y, z: position.z + size.length * 0.4 });
                break;
            case 'industrial':
                // Entre maquinário, passarelas
                chokePoints.push({ x: position.x - size.width * 0.2, y: position.y, z: position.z }, { x: position.x + size.width * 0.2, y: position.y, z: position.z });
                break;
            case 'forest':
                // Clareiras, trilhas
                chokePoints.push({ x: position.x, y: position.y, z: position.z });
                break;
        }
        return chokePoints;
    }
    async designFlankingRoutes(coverPoints, chokePoints) {
        const routes = [];
        // Para cada chokepoint, cria rotas de flanqueamento
        for (const chokePoint of chokePoints) {
            const nearbyCovers = coverPoints
                .filter(cover => this.calculateDistance(cover, chokePoint) < 50)
                .sort((a, b) => this.calculateDistance(a, chokePoint) - this.calculateDistance(b, chokePoint));
            if (nearbyCovers.length >= 3) {
                // Cria rota de flanqueamento pela esquerda
                const leftRoute = [nearbyCovers[0], nearbyCovers[1], chokePoint];
                routes.push(leftRoute);
                // Cria rota de flanqueamento pela direita
                const rightRoute = [nearbyCovers[2], nearbyCovers[1], chokePoint];
                routes.push(rightRoute);
            }
        }
        return routes;
    }
    async placeBuyZones(type, position, size) {
        const buyZones = [];
        // Posiciona zonas de compra em locais seguros e acessíveis
        switch (type) {
            case 'urban':
                buyZones.push({ x: position.x - size.width * 0.4, y: position.y, z: position.z - size.length * 0.4 }, { x: position.x + size.width * 0.4, y: position.y, z: position.z + size.length * 0.4 });
                break;
            case 'military':
                buyZones.push({ x: position.x, y: position.y, z: position.z - size.length * 0.45 });
                break;
            case 'industrial':
                buyZones.push({ x: position.x - size.width * 0.35, y: position.y, z: position.z });
                break;
            case 'forest':
                buyZones.push({ x: position.x, y: position.y, z: position.z - size.length * 0.3 });
                break;
        }
        return buyZones;
    }
    async placeContractZones(type, position, size) {
        const contractZones = [];
        // Posiciona zonas de contrato em locais de alto risco/recompensa
        switch (type) {
            case 'urban':
                contractZones.push({ x: position.x, y: position.y + 10, z: position.z } // Topo de edifício
                );
                break;
            case 'military':
                contractZones.push({ x: position.x, y: position.y, z: position.z } // Centro da base
                );
                break;
            case 'industrial':
                contractZones.push({ x: position.x, y: position.y + 5, z: position.z } // Plataforma elevada
                );
                break;
            case 'forest':
                contractZones.push({ x: position.x, y: position.y + 2, z: position.z } // Clareira central
                );
                break;
        }
        return contractZones;
    }
    async designVerticalElements(type, position, size) {
        const verticalElements = [];
        switch (type) {
            case 'urban':
                // Edifícios de diferentes alturas
                for (let i = 0; i < 5; i++) {
                    verticalElements.push({
                        position: {
                            x: position.x + (Math.random() - 0.5) * size.width * 0.8,
                            y: position.y,
                            z: position.z + (Math.random() - 0.5) * size.length * 0.8
                        },
                        height: 15 + Math.random() * 25,
                        type: 'building'
                    });
                }
                break;
            case 'military':
                // Torres de observação, bunkers
                verticalElements.push({
                    position: { x: position.x, y: position.y, z: position.z },
                    height: 20,
                    type: 'watchtower'
                }, {
                    position: { x: position.x - 30, y: position.y - 3, z: position.z },
                    height: 5,
                    type: 'bunker'
                });
                break;
            case 'industrial':
                // Chaminés, silos, guindastes
                verticalElements.push({
                    position: { x: position.x - 20, y: position.y, z: position.z },
                    height: 35,
                    type: 'chimney'
                }, {
                    position: { x: position.x + 20, y: position.y, z: position.z },
                    height: 25,
                    type: 'crane'
                });
                break;
            case 'forest':
                // Árvores altas, rochas
                for (let i = 0; i < 8; i++) {
                    verticalElements.push({
                        position: {
                            x: position.x + (Math.random() - 0.5) * size.width * 0.9,
                            y: position.y,
                            z: position.z + (Math.random() - 0.5) * size.length * 0.9
                        },
                        height: 8 + Math.random() * 12,
                        type: 'tree'
                    });
                }
                break;
        }
        return verticalElements;
    }
    determineTacticalComplexity(type) {
        const complexities = {
            urban: 'high',
            military: 'high',
            industrial: 'medium',
            forest: 'medium'
        };
        return complexities[type] || 'medium';
    }
    calculatePlayerCapacity(type, size) {
        const baseCapacity = (size.width * size.length) / 1000; // 1 jogador por 1000m²
        const multipliers = {
            urban: 1.5, // Mais denso
            military: 1.2, // Estruturado
            industrial: 1.0, // Padrão
            forest: 0.8 // Mais espaçado
        };
        return Math.ceil(baseCapacity * (multipliers[type] ?? 1.0));
    }
    generateUniqueFeatures(type) {
        const features = {
            urban: ['rooftop_access', 'underground_passages', 'destructible_walls', 'vehicle_spawns'],
            military: ['armored_positions', 'weapon_caches', 'radar_station', 'helicopter_pad'],
            industrial: ['conveyor_belts', 'toxic_areas', 'heavy_machinery', 'steam_vents'],
            forest: ['hidden_caves', 'river_crossing', 'hunting_blinds', 'natural_bridges']
        };
        const typeFeatures = features[type] ?? [];
        const selectedFeatures = [];
        // Seleciona 2-3 características únicas aleatoriamente
        const count = 2 + Math.floor(Math.random() * 2);
        for (let i = 0; i < count && i < typeFeatures.length; i++) {
            const randomIndex = Math.floor(Math.random() * typeFeatures.length);
            if (!selectedFeatures.includes(typeFeatures[randomIndex])) {
                selectedFeatures.push(typeFeatures[randomIndex]);
            }
        }
        return selectedFeatures;
    }
    async validateTacticalDesign(poiSpec) {
        // Valida se o design atende aos critérios táticos
        const validation = {
            coverPointsAdequate: poiSpec.layout.coverPoints.length >= 10,
            chokePointsPresent: poiSpec.layout.chokePoints.length >= 1,
            flankingRoutesAvailable: poiSpec.layout.flankingRoutes.length >= 2,
            buyZonesAccessible: poiSpec.layout.buyZones.length >= 1,
            verticalityPresent: poiSpec.layout.verticalElements.length >= 1
        };
        const issues = Object.entries(validation)
            .filter(([key, valid]) => !valid)
            .map(([key]) => key);
        if (issues.length > 0) {
            console.warn(`⚠️ Problemas táticos detectados no POI ${poiSpec.id}:`, issues);
        }
        // Armazena análise tática
        this.tacticalAnalysis.set(poiSpec.id, {
            validation,
            issues,
            complexity: poiSpec.tacticalComplexity,
            playerFlow: this.analyzePotentialPlayerFlow(poiSpec),
            balanceScore: this.calculateBalanceScore(poiSpec)
        });
    }
    analyzePotentialPlayerFlow(poiSpec) {
        // Analisa como os jogadores provavelmente se moverão pelo POI
        return {
            entryPoints: poiSpec.layout.chokePoints.length,
            coverToMoveRatio: poiSpec.layout.coverPoints.length / (poiSpec.size.width * poiSpec.size.length / 100),
            verticalOptions: poiSpec.layout.verticalElements.filter(el => el.height > 5).length,
            flankingPotential: poiSpec.layout.flankingRoutes.length
        };
    }
    calculateBalanceScore(poiSpec) {
        // Calcula um score de balanceamento tático (0-100)
        let score = 50; // Base
        // Pontos de cobertura adequados
        if (poiSpec.layout.coverPoints.length >= 15)
            score += 10;
        if (poiSpec.layout.coverPoints.length >= 25)
            score += 10;
        // Variedade tática
        if (poiSpec.layout.flankingRoutes.length >= 3)
            score += 15;
        if (poiSpec.layout.verticalElements.length >= 3)
            score += 10;
        // Características únicas
        score += poiSpec.uniqueFeatures.length * 5;
        return Math.min(100, Math.max(0, score));
    }
}
exports.LevelDesignAgent = LevelDesignAgent;
//# sourceMappingURL=LevelDesignAgent.js.map