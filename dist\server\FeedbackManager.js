"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackManager = void 0;
class FeedbackManager {
    constructor(antiCheat) {
        this.feedbacks = new Map();
        this.reports = new Map();
        this.antiCheat = antiCheat;
        this.uploadQueue = new Map();
    }
    async submitFeedback(feedback) {
        const feedbackId = this.generateId();
        const timestamp = Date.now();
        const newFeedback = {
            ...feedback,
            id: feedbackId,
            createdAt: timestamp,
            updatedAt: timestamp,
            status: 'new'
        };
        // Processa anexos
        if (feedback.attachments) {
            newFeedback.attachments = await this.processAttachments(feedback.attachments);
        }
        // Adiciona informações do cliente se não fornecidas
        if (!feedback.clientInfo) {
            newFeedback.clientInfo = await this.collectClientInfo();
        }
        this.feedbacks.set(feedbackId, newFeedback);
        // Se for um feedback de performance, prioriza análise
        if (feedback.type === 'performance' && feedback.clientInfo?.fps < 60) {
            await this.handlePerformanceFeedback(newFeedback);
        }
        return feedbackId;
    }
    async submitReport(report) {
        const reportId = this.generateId();
        const timestamp = Date.now();
        const newReport = {
            ...report,
            id: reportId,
            timestamp,
            status: 'pending'
        };
        // Processa anexos
        if (report.attachments) {
            newReport.attachments = await this.processAttachments(report.attachments);
        }
        // Se for denúncia de trapaça, notifica o sistema anti-cheat
        if (report.type === 'cheating') {
            await this.antiCheat.handleReportedPlayer(report.reportedPlayerId, 'player_report');
        }
        this.reports.set(reportId, newReport);
        return reportId;
    }
    async processAttachments(attachments) {
        const processedAttachments = [];
        for (const attachment of attachments) {
            // Valida tamanho e tipo do arquivo
            if (attachment.size > FeedbackManager.MAX_ATTACHMENT_SIZE) {
                throw new Error(`Attachment size exceeds maximum allowed (50MB)`);
            }
            if (!FeedbackManager.ALLOWED_MIME_TYPES.includes(attachment.mimeType)) {
                throw new Error(`Invalid file type: ${attachment.mimeType}`);
            }
            // Se for screenshot, gera thumbnail
            if (attachment.type === 'screenshot') {
                attachment.thumbnail = await this.generateThumbnail(attachment.url);
            }
            // Se for replay, valida e extrai informações relevantes
            if (attachment.type === 'replay') {
                await this.validateReplay(attachment.url);
            }
            processedAttachments.push(attachment);
        }
        return processedAttachments;
    }
    async collectClientInfo() {
        // Coleta informações do cliente em tempo real
        return {
            fps: await this.getCurrentFPS(),
            ping: await this.getCurrentPing(),
            gpuInfo: await this.getGPUInfo(),
            cpuInfo: await this.getCPUInfo(),
            ramUsage: await this.getRAMUsage(),
            osInfo: await this.getOSInfo()
        };
    }
    async getAnalytics() {
        const feedbackAnalytics = this.generateFeedbackAnalytics();
        const reportAnalytics = this.generateReportAnalytics();
        return {
            feedback: feedbackAnalytics,
            reports: reportAnalytics
        };
    }
    generateFeedbackAnalytics() {
        const analytics = {
            totalFeedbacks: this.feedbacks.size,
            openFeedbacks: 0,
            resolvedFeedbacks: 0,
            averageResolutionTime: 0,
            feedbacksByType: new Map(),
            topIssues: []
        };
        // Calcula métricas
        for (const feedback of this.feedbacks.values()) {
            if (feedback.status === 'resolved' || feedback.status === 'closed') {
                analytics.resolvedFeedbacks++;
            }
            else {
                analytics.openFeedbacks++;
            }
            // Atualiza contagem por tipo
            const typeCount = analytics.feedbacksByType.get(feedback.type) || 0;
            analytics.feedbacksByType.set(feedback.type, typeCount + 1);
        }
        // Calcula top issues
        const issueMap = new Map();
        for (const feedback of this.feedbacks.values()) {
            const key = `${feedback.type}:${feedback.category || 'none'}`;
            const issue = issueMap.get(key) || { count: 0, totalPriority: 0 };
            issue.count++;
            issue.totalPriority += this.getPriorityValue(feedback.priority);
            issueMap.set(key, issue);
        }
        analytics.topIssues = Array.from(issueMap.entries())
            .map(([category, data]) => ({
            category,
            count: data.count,
            avgPriority: data.totalPriority / data.count
        }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        return analytics;
    }
    generateReportAnalytics() {
        const analytics = {
            totalReports: this.reports.size,
            activeReports: 0,
            resolvedReports: 0,
            reportsByType: new Map(),
            confirmedReports: 0,
            falsePositives: 0,
            averageReviewTime: 0
        };
        let totalReviewTime = 0;
        let reviewedCount = 0;
        for (const report of this.reports.values()) {
            if (report.status === 'resolved') {
                analytics.resolvedReports++;
                if (report.verdict === 'confirmed') {
                    analytics.confirmedReports++;
                }
                else if (report.verdict === 'false_positive') {
                    analytics.falsePositives++;
                }
                if (report.reviewedBy) {
                    const reviewTime = report.timestamp - Date.now();
                    totalReviewTime += reviewTime;
                    reviewedCount++;
                }
            }
            else {
                analytics.activeReports++;
            }
            // Atualiza contagem por tipo
            const typeCount = analytics.reportsByType.get(report.type) || 0;
            analytics.reportsByType.set(report.type, typeCount + 1);
        }
        analytics.averageReviewTime = reviewedCount > 0 ? totalReviewTime / reviewedCount : 0;
        return analytics;
    }
    getPriorityValue(priority) {
        switch (priority) {
            case 'critical': return 4;
            case 'high': return 3;
            case 'medium': return 2;
            case 'low': return 1;
            default: return 0;
        }
    }
    async handlePerformanceFeedback(feedback) {
        // Adiciona à fila de análise de performance
        // Implementação específica dependerá do sistema de telemetria
    }
    generateId() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    // Métodos auxiliares de coleta de informações do cliente
    async getCurrentFPS() {
        // Implementação dependerá do sistema de renderização
        return 0;
    }
    async getCurrentPing() {
        // Implementação dependerá do sistema de rede
        return 0;
    }
    async getGPUInfo() {
        // Implementação dependerá do acesso ao hardware
        return '';
    }
    async getCPUInfo() {
        // Implementação dependerá do acesso ao hardware
        return '';
    }
    async getRAMUsage() {
        // Implementação dependerá do acesso ao hardware
        return 0;
    }
    async getOSInfo() {
        // Implementação dependerá do acesso ao sistema
        return '';
    }
    async generateThumbnail(url) {
        // Implementação dependerá do sistema de processamento de imagem
        return '';
    }
    async validateReplay(url) {
        // Implementação dependerá do ReplayManager
    }
}
exports.FeedbackManager = FeedbackManager;
FeedbackManager.MAX_ATTACHMENT_SIZE = 50 * 1024 * 1024; // 50MB
FeedbackManager.ALLOWED_MIME_TYPES = [
    'image/png',
    'image/jpeg',
    'text/plain',
    'application/json',
    'application/tactical-nexus-replay'
];
//# sourceMappingURL=FeedbackManager.js.map