{"version": 3, "file": "AssetOptimizer.js", "sourceRoot": "", "sources": ["../../src/rendering/AssetOptimizer.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AACxD,2DAAwD;AA8CxD,MAAa,cAAc;IAMvB,YAAY,kBAAsC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,kBAAkB,CAAC,CAAC;QACnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,kBAAkB,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IACjD,CAAC;IAEM,KAAK,CAAC,UAAU;QACnB,MAAM,OAAO,CAAC,GAAG,CAAC;YACd,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;SACtC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,eAAe,CACxB,KAAuB,EACvB,OAA6B;QAE7B,iCAAiC;QACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC3B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAEtE,8BAA8B;QAC9B,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CACrF,SAAS,EACT;YACI,MAAM,EAAE,OAAO,EAAE,yCAAyC;YAC1D,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,gBAAgB,EAAE,CAAC,EAAE,2BAA2B;YAChD,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,OAAO,CAAC,YAAY;YAC5B,SAAS,EAAE,KAAK,EAAE,iCAAiC;YACnD,oBAAoB,EAAE,IAAI;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;SACjC,CACJ,CAAC;QAEF,6CAA6C;QAC7C,MAAM,CAAC,cAAc,EAAE,eAAe,CAAC,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CACnF,cAAc,EACd,OAAO,CAAC,YAAY,CACvB,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,4BAA4B,EAAE,eAAe,CAAC,CAAC;QAEpF,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC5B,KAAU,EACV,MAA+B;QAE/B,MAAM,OAAO,GAAiB;YAC1B,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC5C,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;SAC/B,CAAC;QAEF,6BAA6B;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAErD,yCAAyC;QACzC,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAEzG,gDAAgD;QAChD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;QAEtD,0BAA0B;QAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC;QAED,2BAA2B;QAC3B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;QAC5D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC/C,OAAO,CAAC,WAAW,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5D,OAAO,CAAC,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC;QACtD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,mBAAmB,CAAC,KAAU;QAClC,8DAA8D;QAC9D,OAAO;YACH,QAAQ,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,OAAO,EAAE,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;YAC7C,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;YAC9C,GAAG,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;YACtC,QAAQ,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;SACnD,CAAC;IACN,CAAC;IAEO,sBAAsB,CAAC,KAAU,EAAE,QAAsB;QAC7D,8DAA8D;QAC9D,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACnC,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACjC,IAAI,QAAQ,CAAC,OAAO;YAAE,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACvD,IAAI,QAAQ,CAAC,GAAG;YAAE,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QAC3C,IAAI,QAAQ,CAAC,QAAQ;YAAE,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAC9D,CAAC;IAEO,kBAAkB,CAAC,KAAU;QACjC,sDAAsD;QACtD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,eAAe,CAAC,KAAU;QAC9B,+CAA+C;QAC/C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAU,EAAE,WAAmB;QACvD,uCAAuC;IAC3C,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAU,EAAE,SAAyC;QAC5E,oCAAoC;IACxC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAU,EAAE,QAAgB;QACvD,4CAA4C;IAChD,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAU,EAAE,cAAoD;QACzF,2CAA2C;IAC/C,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAU,EAAE,iBAAyD;QAC7F,yCAAyC;IAC7C,CAAC;IAEO,cAAc,CAAC,KAAU;QAC7B,gDAAgD;QAChD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,aAAa,CAAC,KAAU;QAC5B,8CAA8C;QAC9C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,YAAY,CAAC,KAAU;QAC3B,6CAA6C;QAC7C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,aAAa,CAAC,KAAU;QAC5B,8CAA8C;QAC9C,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,oBAAoB,CAAC,KAAU;QACnC,mDAAmD;QACnD,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,eAAe,CAAC,OAAe;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;CACJ;AA1LD,wCA0LC"}