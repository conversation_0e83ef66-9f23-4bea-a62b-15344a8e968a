"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioAgent = void 0;
const GameAudioSystem_1 = require("../../audio/GameAudioSystem");
const AudioManager_1 = require("../../audio/AudioManager");
const PerformanceMetrics_1 = require("../../rendering/PerformanceMetrics");
class AudioAgent {
    constructor() {
        this.createdZones = new Map();
        this.audioAssets = new Map();
        this.soundscapeProfiles = new Map();
        this.activeChannels = new Set();
        this.metrics = PerformanceMetrics_1.PerformanceMetrics.getInstance();
        this.audioManager = new AudioManager_1.AudioManager();
        this.audioSystem = new GameAudioSystem_1.GameAudioSystem({
            maxChannels: 64,
            spatialAudio: true,
            reverbEnabled: true,
            compressionEnabled: true
        });
        this.initializeSoundscapeProfiles();
    }
    async createPOIAmbientSoundscape(poiType, poiId, position, size) {
        console.log(`🔊 Audio Agent: Criando soundscape para POI ${poiType} (${poiId})`);
        const soundscapeProfile = this.getSoundscapeProfile(poiType);
        // Cria zonas ambientais
        const ambientZones = await this.createAmbientZones(poiType, position, size, soundscapeProfile);
        // Cria áudio interativo
        const interactiveAudio = await this.createInteractiveAudio(poiType, soundscapeProfile);
        // Configura zonas de reverberação
        const reverbZones = await this.createReverbZones(poiType, position, size);
        // Otimiza áudio para performance
        await this.optimizeAudioPerformance(ambientZones, interactiveAudio);
        console.log(`✅ Soundscape criado: ${ambientZones.length} zonas ambientais, ${interactiveAudio.length} sons interativos`);
        return { ambientZones, interactiveAudio, reverbZones };
    }
    async createAmbientZones(poiType, position, size, profile) {
        const zones = [];
        // Zona ambiental principal (cobrindo todo o POI)
        const mainZone = await this.createMainAmbientZone(poiType, position, size, profile);
        zones.push(mainZone);
        // Zonas ambientais específicas baseadas no tipo de POI
        const specificZones = await this.createSpecificAmbientZones(poiType, position, size, profile);
        zones.push(...specificZones);
        // Zonas de transição nas bordas
        const transitionZones = await this.createTransitionZones(poiType, position, size);
        zones.push(...transitionZones);
        return zones;
    }
    async createMainAmbientZone(poiType, position, size, profile) {
        const radius = Math.max(size.width, size.length) * 0.6;
        const ambientSounds = await this.generateAmbientSounds(poiType, profile.ambientLayers);
        const zone = {
            id: `ambient_main_${poiType}_${Date.now()}`,
            type: 'ambient',
            position,
            radius,
            volume: 0.7,
            priority: 1,
            sounds: ambientSounds,
            reverb: profile.reverbProfile,
            occlusion: false
        };
        this.createdZones.set(zone.id, zone);
        return zone;
    }
    async createSpecificAmbientZones(poiType, position, size, profile) {
        const zones = [];
        switch (poiType) {
            case 'urban':
                // Zona de tráfego
                zones.push(await this.createTrafficZone(position, size));
                // Zona de atividade humana
                zones.push(await this.createHumanActivityZone(position, size));
                break;
            case 'military':
                // Zona de equipamentos
                zones.push(await this.createMilitaryEquipmentZone(position, size));
                // Zona de comunicações
                zones.push(await this.createCommunicationZone(position, size));
                break;
            case 'industrial':
                // Zona de maquinário
                zones.push(await this.createMachineryZone(position, size));
                // Zona de vapor/gases
                zones.push(await this.createSteamZone(position, size));
                break;
            case 'forest':
                // Zona de vida selvagem
                zones.push(await this.createWildlifeZone(position, size));
                // Zona de vento nas árvores
                zones.push(await this.createWindZone(position, size));
                break;
        }
        return zones;
    }
    async generateAmbientSounds(poiType, ambientLayers) {
        const sounds = [];
        for (const layer of ambientLayers) {
            const sound = await this.createAmbientSound(layer, poiType);
            sounds.push(sound);
            this.audioAssets.set(sound.id, sound);
        }
        return sounds;
    }
    async createAmbientSound(soundType, poiType) {
        const soundSpec = {
            id: `ambient_${soundType}_${poiType}_${Date.now()}`,
            type: 'ambient',
            category: soundType,
            duration: this.getAmbientSoundDuration(soundType),
            loop: true,
            volume: this.getAmbientSoundVolume(soundType),
            pitch: 1.0,
            spatial: this.isAmbientSoundSpatial(soundType),
            falloffDistance: this.getAmbientFalloffDistance(soundType),
            compressionFormat: 'ogg',
            quality: 'medium'
        };
        return soundSpec;
    }
    async createInteractiveAudio(poiType, profile) {
        const interactiveAudio = [];
        for (const soundType of profile.interactiveSounds) {
            const sound = await this.createInteractiveSound(soundType, poiType);
            interactiveAudio.push(sound);
            this.audioAssets.set(sound.id, sound);
        }
        return interactiveAudio;
    }
    async createInteractiveSound(soundType, poiType) {
        const soundSpec = {
            id: `interactive_${soundType}_${poiType}_${Date.now()}`,
            type: 'sfx',
            category: soundType,
            duration: this.getInteractiveSoundDuration(soundType),
            loop: false,
            volume: this.getInteractiveSoundVolume(soundType),
            pitch: 1.0 + (Math.random() - 0.5) * 0.2, // Variação de pitch
            spatial: true,
            falloffDistance: this.getInteractiveFalloffDistance(soundType),
            compressionFormat: 'ogg',
            quality: 'high'
        };
        return soundSpec;
    }
    async createReverbZones(poiType, position, size) {
        const reverbZones = [];
        const reverbSettings = this.getReverbSettingsForPOI(poiType);
        // Zona de reverberação principal
        reverbZones.push({
            id: `reverb_main_${poiType}`,
            position,
            size: { width: size.width * 1.2, length: size.length * 1.2, height: size.height },
            settings: reverbSettings.outdoor
        });
        // Zonas de reverberação para interiores (se aplicável)
        if (poiType === 'urban' || poiType === 'military' || poiType === 'industrial') {
            const interiorZones = this.createInteriorReverbZones(poiType, position, size);
            reverbZones.push(...interiorZones);
        }
        return reverbZones;
    }
    createInteriorReverbZones(poiType, position, size) {
        const zones = [];
        const reverbSettings = this.getReverbSettingsForPOI(poiType);
        // Simula 2-4 espaços interiores
        const interiorCount = 2 + Math.floor(Math.random() * 3);
        for (let i = 0; i < interiorCount; i++) {
            const interiorPosition = {
                x: position.x + (Math.random() - 0.5) * size.width * 0.8,
                y: position.y,
                z: position.z + (Math.random() - 0.5) * size.length * 0.8
            };
            zones.push({
                id: `reverb_interior_${poiType}_${i}`,
                position: interiorPosition,
                size: { width: 10, length: 10, height: 3 },
                settings: reverbSettings.interior
            });
        }
        return zones;
    }
    initializeSoundscapeProfiles() {
        this.soundscapeProfiles.set('urban', {
            name: 'Urban Environment',
            ambientLayers: ['city_traffic', 'distant_voices', 'urban_wind', 'electrical_hum'],
            interactiveSounds: ['footsteps_concrete', 'door_slam', 'car_horn', 'glass_break'],
            musicTracks: ['urban_tension', 'city_exploration'],
            reverbProfile: {
                roomSize: 0.8,
                decay: 1.2,
                preDelay: 0.02,
                damping: 0.6,
                earlyReflections: 0.7,
                lateReflections: 0.5
            },
            dynamicRange: { min: 0.3, max: 0.9 },
            spatialDensity: 0.7
        });
        this.soundscapeProfiles.set('military', {
            name: 'Military Base',
            ambientLayers: ['radio_chatter', 'generator_hum', 'metal_creaking', 'distant_engines'],
            interactiveSounds: ['metal_footsteps', 'weapon_handling', 'radio_static', 'equipment_rattle'],
            musicTracks: ['military_tension', 'tactical_ambient'],
            reverbProfile: {
                roomSize: 0.6,
                decay: 0.8,
                preDelay: 0.01,
                damping: 0.8,
                earlyReflections: 0.6,
                lateReflections: 0.4
            },
            dynamicRange: { min: 0.2, max: 0.8 },
            spatialDensity: 0.5
        });
        this.soundscapeProfiles.set('industrial', {
            name: 'Industrial Zone',
            ambientLayers: ['machinery_hum', 'steam_vents', 'metal_clanking', 'electrical_buzz'],
            interactiveSounds: ['metal_impact', 'steam_release', 'machinery_start', 'warning_beep'],
            musicTracks: ['industrial_ambient', 'mechanical_tension'],
            reverbProfile: {
                roomSize: 0.9,
                decay: 1.5,
                preDelay: 0.03,
                damping: 0.4,
                earlyReflections: 0.8,
                lateReflections: 0.7
            },
            dynamicRange: { min: 0.4, max: 1.0 },
            spatialDensity: 0.8
        });
        this.soundscapeProfiles.set('forest', {
            name: 'Forest Environment',
            ambientLayers: ['wind_through_trees', 'bird_songs', 'rustling_leaves', 'distant_water'],
            interactiveSounds: ['branch_snap', 'leaves_rustle', 'animal_movement', 'water_splash'],
            musicTracks: ['forest_calm', 'nature_exploration'],
            reverbProfile: {
                roomSize: 1.0,
                decay: 2.0,
                preDelay: 0.05,
                damping: 0.3,
                earlyReflections: 0.4,
                lateReflections: 0.8
            },
            dynamicRange: { min: 0.1, max: 0.7 },
            spatialDensity: 0.6
        });
    }
    getSoundscapeProfile(poiType) {
        return this.soundscapeProfiles.get(poiType) || this.soundscapeProfiles.get('urban');
    }
    async createTrafficZone(position, size) {
        return {
            id: `traffic_zone_${Date.now()}`,
            type: 'ambient',
            position: { x: position.x, y: position.y, z: position.z - size.length * 0.4 },
            radius: 30,
            volume: 0.5,
            priority: 2,
            sounds: [await this.createAmbientSound('traffic', 'urban')],
            reverb: this.getDefaultReverb(),
            occlusion: true
        };
    }
    async createHumanActivityZone(position, size) {
        return {
            id: `human_activity_zone_${Date.now()}`,
            type: 'dynamic',
            position: { x: position.x + size.width * 0.2, y: position.y, z: position.z },
            radius: 25,
            volume: 0.4,
            priority: 3,
            sounds: [
                await this.createAmbientSound('distant_voices', 'urban'),
                await this.createAmbientSound('footsteps', 'urban')
            ],
            reverb: this.getDefaultReverb(),
            occlusion: true
        };
    }
    async createMilitaryEquipmentZone(position, size) {
        return {
            id: `military_equipment_zone_${Date.now()}`,
            type: 'ambient',
            position,
            radius: 35,
            volume: 0.6,
            priority: 2,
            sounds: [
                await this.createAmbientSound('generator_hum', 'military'),
                await this.createAmbientSound('equipment_rattle', 'military')
            ],
            reverb: this.getMilitaryReverb(),
            occlusion: false
        };
    }
    async createCommunicationZone(position, size) {
        return {
            id: `communication_zone_${Date.now()}`,
            type: 'interactive',
            position: { x: position.x, y: position.y + 5, z: position.z },
            radius: 20,
            volume: 0.3,
            priority: 4,
            sounds: [await this.createAmbientSound('radio_chatter', 'military')],
            reverb: this.getMilitaryReverb(),
            occlusion: true
        };
    }
    async createMachineryZone(position, size) {
        return {
            id: `machinery_zone_${Date.now()}`,
            type: 'ambient',
            position,
            radius: 40,
            volume: 0.8,
            priority: 1,
            sounds: [
                await this.createAmbientSound('machinery_hum', 'industrial'),
                await this.createAmbientSound('metal_clanking', 'industrial')
            ],
            reverb: this.getIndustrialReverb(),
            occlusion: false
        };
    }
    async createSteamZone(position, size) {
        return {
            id: `steam_zone_${Date.now()}`,
            type: 'dynamic',
            position: { x: position.x - size.width * 0.3, y: position.y + 3, z: position.z },
            radius: 15,
            volume: 0.7,
            priority: 3,
            sounds: [await this.createAmbientSound('steam_vents', 'industrial')],
            reverb: this.getIndustrialReverb(),
            occlusion: true
        };
    }
    async createWildlifeZone(position, size) {
        return {
            id: `wildlife_zone_${Date.now()}`,
            type: 'dynamic',
            position,
            radius: 50,
            volume: 0.5,
            priority: 2,
            sounds: [
                await this.createAmbientSound('bird_songs', 'forest'),
                await this.createAmbientSound('animal_movement', 'forest')
            ],
            reverb: this.getForestReverb(),
            occlusion: false
        };
    }
    async createWindZone(position, size) {
        return {
            id: `wind_zone_${Date.now()}`,
            type: 'ambient',
            position: { x: position.x, y: position.y + size.height * 0.7, z: position.z },
            radius: 60,
            volume: 0.4,
            priority: 1,
            sounds: [await this.createAmbientSound('wind_through_trees', 'forest')],
            reverb: this.getForestReverb(),
            occlusion: false
        };
    }
    async createTransitionZones(poiType, position, size) {
        const zones = [];
        const transitionRadius = 20;
        // Cria zonas de transição nas 4 direções
        const directions = [
            { x: size.width * 0.5, z: 0 }, // Leste
            { x: -size.width * 0.5, z: 0 }, // Oeste
            { x: 0, z: size.length * 0.5 }, // Norte
            { x: 0, z: -size.length * 0.5 } // Sul
        ];
        for (let i = 0; i < directions.length; i++) {
            const dir = directions[i];
            const transitionPosition = {
                x: position.x + dir.x,
                y: position.y,
                z: position.z + dir.z
            };
            const zone = {
                id: `transition_${poiType}_${i}_${Date.now()}`,
                type: 'ambient',
                position: transitionPosition,
                radius: transitionRadius,
                volume: 0.3,
                priority: 5,
                sounds: [await this.createAmbientSound('ambient_transition', poiType)],
                reverb: this.getTransitionReverb(),
                occlusion: false
            };
            zones.push(zone);
            this.createdZones.set(zone.id, zone);
        }
        return zones;
    }
    getAmbientSoundDuration(soundType) {
        const durations = {
            city_traffic: 30,
            distant_voices: 25,
            urban_wind: 20,
            electrical_hum: 15,
            radio_chatter: 10,
            generator_hum: 45,
            metal_creaking: 8,
            machinery_hum: 60,
            steam_vents: 12,
            wind_through_trees: 35,
            bird_songs: 20,
            rustling_leaves: 15
        };
        return durations[soundType] || 20;
    }
    getAmbientSoundVolume(soundType) {
        const volumes = {
            city_traffic: 0.6,
            distant_voices: 0.3,
            urban_wind: 0.4,
            electrical_hum: 0.2,
            radio_chatter: 0.4,
            generator_hum: 0.7,
            machinery_hum: 0.8,
            steam_vents: 0.5,
            wind_through_trees: 0.5,
            bird_songs: 0.4
        };
        return volumes[soundType] || 0.5;
    }
    isAmbientSoundSpatial(soundType) {
        const spatialSounds = [
            'radio_chatter', 'steam_vents', 'bird_songs', 'animal_movement',
            'metal_creaking', 'equipment_rattle'
        ];
        return spatialSounds.includes(soundType);
    }
    getAmbientFalloffDistance(soundType) {
        const distances = {
            city_traffic: 100,
            machinery_hum: 80,
            generator_hum: 60,
            wind_through_trees: 120,
            radio_chatter: 30,
            steam_vents: 25,
            bird_songs: 70
        };
        return distances[soundType] || 50;
    }
    getInteractiveSoundDuration(soundType) {
        const durations = {
            footsteps_concrete: 0.5,
            door_slam: 1.2,
            car_horn: 2.0,
            glass_break: 0.8,
            metal_footsteps: 0.4,
            weapon_handling: 1.5,
            radio_static: 0.3,
            metal_impact: 0.6,
            steam_release: 2.5,
            branch_snap: 0.4,
            water_splash: 1.0
        };
        return durations[soundType] || 1.0;
    }
    getInteractiveSoundVolume(soundType) {
        const volumes = {
            footsteps_concrete: 0.4,
            door_slam: 0.8,
            car_horn: 0.9,
            glass_break: 0.7,
            weapon_handling: 0.6,
            metal_impact: 0.8,
            steam_release: 0.7,
            branch_snap: 0.5,
            water_splash: 0.6
        };
        return volumes[soundType] || 0.6;
    }
    getInteractiveFalloffDistance(soundType) {
        const distances = {
            footsteps_concrete: 15,
            door_slam: 40,
            car_horn: 150,
            glass_break: 50,
            weapon_handling: 25,
            metal_impact: 60,
            steam_release: 35,
            branch_snap: 20,
            water_splash: 30
        };
        return distances[soundType] || 30;
    }
    getDefaultReverb() {
        return {
            roomSize: 0.5,
            decay: 1.0,
            preDelay: 0.02,
            damping: 0.5,
            earlyReflections: 0.5,
            lateReflections: 0.5
        };
    }
    getMilitaryReverb() {
        return {
            roomSize: 0.6,
            decay: 0.8,
            preDelay: 0.01,
            damping: 0.8,
            earlyReflections: 0.6,
            lateReflections: 0.4
        };
    }
    getIndustrialReverb() {
        return {
            roomSize: 0.9,
            decay: 1.5,
            preDelay: 0.03,
            damping: 0.4,
            earlyReflections: 0.8,
            lateReflections: 0.7
        };
    }
    getForestReverb() {
        return {
            roomSize: 1.0,
            decay: 2.0,
            preDelay: 0.05,
            damping: 0.3,
            earlyReflections: 0.4,
            lateReflections: 0.8
        };
    }
    getTransitionReverb() {
        return {
            roomSize: 0.7,
            decay: 1.2,
            preDelay: 0.025,
            damping: 0.6,
            earlyReflections: 0.5,
            lateReflections: 0.6
        };
    }
    getReverbSettingsForPOI(poiType) {
        const settings = {
            urban: {
                outdoor: this.getDefaultReverb(),
                interior: {
                    roomSize: 0.3,
                    decay: 0.6,
                    preDelay: 0.01,
                    damping: 0.7,
                    earlyReflections: 0.8,
                    lateReflections: 0.3
                }
            },
            military: {
                outdoor: this.getMilitaryReverb(),
                interior: {
                    roomSize: 0.4,
                    decay: 0.5,
                    preDelay: 0.005,
                    damping: 0.9,
                    earlyReflections: 0.9,
                    lateReflections: 0.2
                }
            },
            industrial: {
                outdoor: this.getIndustrialReverb(),
                interior: {
                    roomSize: 0.8,
                    decay: 1.2,
                    preDelay: 0.02,
                    damping: 0.3,
                    earlyReflections: 0.9,
                    lateReflections: 0.6
                }
            },
            forest: {
                outdoor: this.getForestReverb(),
                interior: {
                    roomSize: 0.2,
                    decay: 0.4,
                    preDelay: 0.01,
                    damping: 0.8,
                    earlyReflections: 0.6,
                    lateReflections: 0.2
                }
            }
        };
        return settings[poiType] || settings.urban;
    }
    async optimizeAudioPerformance(ambientZones, interactiveAudio) {
        console.log('🔧 Otimizando performance de áudio...');
        // Monitora canais ativos
        const activeChannels = this.metrics.getActiveAudioChannels();
        const maxChannels = 64;
        if (activeChannels > maxChannels * 0.8) {
            // Reduz qualidade de áudio se muitos canais estão ativos
            for (const zone of ambientZones) {
                if (zone.priority > 3) {
                    zone.volume *= 0.8;
                }
            }
            for (const audio of interactiveAudio) {
                if (audio.quality === 'high') {
                    audio.quality = 'medium';
                }
            }
        }
        // Otimiza compressão baseado na performance
        const currentFPS = this.metrics.measureFPS();
        if (currentFPS < 500) {
            for (const audio of interactiveAudio) {
                if (audio.compressionFormat === 'wav') {
                    audio.compressionFormat = 'ogg';
                }
            }
        }
        console.log(`✅ Áudio otimizado: ${ambientZones.length} zonas, ${interactiveAudio.length} sons interativos`);
    }
    async getCreatedZones() {
        return Array.from(this.createdZones.values());
    }
    async getAudioAssets() {
        return Array.from(this.audioAssets.values());
    }
    async getSoundscapeProfiles() {
        return this.soundscapeProfiles;
    }
    async getActiveChannels() {
        return this.activeChannels;
    }
}
exports.AudioAgent = AudioAgent;
//# sourceMappingURL=AudioAgent.js.map