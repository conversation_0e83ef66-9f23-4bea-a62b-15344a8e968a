"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReplayManager = void 0;
class ReplayManager {
    constructor() {
        this.recording = false;
        this.buffer = [];
        this.events = [];
        this.metadata = null;
        this.currentTick = 0;
        this.worker = new Worker('./ReplayWorker.ts');
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
    }
    startRecording(initialMetadata) {
        this.recording = true;
        this.buffer = [];
        this.events = [];
        this.currentTick = 0;
        this.metadata = {
            version: ReplayManager.VERSION,
            timestamp: Date.now(),
            mapName: initialMetadata.mapName || '',
            duration: 0,
            players: initialMetadata.players || [],
            gameMode: initialMetadata.gameMode || 'standard',
            checksum: ''
        };
        // Inicia worker para compressão assíncrona
        this.worker.postMessage({
            type: 'start',
            metadata: this.metadata
        });
    }
    stopRecording() {
        this.recording = false;
        return new Promise((resolve) => {
            this.worker.onmessage = (e) => {
                if (e.data.type === 'complete') {
                    resolve(new Blob([e.data.replayData], { type: 'application/tactical-nexus-replay' }));
                }
            };
            this.worker.postMessage({
                type: 'finish',
                buffer: this.buffer,
                events: this.events,
                metadata: {
                    ...this.metadata,
                    duration: this.currentTick
                }
            });
        });
    }
    recordFrame(playerStates, playerInputs, safeZone) {
        if (!this.recording)
            return;
        const frame = {
            tick: this.currentTick++,
            playerStates: new Map(playerStates),
            playerInputs: new Map(playerInputs),
            events: [],
            safeZone
        };
        this.buffer.push(frame);
        // Se o buffer estiver muito grande, envia para o worker processar
        if (this.buffer.length * this.estimateFrameSize() > ReplayManager.BUFFER_SIZE) {
            this.flushBuffer();
        }
    }
    recordEvent(event) {
        if (!this.recording)
            return;
        this.events.push({
            ...event,
            tick: this.currentTick
        });
    }
    flushBuffer() {
        this.worker.postMessage({
            type: 'compress',
            frames: this.buffer.splice(0)
        });
    }
    estimateFrameSize() {
        // Estimativa aproximada do tamanho de um frame em bytes
        return 1000; // Valor exemplo, ajustar baseado em medições reais
    }
    async extractClip(replayId, startTime, endTime, focusPlayerId) {
        const clipId = `${replayId}_${startTime}_${endTime}`;
        return new Promise((resolve) => {
            this.worker.onmessage = (e) => {
                if (e.data.type === 'clipComplete' && e.data.clipId === clipId) {
                    resolve(e.data.clipData);
                }
            };
            this.worker.postMessage({
                type: 'extractClip',
                replayId,
                startTime,
                endTime,
                focusPlayerId,
                clipId
            });
        });
    }
    async getPlayerRecentReplays(playerId, limit = 10) {
        return new Promise((resolve) => {
            this.worker.onmessage = (e) => {
                if (e.data.type === 'recentReplays') {
                    resolve(e.data.replayIds);
                }
            };
            this.worker.postMessage({
                type: 'getRecentReplays',
                playerId,
                limit
            });
        });
    }
    async loadReplay(replayId) {
        return new Promise((resolve) => {
            this.worker.onmessage = (e) => {
                if (e.data.type === 'replayLoaded' && e.data.replayId === replayId) {
                    resolve(e.data.replayData);
                }
            };
            this.worker.postMessage({
                type: 'loadReplay',
                replayId
            });
        });
    }
    handleWorkerMessage(e) {
        switch (e.data.type) {
            case 'error':
                console.error('ReplayWorker error:', e.data.error);
                break;
            case 'bufferFull':
                this.handleBufferFull();
                break;
            case 'compressionProgress':
                this.handleCompressionProgress(e.data.progress);
                break;
        }
    }
    handleBufferFull() {
        // Comprime e salva buffer atual
        this.worker.postMessage({
            type: 'compressBuffer',
            buffer: this.buffer.slice(0, Math.floor(this.buffer.length / 2))
        });
        // Remove primeira metade do buffer
        this.buffer = this.buffer.slice(Math.floor(this.buffer.length / 2));
    }
    handleCompressionProgress(progress) {
        // Pode ser usado para atualizar UI com progresso da compressão
        if (this.onCompressionProgress) {
            this.onCompressionProgress(progress);
        }
    }
}
exports.ReplayManager = ReplayManager;
ReplayManager.BUFFER_SIZE = 1024 * 1024 * 10; // 10MB buffer
ReplayManager.COMPRESSION_LEVEL = 7;
ReplayManager.VERSION = '1.0.0';
//# sourceMappingURL=ReplayManager.js.map