"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeaponSFX = void 0;
const AudioManager_1 = require("./AudioManager");
class WeaponSFX {
    constructor(metrics) {
        this.audioManager = new AudioManager_1.AudioManager(metrics);
        this.soundProfiles = new Map();
        this.initializeWeaponProfiles();
    }
    initializeWeaponProfiles() {
        // Rifle Híbrido (AK-47/M4A1)
        this.soundProfiles.set('hybrid_rifle', {
            fire: {
                variations: 5,
                volume: 1.0,
                minPitch: 0.98,
                maxPitch: 1.02,
                distance: 1000,
                spread: 45
            },
            reload: {
                variations: 2,
                volume: 0.8,
                sequence: [
                    'mag_out',
                    'mag_fumble',
                    'mag_in',
                    'bolt_pull'
                ]
            },
            handling: {
                variations: 3,
                volume: 0.7
            }
        });
        // AWP Modernizada
        this.soundProfiles.set('modern_awp', {
            fire: {
                variations: 3,
                volume: 1.0,
                minPitch: 0.99,
                maxPitch: 1.01,
                distance: 1500,
                spread: 30
            },
            reload: {
                variations: 2,
                volume: 0.85,
                sequence: [
                    'bolt_back',
                    'mag_out',
                    'mag_in',
                    'bolt_forward'
                ]
            },
            handling: {
                variations: 2,
                volume: 0.75
            }
        });
        // Desert Eagle
        this.soundProfiles.set('desert_eagle', {
            fire: {
                variations: 4,
                volume: 0.95,
                minPitch: 0.97,
                maxPitch: 1.03,
                distance: 800,
                spread: 60
            },
            reload: {
                variations: 2,
                volume: 0.8,
                sequence: [
                    'mag_out',
                    'mag_in',
                    'slide_release'
                ]
            },
            handling: {
                variations: 2,
                volume: 0.7
            }
        });
    }
    async loadWeaponSounds(weaponId) {
        const profile = this.soundProfiles.get(weaponId);
        if (!profile)
            return;
        // Carrega sons de tiro
        for (let i = 1; i <= profile.fire.variations; i++) {
            await this.audioManager.loadWeaponSFX(weaponId);
        }
        // Carrega sons de reload
        for (const action of profile.reload.sequence) {
            for (let i = 1; i <= profile.reload.variations; i++) {
                await this.audioManager.loadWeaponSFX(weaponId);
            }
        }
        // Carrega sons de manuseio
        for (let i = 1; i <= profile.handling.variations; i++) {
            await this.audioManager.loadWeaponSFX(weaponId);
        }
    }
    playWeaponFire(weaponId, position) {
        const profile = this.soundProfiles.get(weaponId);
        if (!profile)
            return;
        this.audioManager.playWeaponSound(weaponId, 'fire', {
            position,
            spread: profile.fire.spread,
            maxDistance: profile.fire.distance,
            directivity: 120
        });
    }
    async playReloadSequence(weaponId, position) {
        const profile = this.soundProfiles.get(weaponId);
        if (!profile)
            return;
        for (const action of profile.reload.sequence) {
            this.audioManager.playWeaponSound(weaponId, action, {
                position,
                spread: 90,
                maxDistance: 200,
                directivity: 180
            });
            // Espera o tempo apropriado entre sons
            await new Promise(resolve => setTimeout(resolve, 200));
        }
    }
    playWeaponHandling(weaponId, action, position) {
        const profile = this.soundProfiles.get(weaponId);
        if (!profile)
            return;
        this.audioManager.playWeaponSound(weaponId, action, {
            position,
            spread: 120,
            maxDistance: 150,
            directivity: 180
        });
    }
    async loadImpactSounds() {
        const surfaces = ['metal', 'concrete', 'wood', 'flesh'];
        for (const surface of surfaces) {
            await this.audioManager.loadImpactSFX(surface);
        }
    }
    playImpactSound(surface, position) {
        this.audioManager.playImpactSound(surface, {
            position,
            spread: 90,
            maxDistance: 300,
            directivity: 150,
            rolloffFactor: 2
        });
    }
    updateListenerPosition(position, orientation) {
        this.audioManager.updateListenerPosition(position, orientation);
    }
}
exports.WeaponSFX = WeaponSFX;
//# sourceMappingURL=WeaponSFX.js.map