"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HybridLightingSystem = void 0;
var LightmapFormat;
(function (LightmapFormat) {
    LightmapFormat[LightmapFormat["R11G11B10_FLOAT"] = 0] = "R11G11B10_FLOAT";
    LightmapFormat[LightmapFormat["RGBA16_FLOAT"] = 1] = "RGBA16_FLOAT";
    LightmapFormat[LightmapFormat["RGBA8_UNORM"] = 2] = "RGBA8_UNORM"; // Baixa qualidade, economia de memória
})(LightmapFormat || (LightmapFormat = {}));
class HybridLightingSystem {
    constructor() {
        this.dynamicLights = new Map();
        this.lightmaps = new Map();
        this.shadowCascades = [];
        this.initializeShadowCascades();
    }
    initializeShadowCascades() {
        // Configura cascatas de sombra com diferentes resoluções e ranges
        const cascadeRanges = [20, 50, 120, 300]; // Distâncias em unidades
        const resolutions = [2048, 1024, 512, 256]; // Resoluções por cascata
        for (let i = 0; i < HybridLightingSystem.SHADOW_CASCADE_COUNT; i++) {
            this.shadowCascades.push({
                resolution: resolutions[i],
                range: cascadeRanges[i],
                bias: 0.001 * (i + 1), // Bias aumenta com a distância
                softness: i * 0.5 // Sombras mais suaves à distância
            });
        }
    }
    addDynamicLight(id, light) {
        if (this.dynamicLights.size >= HybridLightingSystem.MAX_DYNAMIC_LIGHTS) {
            console.warn('Limite máximo de luzes dinâmicas atingido');
            return false;
        }
        this.dynamicLights.set(id, this.optimizeLight(light));
        return true;
    }
    optimizeLight(light) {
        return {
            ...light,
            radius: Math.min(light.radius, 100), // Limita raio para performance
            castsShadows: light.intensity > 0.5 && light.castsShadows // Só projeta sombras se for intensa o suficiente
        };
    }
    updateDynamicLighting(cameraPosition, visibleObjects) {
        this.cullLights(cameraPosition);
        this.updateShadowMaps(cameraPosition);
        this.renderDynamicLighting(visibleObjects);
    }
    cullLights(cameraPosition) {
        // Implementa culling de luzes baseado em distância e visibilidade
        this.dynamicLights.forEach((light, id) => {
            const distance = this.distance(cameraPosition, light.position);
            if (distance > light.radius * 2) {
                // Desativa temporariamente luzes muito distantes
                light.intensity = 0;
            }
        });
    }
    updateShadowMaps(cameraPosition) {
        // Atualiza apenas shadow maps necessários
        this.dynamicLights.forEach((light, id) => {
            if (!light.castsShadows || light.intensity <= 0)
                return;
            // Determina qual cascata usar baseado na distância
            const distance = this.distance(cameraPosition, light.position);
            const cascadeIndex = this.findCascadeIndex(distance);
            if (cascadeIndex >= 0) {
                this.renderShadowMap(light, this.shadowCascades[cascadeIndex]);
            }
        });
    }
    findCascadeIndex(distance) {
        for (let i = 0; i < this.shadowCascades.length; i++) {
            if (distance <= this.shadowCascades[i].range) {
                return i;
            }
        }
        return -1;
    }
    renderShadowMap(light, cascade) {
        // TODO: Implementar renderização de shadow map
        // - Usar resolução apropriada da cascata
        // - Aplicar otimizações de culling
        // - Usar hardware PCF quando disponível
    }
    registerLightmap(id, data) {
        // Registra lightmap pré-computado para objetos estáticos
        this.lightmaps.set(id, this.optimizeLightmap(data));
    }
    optimizeLightmap(data) {
        // Otimiza formato do lightmap baseado em hardware
        const optimized = { ...data };
        // Escolhe formato baseado em suporte de hardware
        if (this.hasHardwareR11G11B10Support()) {
            optimized.format = LightmapFormat.R11G11B10_FLOAT;
        }
        else {
            optimized.format = LightmapFormat.RGBA8_UNORM;
        }
        return optimized;
    }
    hasHardwareR11G11B10Support() {
        // TODO: Verificar suporte do hardware
        return true;
    }
    renderDynamicLighting(visibleObjects) {
        // TODO: Implementar renderização de iluminação dinâmica
        // - Aplicar iluminação por tile/cluster
        // - Combinar com lightmaps
        // - Otimizar para objetos dinâmicos
    }
    // Utilitários
    distance(v1, v2) {
        const dx = v2.x - v1.x;
        const dy = v2.y - v1.y;
        const dz = v2.z - v1.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
}
exports.HybridLightingSystem = HybridLightingSystem;
HybridLightingSystem.MAX_DYNAMIC_LIGHTS = 64;
HybridLightingSystem.SHADOW_CASCADE_COUNT = 4;
HybridLightingSystem.LIGHTMAP_SIZE = 2048;
//# sourceMappingURL=HybridLighting.js.map