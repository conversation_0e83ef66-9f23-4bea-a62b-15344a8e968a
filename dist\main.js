
const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');

console.log('🚀 Tactical Nexus - Iniciando aplicação...');
console.log('📁 Diretório atual:', __dirname);
console.log('📄 Arquivo index.html esperado em:', path.join(__dirname, 'index.html'));

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1920,
        height: 1080,
        minWidth: 1280,
        minHeight: 720,
        show: false,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false
        }
    });

    const indexPath = path.join(__dirname, 'index.html');
    console.log('🔍 Tentando carregar:', indexPath);
    mainWindow.loadFile(indexPath);

    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.maximize();
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
