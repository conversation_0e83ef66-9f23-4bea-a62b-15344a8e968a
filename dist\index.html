<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tactical Nexus</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="icon" href="../assets/icon.ico">
</head>
<body>
    <div id="app" class="tactical-nexus-app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-content">
                <div class="logo-container">
                    <h1 class="game-title">TACTICAL NEXUS</h1>
                    <p class="game-subtitle">CLASSIFIED WARFARE SIMULATION</p>
                </div>
                
                <div class="loading-bar-container">
                    <div class="loading-bar">
                        <div class="loading-progress" id="loading-progress"></div>
                    </div>
                    <p class="loading-text" id="loading-text">INITIALIZING COMBAT SYSTEMS...</p>
                </div>

                <div class="system-info">
                    <p>VERSION 1.0.0 | COMBAT READY | MAXIMUM LETHALITY</p>
                </div>
            </div>
        </div>

        <!-- Main Game Interface -->
        <div id="main-interface" class="main-interface hidden">
            <!-- Header -->
            <header class="game-header">
                <div class="header-left">
                    <div class="logo">
                        <h1>TACTICAL NEXUS</h1>
                    </div>
                    <div class="server-status">
                        <span class="status-indicator online"></span>
                        <span>OPERATIONAL</span>
                    </div>
                </div>

                <div class="header-center">
                    <div class="performance-stats">
                        <div class="stat">
                            <span class="label">FPS</span>
                            <span class="value" id="fps-counter">999</span>
                        </div>
                        <div class="stat">
                            <span class="label">PING</span>
                            <span class="value" id="ping-counter">15</span>
                        </div>
                        <div class="stat">
                            <span class="label">PLAYERS</span>
                            <span class="value" id="player-counter">1,337</span>
                        </div>
                    </div>
                </div>

                <div class="header-right">
                    <div class="player-info" id="player-info">
                        <div class="player-avatar"></div>
                        <div class="player-details">
                            <span class="username" id="player-username">OPERATOR-7734</span>
                            <span class="level" id="player-level">RANK: LIEUTENANT</span>
                        </div>
                        <div class="currency">
                            <span class="coins" id="player-coins">◈ 15,420 CREDITS</span>
                            <span class="premium" id="player-premium">◉ 250 TOKENS</span>
                        </div>
                    </div>

                    <div class="window-controls">
                        <button class="window-btn minimize" id="minimize-btn">−</button>
                        <button class="window-btn maximize" id="maximize-btn">□</button>
                        <button class="window-btn close" id="close-btn">×</button>
                    </div>
                </div>
            </header>

            <!-- Navigation -->
            <nav class="main-navigation">
                <button class="nav-btn active" data-section="home">
                    <span class="icon">▣</span>
                    <span class="label">DASHBOARD</span>
                </button>
                <button class="nav-btn" data-section="play">
                    <span class="icon">◉</span>
                    <span class="label">DEPLOY</span>
                </button>
                <button class="nav-btn" data-section="loadout">
                    <span class="icon">⬢</span>
                    <span class="label">ARSENAL</span>
                </button>
                <button class="nav-btn" data-section="store">
                    <span class="icon">◈</span>
                    <span class="label">ARMORY</span>
                </button>
                <button class="nav-btn" data-section="profile">
                    <span class="icon">◎</span>
                    <span class="label">OPERATOR</span>
                </button>
                <button class="nav-btn" data-section="settings">
                    <span class="icon">◐</span>
                    <span class="label">SYSTEMS</span>
                </button>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Home Section -->
                <section id="home-section" class="content-section active">
                    <div class="welcome-area">
                        <h2>TACTICAL NEXUS COMMAND CENTER</h2>
                        <p>Advanced warfare simulation platform. Engage in high-stakes tactical operations.</p>

                        <div class="quick-actions">
                            <button class="btn-primary btn-large" id="quick-play-btn">
                                ◉ QUICK DEPLOYMENT
                            </button>
                            <button class="btn-secondary" id="training-btn">
                                ◈ COMBAT TRAINING
                            </button>
                        </div>
                    </div>

                    <div class="featured-content">
                        <div class="featured-modes">
                            <h3>ACTIVE OPERATIONS</h3>
                            <div class="mode-grid">
                                <div class="mode-card featured">
                                    <div class="mode-icon">◉</div>
                                    <h4>ELIMINATION PROTOCOL</h4>
                                    <p>100 operators, single survivor</p>
                                    <span class="mode-time">20-30 min</span>
                                </div>
                                <div class="mode-card">
                                    <div class="mode-icon">◈</div>
                                    <h4>SQUAD ELIMINATION</h4>
                                    <p>8v8 tactical engagement</p>
                                    <span class="mode-time">10-15 min</span>
                                </div>
                            </div>
                        </div>

                        <div class="news-feed">
                            <h3>INTEL BRIEFING</h3>
                            <div class="news-items">
                                <div class="news-item">
                                    <div class="news-icon">◉</div>
                                    <div class="news-content">
                                        <h4>NEXUS PROTOCOL ACTIVATED</h4>
                                        <p>Advanced tactical warfare system online.</p>
                                        <span class="news-time">NOW</span>
                                    </div>
                                </div>
                                <div class="news-item">
                                    <div class="news-icon">◈</div>
                                    <div class="news-content">
                                        <h4>NEW AO: URBAN SECTOR</h4>
                                        <p>High-density combat zone deployed.</p>
                                        <span class="news-time">2 MIN AGO</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Play Section -->
                <section id="play-section" class="content-section">
                    <h2>SELECT MISSION TYPE</h2>
                    <div class="game-modes-grid" id="game-modes-grid">
                        <!-- Mission types will be inserted dynamically -->
                    </div>

                    <div class="matchmaking-status hidden" id="matchmaking-status">
                        <div class="searching-animation">
                            <div class="spinner"></div>
                            <h3>SEARCHING FOR TARGETS...</h3>
                            <p id="search-status">ANALYZING HOSTILE OPERATORS...</p>
                            <button class="btn-secondary" id="cancel-search">ABORT MISSION</button>
                        </div>
                    </div>
                </section>

                <!-- Loadout Section -->
                <section id="loadout-section" class="content-section">
                    <h2>Equipamentos</h2>
                    <div class="loadout-editor">
                        <div class="weapon-slots">
                            <div class="weapon-slot primary">
                                <label>Arma Primária</label>
                                <div class="weapon-preview" id="primary-weapon">
                                    <img src="../assets/weapons/m4a1.png" alt="M4A1">
                                    <span>M4A1 Assault Rifle</span>
                                </div>
                                <button class="change-weapon-btn">Alterar</button>
                            </div>
                            <div class="weapon-slot secondary">
                                <label>Arma Secundária</label>
                                <div class="weapon-preview" id="secondary-weapon">
                                    <img src="../assets/weapons/glock.png" alt="Glock">
                                    <span>Glock Pistol</span>
                                </div>
                                <button class="change-weapon-btn">Alterar</button>
                            </div>
                        </div>
                        
                        <div class="equipment-slots">
                            <div class="equipment-slot">
                                <label>Equipamento 1</label>
                                <div class="equipment-preview">Granada de Fragmentação</div>
                            </div>
                            <div class="equipment-slot">
                                <label>Equipamento 2</label>
                                <div class="equipment-preview">Granada de Fumaça</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Store Section -->
                <section id="store-section" class="content-section">
                    <h2>Loja</h2>
                    <div class="store-categories">
                        <button class="category-btn active" data-category="weapons">Armas</button>
                        <button class="category-btn" data-category="skins">Skins</button>
                        <button class="category-btn" data-category="boosts">Boosts</button>
                        <button class="category-btn" data-category="bundles">Pacotes</button>
                    </div>
                    <div class="store-items" id="store-items">
                        <!-- Itens da loja serão inseridos dinamicamente -->
                    </div>
                </section>

                <!-- Profile Section -->
                <section id="profile-section" class="content-section">
                    <h2>Perfil do Jogador</h2>
                    <div class="profile-content">
                        <div class="profile-stats">
                            <div class="stat-card">
                                <h3>Estatísticas de Combate</h3>
                                <div class="stats-grid">
                                    <div class="stat">
                                        <span class="label">Eliminações</span>
                                        <span class="value" id="kills-stat">1,337</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">Mortes</span>
                                        <span class="value" id="deaths-stat">420</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">K/D Ratio</span>
                                        <span class="value" id="kdr-stat">3.18</span>
                                    </div>
                                    <div class="stat">
                                        <span class="label">Precisão</span>
                                        <span class="value" id="accuracy-stat">72%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <h3>Progresso</h3>
                                <div class="level-progress">
                                    <div class="level-info">
                                        <span class="current-level">Nível 25</span>
                                        <span class="next-level">Nível 26</span>
                                    </div>
                                    <div class="xp-bar">
                                        <div class="xp-progress" style="width: 70%"></div>
                                    </div>
                                    <div class="xp-info">
                                        <span>15,750 / 22,500 XP</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings-section" class="content-section">
                    <h2>Configurações</h2>
                    <div class="settings-tabs">
                        <button class="tab-btn active" data-tab="graphics">Gráficos</button>
                        <button class="tab-btn" data-tab="audio">Áudio</button>
                        <button class="tab-btn" data-tab="controls">Controles</button>
                        <button class="tab-btn" data-tab="game">Jogo</button>
                    </div>
                    
                    <div class="settings-content">
                        <div id="graphics-settings" class="settings-tab active">
                            <div class="setting-group">
                                <h3>Performance</h3>
                                <div class="setting-item">
                                    <label>Limite de FPS</label>
                                    <select id="fps-limit">
                                        <option value="60">60 FPS</option>
                                        <option value="120">120 FPS</option>
                                        <option value="240">240 FPS</option>
                                        <option value="999" selected>999 FPS (Recomendado)</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Qualidade Gráfica</label>
                                    <select id="graphics-quality">
                                        <option value="low">Baixa</option>
                                        <option value="medium">Média</option>
                                        <option value="high">Alta</option>
                                        <option value="ultra" selected>Ultra</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <!-- Notifications -->
        <div id="notifications" class="notifications-container">
            <!-- Notificações serão inseridas dinamicamente -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
</body>
</html>
