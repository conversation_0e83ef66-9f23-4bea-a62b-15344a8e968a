"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventorySystem = void 0;
class InventorySystem {
    constructor() {
        this.playerInventories = new Map();
    }
    initializePlayerInventory(playerId) {
        const initialInventory = [
            { itemId: null, type: 'primary' },
            { itemId: null, type: 'secondary' },
            { itemId: null, type: 'melee' },
            { itemId: null, type: 'grenade' },
            { itemId: null, type: 'grenade' },
            { itemId: null, type: 'grenade' },
            { itemId: null, type: 'armor' },
            { itemId: null, type: 'healkit' },
            { itemId: null, type: 'healkit' },
            { itemId: null, type: 'utility' }
        ];
        this.playerInventories.set(playerId, initialInventory);
    }
    addItem(playerId, itemId, type) {
        const inventory = this.playerInventories.get(playerId);
        if (!inventory)
            return false;
        // Encontra slot vazio apropriado
        const availableSlot = inventory.find(slot => slot.type === type && slot.itemId === null);
        if (availableSlot) {
            availableSlot.itemId = itemId;
            return true;
        }
        return false;
    }
    removeItem(playerId, itemId) {
        const inventory = this.playerInventories.get(playerId);
        if (!inventory)
            return false;
        const slot = inventory.find(slot => slot.itemId === itemId);
        if (slot) {
            slot.itemId = null;
            return true;
        }
        return false;
    }
    hasSpace(playerId, type) {
        const inventory = this.playerInventories.get(playerId);
        if (!inventory)
            return false;
        const typeSlots = inventory.filter(slot => slot.type === type);
        const emptySlots = typeSlots.filter(slot => slot.itemId === null);
        switch (type) {
            case 'primary':
                return emptySlots.length > 0;
            case 'secondary':
                return emptySlots.length > 0;
            case 'grenade':
                return emptySlots.length > 0;
            case 'healkit':
                return emptySlots.length > 0;
            case 'utility':
                return emptySlots.length > 0;
            default:
                return false;
        }
    }
    getInventory(playerId) {
        return this.playerInventories.get(playerId) || null;
    }
    hasItem(playerId, itemId) {
        const inventory = this.playerInventories.get(playerId);
        if (!inventory)
            return false;
        return inventory.some(slot => slot.itemId === itemId);
    }
    dropItem(playerId, itemId, position) {
        if (this.removeItem(playerId, itemId)) {
            // Aqui você pode implementar a lógica para criar o item no mundo
            // usando o LootSystem
            return true;
        }
        return false;
    }
    transferItem(fromPlayerId, toPlayerId, itemId) {
        if (!this.hasItem(fromPlayerId, itemId))
            return false;
        const inventory = this.playerInventories.get(fromPlayerId);
        if (!inventory)
            return false;
        const slot = inventory.find(slot => slot.itemId === itemId);
        if (!slot)
            return false;
        if (this.hasSpace(toPlayerId, slot.type)) {
            this.removeItem(fromPlayerId, itemId);
            return this.addItem(toPlayerId, itemId, slot.type);
        }
        return false;
    }
}
exports.InventorySystem = InventorySystem;
InventorySystem.MAX_PRIMARY_WEAPONS = 1;
InventorySystem.MAX_SECONDARY_WEAPONS = 1;
InventorySystem.MAX_GRENADES = 3;
InventorySystem.MAX_HEALKITS = 2;
InventorySystem.MAX_UTILITY = 1;
//# sourceMappingURL=InventorySystem.js.map