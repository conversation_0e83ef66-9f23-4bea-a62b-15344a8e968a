{"version": 3, "file": "SafeZoneManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/SafeZoneManager.ts"], "names": [], "mappings": ";;;AAaA,MAAa,eAAe;IAWxB,YAAY,UAAsB;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAE7B,qCAAqC;QACrC,IAAI,CAAC,KAAK,GAAG;YACT,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACnC,aAAa,EAAE,eAAe,CAAC,cAAc;YAC7C,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAChC,UAAU,EAAE,eAAe,CAAC,cAAc;YAC1C,KAAK,EAAE,CAAC;YACR,YAAY,EAAE,eAAe,CAAC,UAAU;YACxC,eAAe,EAAE,CAAC;SACrB,CAAC;QAEF,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAEO,qBAAqB;QACzB,6CAA6C;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC;IAC5C,CAAC;IAEM,MAAM,CAAC,SAAiB;QAC3B,iCAAiC;QACjC,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,SAAS,CAAC;QAErC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC5C,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAC9B,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;YAC7E,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QAEjD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAEvE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,WAAW,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC;QAChG,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACtC,yDAAyD;QACzD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEjD,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YACvB,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC;YACjF,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7B,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC;SACpF,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAChC,IAAI,CAAC,KAAK,CAAC,aAAa,EACxB,IAAI,CAAC,KAAK,CAAC,UAAU,EACrB,cAAc,CACjB,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,KAAa;QAC1C,4DAA4D;QAC5D,MAAM,QAAQ,GAAG,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC;QAChD,OAAO,IAAI,CAAC,IAAI,CACZ,eAAe,CAAC,cAAc,EAC9B,eAAe,CAAC,UAAU,EAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,qDAAqD;SAChF,CAAC;IACN,CAAC;IAEO,wBAAwB;QAC5B,mEAAmE;QACnE,4BAA4B;QAC5B,6BAA6B;QAC7B,kEAAkE;QAElE,8EAA8E;QAC9E,0BAA0B;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC;QAElE,OAAO;YACH,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC1D,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAC7D,CAAC;IACN,CAAC;IAEM,oBAAoB,CAAC,QAAiB;QACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClG,CAAC;IAEM,mBAAmB,CAAC,QAAiB;QACxC,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACtC,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACxC,CAAC;IAEO,IAAI,CAAC,KAAa,EAAE,GAAW,EAAE,QAAgB;QACrD,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC;IAC5C,CAAC;IAEO,UAAU,CAAC,CAAS;QACxB,qDAAqD;QACrD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC;IAEM,QAAQ;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;;AAhJL,0CAiJC;AAhJ2B,8BAAc,GAAG,IAAI,CAAC,CAAC,sBAAsB;AAC7C,0BAAU,GAAG,EAAE,CAAC,CAAC,oBAAoB;AACrC,sBAAM,GAAG,CAAC,CAAC,CAAC,kCAAkC;AAC9C,2BAAW,GAAG,GAAG,CAAC,CAAC,qBAAqB;AACxC,0BAAU,GAAG,EAAE,CAAC,CAAC,0BAA0B"}