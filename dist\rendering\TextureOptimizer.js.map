{"version": 3, "file": "TextureOptimizer.js", "sourceRoot": "", "sources": ["../../src/rendering/TextureOptimizer.ts"], "names": [], "mappings": ";;;AAAA,+DAA0F;AAC1F,+BAA4B;AAC5B,2BAAkC;AAClC,2CAAyC;AA4CzC,MAAa,gBAAgB;IAezB;QAbQ,cAAS,GAAc;YAC3B,cAAc,EAAE;gBACZ,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE;gBAC5C,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;aAChD;YACD,mBAAmB,EAAE;gBACjB,GAAG,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;gBACtC,GAAG,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;gBACvC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC5D,IAAI,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;aAC7C;SACJ,CAAC;QAGE,IAAI,CAAC,gBAAgB,GAAG,IAAI,kDAA4B,EAAE,CAAC;QAC3D,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,UAAU;QACd,IAAI,CAAC;YACD,MAAM,UAAU,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;YACzE,MAAM,UAAU,GAAG,IAAA,iBAAY,EAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACpD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,WAAqC,EACrC,KAAa,EACb,MAAc,EACd,SAA6C,EAAE;QAE/C,MAAM,WAAW,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC5C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAElD,MAAM,aAAa,GAA8B;YAC7C,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO;YACrD,OAAO,EAAE,GAAG;YACZ,eAAe,EAAE,IAAI;YACrB,QAAQ,EAAE,SAAS;SACtB,CAAC;QAEF,MAAM,WAAW,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QAEpD,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;YAEpC,yCAAyC;YACzC,MAAM,WAAW,GAAG,WAAW,YAAY,UAAU,CAAC,CAAC;gBACnD,WAAW,CAAC,CAAC;gBACb,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;YAEhC,wCAAwC;YACxC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC5E,MAAM,kBAAkB,GAAG;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,SAAS,EAAE,YAAY,CAAC,SAAS;aACpC,CAAC;YAEF,qBAAqB;YACrB,MAAM,cAAc,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE;gBACnD,KAAK;gBACL,MAAM;gBACN,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,GAAG,kBAAkB;aACxB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,cAAc,GAAG,OAAO,GAAG,SAAS,CAAC;YAE3C,mBAAmB;YACnB,MAAM,YAAY,GAAG,WAAW,CAAC,UAAU,CAAC;YAC5C,MAAM,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC;YACjD,MAAM,gBAAgB,GAAG,YAAY,GAAG,cAAc,CAAC;YAEvD,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAEvE,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,WAAW,EAAE;gBAC5C,YAAY;gBACZ,aAAa,EAAE,cAAc;gBAC7B,gBAAgB;gBAChB,gBAAgB,EAAE,cAAc;gBAChC,eAAe,EAAE,CAAC,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG;gBACvE,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,IAAI,EAAE,OAAO,CAAC,IAAI;aACrB,CAAC,CAAC;YAEH,OAAO;gBACH,IAAI,EAAE,IAAI,UAAU,CAAC,cAAc,CAAC;gBACpC,KAAK;gBACL,MAAM;gBACN,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,YAAY;gBACZ,cAAc;gBACd,OAAO,EAAE;oBACL,gBAAgB;oBAChB,gBAAgB,EAAE,cAAc;oBAChC,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;iBACrB;aACJ,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAoB,EAAE,UAAsB;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC5D,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEO,aAAa,CAAC,QAAoB,EAAE,UAAsB;QAC9D,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACzC,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,GAAG,IAAI,GAAG,CAAC;QACX,IAAI,GAAG,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC;QAE/B,MAAM,MAAM,GAAG,GAAG,CAAC;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAoB,EAAE,UAAsB;QACpE,qCAAqC;QACrC,4DAA4D;QAC5D,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,MAAM,UAAU,GAAG,CAAC,CAAC;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC;YAC7B,MAAM,GAAG,GAAG,KAAK,GAAG,UAAU,CAAC;YAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAEhD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAElE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YACnC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YAEnC,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;YAC1E,IAAI,IAAI,SAAS,GAAG,WAAW,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,GAAG,OAAO,CAAC;IAC1B,CAAC;IAEO,IAAI,CAAC,IAAgB;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACjE,CAAC;IAEO,QAAQ,CAAC,IAAgB,EAAE,IAAY;QAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACrF,CAAC;IAEO,UAAU,CAAC,KAAiB,EAAE,KAAiB,EAAE,KAAa,EAAE,KAAa;QACjF,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,WAAW,IAAI,OAAO,CAAC,cAAc,CAAC;QAC1C,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,oDAAoD;QACpD,OAAO,EAAE,CAAC;IACd,CAAC;CACJ;AAzMD,4CAyMC"}