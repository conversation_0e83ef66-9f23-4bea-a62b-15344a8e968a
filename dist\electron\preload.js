"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// API segura exposta para o renderer process
const electronAPI = {
    // Informações do sistema
    getSystemInfo: () => electron_1.ipcRenderer.invoke('get-system-info'),
    getPerformanceSettings: () => electron_1.ipcRenderer.invoke('get-performance-settings'),
    getGameStats: () => electron_1.ipcRenderer.invoke('get-game-stats'),
    // Controles da janela
    minimizeWindow: () => electron_1.ipcRenderer.send('window-minimize'),
    maximizeWindow: () => electron_1.ipcRenderer.send('window-maximize'),
    closeWindow: () => electron_1.ipcRenderer.send('window-close'),
    // Navegação externa
    openExternal: (url) => electron_1.ipcRenderer.send('open-external', url),
    // Listeners para eventos do main process
    onQuickPlay: (callback) => {
        electron_1.ipcRenderer.on('quick-play', callback);
        return () => electron_1.ipcRenderer.removeListener('quick-play', callback);
    },
    onFindMatch: (callback) => {
        electron_1.ipcRenderer.on('find-match', callback);
        return () => electron_1.ipcRenderer.removeListener('find-match', callback);
    },
    onShowStats: (callback) => {
        electron_1.ipcRenderer.on('show-stats', callback);
        return () => electron_1.ipcRenderer.removeListener('show-stats', callback);
    },
    onOpenSettings: (callback) => {
        electron_1.ipcRenderer.on('open-settings', callback);
        return () => electron_1.ipcRenderer.removeListener('open-settings', callback);
    },
    onUpdateProgress: (callback) => {
        electron_1.ipcRenderer.on('update-progress', (_, progress) => callback(progress));
        return () => electron_1.ipcRenderer.removeListener('update-progress', callback);
    },
    // API do jogo
    game: {
        // Matchmaking
        searchMatch: (gameMode, region) => {
            return new Promise((resolve) => {
                // Simula busca por partida
                setTimeout(() => {
                    resolve({
                        matchId: `match_${Date.now()}`,
                        gameMode,
                        region,
                        estimatedTime: Math.floor(Math.random() * 30) + 10 // 10-40 segundos
                    });
                }, 2000);
            });
        },
        // Estatísticas do jogador
        getPlayerStats: () => {
            return Promise.resolve({
                level: 25,
                experience: 15750,
                experienceToNext: 2250,
                kills: 1337,
                deaths: 420,
                wins: 89,
                losses: 31,
                kdr: 3.18,
                accuracy: 0.72,
                playtime: 156000 // segundos
            });
        },
        // Economia do jogador
        getPlayerEconomy: () => {
            return Promise.resolve({
                coins: 15420,
                premiumCurrency: 250,
                unlockedWeapons: ['m4a1', 'ak47', 'awp', 'mp5'],
                unlockedSkins: ['desert_camo', 'urban_camo', 'gold_plated'],
                activeBoosts: [
                    {
                        type: 'xp',
                        multiplier: 2.0,
                        timeRemaining: 3600 // 1 hora
                    }
                ]
            });
        },
        // Configurações do jogo
        getGameSettings: () => {
            return Promise.resolve({
                graphics: {
                    quality: 'ultra',
                    resolution: '2560x1440',
                    fpsLimit: 999,
                    vsync: false,
                    antiAliasing: 'MSAA_8x',
                    shadows: 'ultra',
                    textures: 'ultra'
                },
                audio: {
                    masterVolume: 80,
                    musicVolume: 60,
                    sfxVolume: 90,
                    voiceVolume: 85,
                    spatialAudio: true
                },
                controls: {
                    mouseSensitivity: 2.5,
                    invertY: false,
                    keyBindings: {
                        moveForward: 'W',
                        moveBackward: 'S',
                        moveLeft: 'A',
                        moveRight: 'D',
                        jump: 'Space',
                        crouch: 'Ctrl',
                        reload: 'R',
                        interact: 'F'
                    }
                }
            });
        },
        // Salvar configurações
        saveGameSettings: (settings) => {
            console.log('💾 Salvando configurações:', settings);
            return Promise.resolve(true);
        },
        // Leaderboards
        getLeaderboard: (category, limit = 10) => {
            const mockPlayers = [];
            for (let i = 1; i <= limit; i++) {
                mockPlayers.push({
                    rank: i,
                    username: `Player${i.toString().padStart(3, '0')}`,
                    level: Math.floor(Math.random() * 50) + 50,
                    value: Math.floor(Math.random() * 5000) + 1000
                });
            }
            return Promise.resolve(mockPlayers);
        },
        // Loja
        getStoreItems: (category) => {
            const items = [
                {
                    id: 'weapon_ak47',
                    name: 'AK-47',
                    description: 'Rifle de assalto poderoso',
                    category: 'weapon',
                    rarity: 'rare',
                    price: { coins: 5000 },
                    owned: false
                },
                {
                    id: 'skin_dragon_fire',
                    name: 'Dragon Fire',
                    description: 'Skin épica com efeitos de fogo',
                    category: 'skin',
                    rarity: 'epic',
                    price: { premiumCurrency: 800 },
                    owned: false
                },
                {
                    id: 'boost_xp_2x',
                    name: '2x XP Boost',
                    description: 'Dobra XP por 24 horas',
                    category: 'boost',
                    rarity: 'common',
                    price: { coins: 2000 },
                    owned: false
                }
            ];
            if (category) {
                return Promise.resolve(items.filter(item => item.category === category));
            }
            return Promise.resolve(items);
        },
        // Comprar item
        purchaseItem: (itemId, paymentMethod) => {
            console.log(`💰 Comprando item ${itemId} com ${paymentMethod}`);
            return Promise.resolve({
                success: true,
                message: 'Item comprado com sucesso!',
                newBalance: {
                    coins: 10420,
                    premiumCurrency: 250
                }
            });
        }
    },
    // Utilitários
    utils: {
        formatTime: (seconds) => {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        },
        formatNumber: (num) => {
            return num.toLocaleString();
        },
        formatCurrency: (amount, type) => {
            const symbol = type === 'coins' ? '💰' : '💎';
            return `${symbol} ${amount.toLocaleString()}`;
        }
    }
};
// Expõe a API de forma segura
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
//# sourceMappingURL=preload.js.map