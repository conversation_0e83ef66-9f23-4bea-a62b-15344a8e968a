{"version": 3, "file": "OptimizationMetrics.js", "sourceRoot": "", "sources": ["../../src/rendering/OptimizationMetrics.ts"], "names": [], "mappings": ";;;AAWA,MAAa,4BAA4B;IAGrC;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED,cAAc,CAAC,EAAU;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE;gBACjB,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;aACrB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,YAAY,CAAC,EAAU,EAAE,OAAqC;QAC1D,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE;gBACjB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAE;gBACxB,GAAG,OAAO;aACb,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,YAAY,CAAC,EAAU;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrC,OAAO,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;IACtF,CAAC;IAED,UAAU,CAAC,EAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,KAAK;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED,cAAc;QACV,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACjD,MAAM,IAAI,eAAe,EAAE,IAAI,CAAC;YAChC,MAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAChF,MAAM,IAAI,wBAAwB,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAClF,MAAM,IAAI,yBAAyB,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5E,MAAM,IAAI,yBAAyB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3E,MAAM,IAAI,6BAA6B,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAEjF,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBAChC,MAAM,IAAI,gBAAgB,OAAO,CAAC,OAAO,IAAI,CAAC;YAClD,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,cAAc,OAAO,CAAC,MAAM,IAAI,CAAC;YAC/C,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC7B,MAAM,IAAI,WAAW,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACvD,CAAC;QACL,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AA/DD,oEA+DC"}