"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetStreamingManager = void 0;
class AssetStreamingManager {
    constructor() {
        this.VIEW_DISTANCE = 1000; // 1km de distância de visualização
        this.MAX_CONCURRENT_LOADS = 5;
        this.currentLoads = 0;
        this.zones = new Map();
        this.activeZones = new Set();
        this.loadedAssets = new Map();
    }
    setupStreamingZone(config) {
        const zone = {
            ...config,
            isActive: false,
            loadedAssets: new Set()
        };
        this.zones.set(config.id, zone);
    }
    async update(playerPosition) {
        // Atualiza zonas ativas baseado na posição do jogador
        for (const [id, zone] of this.zones) {
            const distance = this.calculateDistance(playerPosition, zone.position);
            const shouldBeActive = distance <= this.VIEW_DISTANCE;
            if (shouldBeActive && !zone.isActive) {
                await this.activateZone(zone);
            }
            else if (!shouldBeActive && zone.isActive) {
                await this.deactivateZone(zone);
            }
        }
        // Gerencia carregamento de assets
        await this.manageAssetLoading();
    }
    async activateZone(zone) {
        zone.isActive = true;
        this.activeZones.add(zone.id);
        // Inicia carregamento de assets prioritários
        const priorityAssets = zone.assets
            .filter(asset => asset.priority > 0.7)
            .sort((a, b) => b.priority - a.priority);
        for (const asset of priorityAssets) {
            if (this.currentLoads < this.MAX_CONCURRENT_LOADS) {
                await this.loadAsset(asset, zone);
            }
        }
    }
    async deactivateZone(zone) {
        zone.isActive = false;
        this.activeZones.delete(zone.id);
        // Libera assets não prioritários
        for (const assetId of zone.loadedAssets) {
            const asset = this.loadedAssets.get(assetId);
            if (asset && asset.priority < 0.5) {
                await this.unloadAsset(asset, zone);
            }
        }
    }
    async loadAsset(asset, zone) {
        if (this.loadedAssets.has(asset.id))
            return;
        this.currentLoads++;
        try {
            // TODO: Implementar carregamento real do asset baseado no tipo
            await new Promise(resolve => setTimeout(resolve, 100)); // Simulação
            this.loadedAssets.set(asset.id, asset);
            zone.loadedAssets.add(asset.id);
        }
        finally {
            this.currentLoads--;
        }
    }
    async unloadAsset(asset, zone) {
        this.loadedAssets.delete(asset.id);
        zone.loadedAssets.delete(asset.id);
        // TODO: Implementar liberação real do asset
    }
    calculateDistance(a, b) {
        const dx = a.x - b.x;
        const dz = a.z - b.z;
        return Math.sqrt(dx * dx + dz * dz);
    }
    async manageAssetLoading() {
        if (this.currentLoads >= this.MAX_CONCURRENT_LOADS)
            return;
        // Coleta todas as zonas ativas que ainda têm assets para carregar
        const activeZonesWithPendingAssets = Array.from(this.activeZones)
            .map(id => this.zones.get(id))
            .filter((zone) => {
            return zone !== undefined && zone.assets.length > zone.loadedAssets.size;
        });
        if (activeZonesWithPendingAssets.length === 0)
            return;
        // Ordena zonas por prioridade
        activeZonesWithPendingAssets.sort((a, b) => b.priority - a.priority);
        // Para cada zona ativa
        for (const zone of activeZonesWithPendingAssets) {
            if (this.currentLoads >= this.MAX_CONCURRENT_LOADS)
                break;
            // Encontra assets não carregados
            const pendingAssets = zone.assets.filter(asset => !zone.loadedAssets.has(asset.id));
            // Ordena por prioridade
            pendingAssets.sort((a, b) => b.priority - a.priority);
            // Carrega assets até atingir o limite
            for (const asset of pendingAssets) {
                if (this.currentLoads >= this.MAX_CONCURRENT_LOADS)
                    break;
                await this.loadAsset(asset, zone);
            }
        }
    }
    getStreamingZonesCount() {
        return this.zones.size;
    }
    getActiveZonesCount() {
        return this.activeZones.size;
    }
    getLoadedAssetsCount() {
        return this.loadedAssets.size;
    }
    getStreamingStats() {
        return {
            totalZones: this.zones.size,
            activeZones: this.activeZones.size,
            loadedAssets: this.loadedAssets.size,
            currentLoads: this.currentLoads,
            memoryUsage: this.estimateMemoryUsage()
        };
    }
    estimateMemoryUsage() {
        let totalMemory = 0;
        for (const asset of this.loadedAssets.values()) {
            totalMemory += asset.size;
        }
        return totalMemory;
    }
}
exports.AssetStreamingManager = AssetStreamingManager;
//# sourceMappingURL=AssetStreamingManager.js.map