const fs = require('fs');
const path = require('path');

console.log('🔨 Iniciando build do Tactical Nexus...');

// Cria diretório dist se não existir
if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
}

// Copia arquivos necessários
const filesToCopy = [
    { from: 'src/renderer/index.html', to: 'dist/index.html' },
    { from: 'src/renderer/js/app.js', to: 'dist/js/app.js' },
    { from: 'src/renderer/styles/main.css', to: 'dist/styles/main.css' },
    { from: 'assets/splash.html', to: 'dist/assets/splash.html' },
    { from: 'README.txt', to: 'dist/README.txt' },
    { from: 'package.json', to: 'dist/package.json' }
];

// Cria diretórios necessários
const dirsToCreate = [
    'dist/js',
    'dist/styles',
    'dist/assets'
];

dirsToCreate.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Criado diretório: ${dir}`);
    }
});

// Copia arquivos
filesToCopy.forEach(({ from, to }) => {
    try {
        if (fs.existsSync(from)) {
            fs.copyFileSync(from, to);
            console.log(`📄 Copiado: ${from} -> ${to}`);
        } else {
            console.log(`⚠️  Arquivo não encontrado: ${from}`);
        }
    } catch (error) {
        console.error(`❌ Erro ao copiar ${from}:`, error.message);
    }
});

// Cria arquivo main.js simplificado para Electron
const mainJs = `
const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1920,
        height: 1080,
        minWidth: 1280,
        minHeight: 720,
        show: false,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false
        }
    });

    mainWindow.loadFile('index.html');

    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.maximize();
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
`;

fs.writeFileSync('dist/main.js', mainJs);
console.log('📄 Criado: dist/main.js');

// Cria package.json simplificado
const packageJson = {
    "name": "tactical-nexus",
    "version": "1.0.0",
    "description": "O Futuro dos Jogos Táticos",
    "main": "main.js",
    "author": "Tactical Nexus Team",
    "license": "MIT"
};

fs.writeFileSync('dist/package.json', JSON.stringify(packageJson, null, 2));
console.log('📄 Criado: dist/package.json');

console.log('✅ Build concluído com sucesso!');
console.log('📦 Arquivos prontos em: ./dist/');
console.log('🚀 Para testar: cd dist && npm install && npx electron .');
