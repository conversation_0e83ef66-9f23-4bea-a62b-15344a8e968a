{"version": 3, "file": "HitRegistration.js", "sourceRoot": "", "sources": ["../../src/netcode/HitRegistration.ts"], "names": [], "mappings": ";;;AAGA,MAAa,qBAAqB;IAM9B,YAAY,mBAAwC;QAJnC,qBAAgB,GAAG,IAAI,CAAC,CAAC,8CAA8C;QACvE,wBAAmB,GAAG,GAAG,CAAC;QAC1B,oBAAe,GAAG,GAAG,CAAC;QAGnC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACnD,CAAC;IAEM,UAAU,CACb,SAAiB,EACjB,YAAyB,EACzB,aAAsB,EACtB,QAAgB;QAEhB,iDAAiD;QACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAExE,qDAAqD;QACrD,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAClC,YAAY,CAAC,QAAQ,EACrB,aAAa,EACb,gBAAgB,EAChB,YAAY,CAAC,EAAE,CAClB,CAAC;QAEF,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QAEtB,0DAA0D;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAC/B,GAAG,CAAC,WAAW,EACf,GAAG,CAAC,WAAW,EACf,QAAQ,CACX,CAAC;QAEF,OAAO;YACH,SAAS;YACT,SAAS,EAAE,YAAY,CAAC,EAAE;YAC1B,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,MAAM;YACN,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,MAAM,EAAE,QAAQ;SACnB,CAAC;IACN,CAAC;IAEO,qBAAqB,CACzB,MAAe,EACf,SAAkB,EAClB,YAAsC,EACtC,SAAiB;QAEjB,IAAI,UAAU,GAKH,IAAI,CAAC;QAEhB,+BAA+B;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YACzH,IAAI,QAAQ,KAAK,SAAS;gBAAE,SAAS,CAAC,wCAAwC;YAE9E,4CAA4C;YAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAClC,MAAM,EACN,aAAa,EACb,WAAW,CAAC,QAAQ,CACvB,CAAC;YAEF,IAAI,GAAG,IAAI,CAAC,CAAC,UAAU,IAAI,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7D,UAAU,GAAG;oBACT,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,QAAQ;oBACR,WAAW;iBACd,CAAC;YACN,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAE7B,OAAO;YACH,WAAW,EAAE,UAAU,CAAC,QAAQ;YAChC,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,WAAW,EAAE,UAAU,CAAC,WAAW;SACtC,CAAC;IACN,CAAC;IAEO,qBAAqB,CACzB,MAAe,EACf,SAAkB,EAClB,cAAuB;QAEvB,gDAAgD;QAChD,MAAM,OAAO,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,gCAAgC;QACzE,MAAM,MAAM,GAAG;YACX,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC;YACnC,CAAC,EAAE,cAAc,CAAC,CAAC;YACnB,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC;SACtC,CAAC;QACF,MAAM,MAAM,GAAG;YACX,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC;YACnC,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;YAC/B,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC;SACtC,CAAC;QAEF,4BAA4B;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QAEtB,OAAO;YACH,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,QAAQ,EAAE;gBACN,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ;gBACxC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ;gBACxC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ;aAC3C;SACJ,CAAC;IACN,CAAC;IAEO,kBAAkB,CACtB,MAAe,EACf,SAAkB,EAClB,MAAe,EACf,MAAe;QAEf,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAQ,iBAAiB;QAC7C,MAAM,IAAI,GAAsB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC7C,wBAAwB;gBACxB,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC7D,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,4DAA4D;gBAC5D,MAAM,MAAM,GAAG,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;gBAChD,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;gBAEhD,IAAI,EAAE,GAAG,EAAE;oBAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAEjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC1B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAE1B,IAAI,IAAI,GAAG,IAAI;oBAAE,OAAO,IAAI,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAChD,CAAC;IAEO,eAAe,CAAC,CAAU;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,OAAO;YACH,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM;YACf,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM;YACf,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM;SAClB,CAAC;IACN,CAAC;IAEO,eAAe,CACnB,WAAoB,EACpB,WAAwB,EACxB,QAAgB;QAEhB,qDAAqD;QACrD,MAAM,UAAU,GAAG,EAAE,CAAC,CAAC,uBAAuB;QAC9C,IAAI,WAAW,GAAG,UAAU,CAAC;QAE7B,mCAAmC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,UAAU,EAAE,CAAC;YACb,WAAW,IAAI,IAAI,CAAC,mBAAmB,CAAC;QAC5C,CAAC;QAED,iCAAiC;QACjC,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACxB,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QACxC,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAEO,gBAAgB,CAAC,WAAoB,EAAE,cAAuB;QAClE,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,oBAAoB;QACjE,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,2BAA2B;QACtE,OAAO,WAAW,CAAC,CAAC,IAAI,aAAa,IAAI,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC;IAC1E,CAAC;CACJ;AA/LD,sDA+LC"}