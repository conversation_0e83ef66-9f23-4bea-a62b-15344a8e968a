{"version": 3, "file": "OverdrawOptimizer.js", "sourceRoot": "", "sources": ["../../src/rendering/OverdrawOptimizer.ts"], "names": [], "mappings": ";;;AA4BA,MAAa,iBAAiB;IAM1B,YAAY,OAA2B;QAJ/B,sBAAiB,GAAuB,EAAE,CAAC;QAK/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/C,CAAC;IAED,YAAY,CAAC,QAA6C,EAAE,OAAoB;QAC5E,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;IAC/B,CAAC;IAED,mBAAmB,CAAC,MAAwB;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAED,mBAAmB;QACf,wCAAwC;QACxC,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE/E,yEAAyE;QACzE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;YACtE,OAAO,KAAK,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,gFAAgF;QAChF,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;YACtE,OAAO,KAAK,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;YAC/B,sBAAsB,EAAE,kBAAkB,CAAC,MAAM;YACjD,iBAAiB,EAAE,aAAa,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,aAAa,EAAE,GAAG,kBAAkB,CAAC,CAAC;IACrD,CAAC;IAED,iBAAiB;QACb,MAAM,cAAc,GAAuB,EAAE,CAAC;QAC9C,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,sCAAsC;QACtC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;gBACtD,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC;gBACjC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEzB,4BAA4B;gBAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;gBACtD,eAAe,IAAI,WAAW,CAAC;gBAC/B,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACrD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,iBAAiB;QACnD,MAAM,eAAe,GAAG,eAAe,GAAG,YAAY,CAAC;QACvD,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;QAEtE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;YAC/B,eAAe;YACf,eAAe;YACf,WAAW;YACX,eAAe;SAClB,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,kBAAkB;QACpB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,kBAAkB;QAElD,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;YAC/B,IAAI,GAAG,CAAC,eAAe,EAAE,CAAC;gBACtB,aAAa,IAAI,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;QAED,OAAO,aAAa,GAAG,YAAY,CAAC;IACxC,CAAC;IAEO,iBAAiB,CAAC,CAAsC,EAAE,CAAsC;QACpG,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,SAAS,CAAC,GAAqB;QACnC,kBAAkB;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,QAAQ,GAAG,CAAC,MAAM,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,eAAe,CACnB,KAA0C,EAC1C,KAAwE;QAExE,OAAO,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC3B,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YACxB,KAAK,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEO,wBAAwB,CAAC,GAAqB;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3E,OAAO,cAAc,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAClD,CAAC;IAEO,uBAAuB,CAAC,GAAqB;QACjD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;YACvB,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAC7D,CAAC;QACD,MAAM,UAAU,GAAG,GAAG,CAAC,eAAe,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QACvD,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;IAC7D,CAAC;IAEO,uBAAuB,CAAC,GAAqB;QACjD,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,OAAO,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAClC,CAAC;IAEO,6BAA6B,CAAC,GAAqB;QACvD,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;IAC5E,CAAC;IAEO,uBAAuB,CAAC,eAAuB;QACnD,yDAAyD;QACzD,OAAO,eAAe,GAAG,QAAQ,CAAC,CAAC,8BAA8B;IACrE,CAAC;CACJ;AA7JD,8CA6JC"}