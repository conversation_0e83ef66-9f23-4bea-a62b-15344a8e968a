"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HitRegistrationSystem = void 0;
class HitRegistrationSystem {
    constructor(latencyCompensation) {
        this.MAX_HIT_DISTANCE = 8192; // Distância máxima de tiro (unidades do jogo)
        this.HEADSHOT_MULTIPLIER = 4.0;
        this.ARMOR_REDUCTION = 0.5;
        this.latencyCompensation = latencyCompensation;
    }
    processHit(timestamp, shooterState, shotDirection, weaponId) {
        // Rewind o estado do jogo para o momento do tiro
        const historicalStates = this.latencyCompensation.rewindTime(timestamp);
        // Encontra a interseção do tiro com outros jogadores
        const hit = this.raycastAgainstPlayers(shooterState.position, shotDirection, historicalStates, shooterState.id);
        if (!hit)
            return null;
        // Calcula o dano baseado na localização do hit e armadura
        const damage = this.calculateDamage(hit.hitLocation, hit.targetState, weaponId);
        return {
            timestamp,
            shooterId: shooterState.id,
            targetId: hit.targetId,
            damage,
            hitLocation: hit.hitLocation,
            weapon: weaponId
        };
    }
    raycastAgainstPlayers(origin, direction, playerStates, shooterId) {
        let closestHit = null;
        // Normaliza o vetor de direção
        const normalizedDir = this.normalizeVector(direction);
        for (const [targetId, targetState] of playerStates.entries()) {
            if (targetId === shooterId)
                continue; // Ignora colisão com o próprio atirador
            // Realiza hit test contra hitbox do jogador
            const hit = this.intersectPlayerHitbox(origin, normalizedDir, targetState.position);
            if (hit && (!closestHit || hit.distance < closestHit.distance)) {
                closestHit = {
                    distance: hit.distance,
                    location: hit.location,
                    targetId,
                    targetState
                };
            }
        }
        if (!closestHit)
            return null;
        return {
            hitLocation: closestHit.location,
            targetId: closestHit.targetId,
            targetState: closestHit.targetState
        };
    }
    intersectPlayerHitbox(origin, direction, targetPosition) {
        // Simplificado para uma caixa de colisão básica
        const boxSize = { x: 32, y: 72, z: 32 }; // Tamanho da hitbox em unidades
        const boxMin = {
            x: targetPosition.x - boxSize.x / 2,
            y: targetPosition.y,
            z: targetPosition.z - boxSize.z / 2
        };
        const boxMax = {
            x: targetPosition.x + boxSize.x / 2,
            y: targetPosition.y + boxSize.y,
            z: targetPosition.z + boxSize.z / 2
        };
        // Ray-Box intersection test
        const hit = this.rayBoxIntersection(origin, direction, boxMin, boxMax);
        if (!hit)
            return null;
        return {
            distance: hit.distance,
            location: {
                x: origin.x + direction.x * hit.distance,
                y: origin.y + direction.y * hit.distance,
                z: origin.z + direction.z * hit.distance
            }
        };
    }
    rayBoxIntersection(origin, direction, boxMin, boxMax) {
        let tmin = -Infinity;
        let tmax = Infinity; // Para cada eixo
        const axes = ['x', 'y', 'z'];
        for (const axis of axes) {
            if (Math.abs(direction[axis]) < Number.EPSILON) {
                // Raio paralelo ao eixo
                if (origin[axis] < boxMin[axis] || origin[axis] > boxMax[axis]) {
                    return null;
                }
            }
            else {
                // Calcula intersecção com os planos perpendiculares ao eixo
                const invDir = 1.0 / direction[axis];
                let t1 = (boxMin[axis] - origin[axis]) * invDir;
                let t2 = (boxMax[axis] - origin[axis]) * invDir;
                if (t1 > t2)
                    [t1, t2] = [t2, t1];
                tmin = Math.max(tmin, t1);
                tmax = Math.min(tmax, t2);
                if (tmin > tmax)
                    return null;
            }
        }
        return tmin > 0 ? { distance: tmin } : null;
    }
    normalizeVector(v) {
        const length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
        return {
            x: v.x / length,
            y: v.y / length,
            z: v.z / length
        };
    }
    calculateDamage(hitLocation, targetState, weaponId) {
        // TODO: Implementar sistema de dano baseado em armas
        const baseDamage = 25; // Dano base temporário
        let finalDamage = baseDamage;
        // Verifica headshot (simplificado)
        const isHeadshot = this.isHeadshotHeight(hitLocation, targetState.position);
        if (isHeadshot) {
            finalDamage *= this.HEADSHOT_MULTIPLIER;
        }
        // Reduz dano baseado na armadura
        if (targetState.armor > 0) {
            finalDamage *= this.ARMOR_REDUCTION;
        }
        return Math.round(finalDamage);
    }
    isHeadshotHeight(hitLocation, targetPosition) {
        const headZoneStart = targetPosition.y + 60; // Altura do pescoço
        const headZoneEnd = targetPosition.y + 72; // Altura do topo da cabeça
        return hitLocation.y >= headZoneStart && hitLocation.y <= headZoneEnd;
    }
}
exports.HitRegistrationSystem = HitRegistrationSystem;
//# sourceMappingURL=HitRegistration.js.map