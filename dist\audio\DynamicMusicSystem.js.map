{"version": 3, "file": "DynamicMusicSystem.js", "sourceRoot": "", "sources": ["../../src/audio/DynamicMusicSystem.ts"], "names": [], "mappings": ";;;AA2BA,MAAa,kBAAkB;IAU3B,YAAY,WAA4B,EAAE,OAA2B;QAHpD,oBAAe,GAAG,GAAG,CAAC,CAAC,4BAA4B;QACnD,oBAAe,GAAG,GAAG,CAAC,CAAC,2BAA2B;QAG/D,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IAAI,CAAC,YAAY,GAAG;YAChB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,GAAG;YAClB,SAAS,EAAE,KAAK;SACnB,CAAC;QAEF,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,uBAAuB;QAC3B,uBAAuB;QACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE;YACjC,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,yCAAyC;YAC/C,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;YAC/B,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;YACtC,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,IAAI;YACT,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;SACpC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;YACzB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,iCAAiC;YACvC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC;YACzC,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,IAAI;YACT,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;SACpC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;YACxB,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,gCAAgC;YACtC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC;YACjD,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,IAAI;YACT,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;SACpC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;YACzB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,iCAAiC;YACvC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC;YAC5D,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,IAAI;YACT,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;SACpC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE;YAC7B,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,qCAAqC;YAC3C,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,CAAC,QAAQ,CAAC;YAClB,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,WAAW;YAChB,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;SACpC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE;YAC5B,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,oCAAoC;YAC1C,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,CAAC,WAAW,CAAC;YACrB,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,WAAW;YAChB,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;SACnC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe;QACnB,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;IAEM,eAAe,CAAC,QAAoC;QACvD,IAAI,CAAC,YAAY,GAAG;YAChB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,QAAQ;SACd,CAAC;IACN,CAAC;IAEO,WAAW;QACf,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEtD,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1D,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QACjD,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB;QAC3B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnC,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC/B,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,cAA2B;QAC7D,yCAAyC;QACzC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,SAAS,EAAE;oBACxC,UAAU,EAAE,IAAI,CAAC,eAAe;oBAChC,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;oBAC5C,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK;oBACnC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG;iBAClC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAEO,kBAAkB;QACtB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAqB;QAChD,IAAI,UAAU,GAAG,GAAG,CAAC;QAErB,uCAAuC;QACvC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAC5B,CAAC,EACD,IAAI,CAAC,GAAG,CACJ,CAAC,EACD,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;gBACrD,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAClD,CACJ,CAAC;YACF,UAAU,IAAI,eAAe,CAAC;QAClC,CAAC;QAED,+BAA+B;QAC/B,QAAQ,OAAO,CAAC,EAAE,EAAE,CAAC;YACjB,KAAK,aAAa;gBACd,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;gBACtE,MAAM;YACV,KAAK,YAAY;gBACb,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;gBACtE,MAAM;QACd,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,YAAY,CAAC,CAAc,EAAE,CAAc;QAC/C,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,kBAAkB,CAAC,SAAiB;QACvC,IAAI,CAAC,eAAe,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7E,CAAC;IAEM,uBAAuB;QAC1B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAEM,wBAAwB;QAC3B,IAAI,CAAC,eAAe,CAAC;YACjB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,GAAG;SACjB,CAAC,CAAC;IACP,CAAC;CACJ;AAhOD,gDAgOC"}