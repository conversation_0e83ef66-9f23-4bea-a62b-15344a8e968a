{"version": 3, "file": "AIManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/AIManager.ts"], "names": [], "mappings": ";;;AAgCA,MAAa,SAAS;IAWlB,YAAY,UAAsB;QAL1B,SAAI,GAAuB,IAAI,GAAG,EAAE,CAAC;QACrC,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QAK3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAClD,CAAC;IAEO,oBAAoB;QACxB,MAAM,KAAK,GAAG,SAAS,CAAC,mBAAmB,IAAI,CAAC,CAAC;QACjD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,8CAA8C;YAChF,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,MAAM,CAAC,SAAiB;QAC3B,+CAA+C;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACzE,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;iBAC3C,KAAK,CAAC,eAAe,EAAE,eAAe,GAAG,aAAa,CAAC,CAAC;YAE7D,MAAM,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,SAAS;aACZ,CAAC,CAAC;YAEH,eAAe,IAAI,aAAa,CAAC;QACrC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,KAAa,EAAE,QAAiB,EAAE,OAAgB;QAC5D,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAE1B,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,KAAa;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,GAAG,EAAE,CAAC;YACN,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,KAAa,EAAE,OAAe;QACnD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG;gBACJ,EAAE,EAAE,OAAO;gBACX,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,QAAQ;gBACnB,SAAS,EAAE;oBACP,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;oBAC9B,QAAQ,EAAE,CAAC;iBACd;aACJ,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;QACzB,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,KAAa;QACjC,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrD,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAChC,CAAC;gBACD,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,WAAoB,EAAE,cAAuB;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1D,IAAI,QAAQ,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,CAAC;YACb,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC;IAC9C,CAAC;IAEO,iBAAiB,CAAC,IAAa,EAAE,IAAa;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAEM,yBAAyB,CAAC,OAAe,EAAE,KAAa;QAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAExB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,WAAW,KAAK,CAAC,CAAC;YAAE,OAAO,IAAI,CAAC;QAEpC,mDAAmD;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC3E,OAAO;YACH,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC/B,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;SAClC,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,SAA6B,EAAE,KAAa;QACzE,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,uBAAuB;QAE1C,QAAQ,SAAS,EAAE,CAAC;YAChB,KAAK,QAAQ;gBACT,OAAO;oBACH,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,OAAO;oBAC5C,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,OAAO;iBAC/C,CAAC;YACN,KAAK,MAAM;gBACP,OAAO;oBACH,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO;oBACxB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;iBACP,CAAC;YACN,KAAK,OAAO;gBACR,OAAO;oBACH,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO;oBACxB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO;iBACrC,CAAC;YACN;gBACI,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;;AAtKL,8BAuKC;AAtK2B,qBAAW,GAAG,EAAE,AAAL,CAAM,CAAC,sBAAsB;AACxC,iCAAuB,GAAG,GAAG,AAAN,CAAO,CAAC,SAAS;AACxC,4BAAkB,GAAG,GAAG,AAAN,CAAO,CAAC,SAAS;AACnC,2BAAiB,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,AAAjB,CAAkB,CAAC,SAAS"}