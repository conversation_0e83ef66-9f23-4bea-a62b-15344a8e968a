{"version": 3, "file": "WeaponSFX.js", "sourceRoot": "", "sources": ["../../src/audio/WeaponSFX.ts"], "names": [], "mappings": ";;;AAAA,iDAA8C;AAuB9C,MAAa,SAAS;IAIlB,YAAY,OAA2B;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,6BAA6B;QAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE;YACnC,IAAI,EAAE;gBACF,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,EAAE;aACb;YACD,MAAM,EAAE;gBACJ,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,SAAS;oBACT,YAAY;oBACZ,QAAQ;oBACR,WAAW;iBACd;aACJ;YACD,QAAQ,EAAE;gBACN,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;aACd;SACJ,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE;YACjC,IAAI,EAAE;gBACF,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,EAAE;aACb;YACD,MAAM,EAAE;gBACJ,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE;oBACN,WAAW;oBACX,SAAS;oBACT,QAAQ;oBACR,cAAc;iBACjB;aACJ;YACD,QAAQ,EAAE;gBACN,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,IAAI;aACf;SACJ,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE;YACnC,IAAI,EAAE;gBACF,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,EAAE;aACb;YACD,MAAM,EAAE;gBACJ,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,SAAS;oBACT,QAAQ;oBACR,eAAe;iBAClB;aACJ;YACD,QAAQ,EAAE;gBACN,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG;aACd;SACJ,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,uBAAuB;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC;QAED,yBAAyB;QACzB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAEM,cAAc,CACjB,QAAgB,EAChB,QAA6C;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE;YAChD,QAAQ;YACR,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;YAC3B,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ;YAClC,WAAW,EAAE,GAAG;SACnB,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC3B,QAAgB,EAChB,QAA6C;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE;gBAChD,QAAQ;gBACR,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,GAAG;gBAChB,WAAW,EAAE,GAAG;aACnB,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAEM,kBAAkB,CACrB,QAAgB,EAChB,MAA4B,EAC5B,QAA6C;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE;YAChD,QAAQ;YACR,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,GAAG;YAChB,WAAW,EAAE,GAAG;SACnB,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,gBAAgB;QACzB,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAExD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEM,eAAe,CAClB,OAAe,EACf,QAA6C;QAE7C,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE;YACvC,QAAQ;YACR,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,GAAG;YAChB,WAAW,EAAE,GAAG;YAChB,aAAa,EAAE,CAAC;SACnB,CAAC,CAAC;IACP,CAAC;IAEM,sBAAsB,CACzB,QAA6C,EAC7C,WAAgD;QAEhD,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACpE,CAAC;CACJ;AA7LD,8BA6LC"}