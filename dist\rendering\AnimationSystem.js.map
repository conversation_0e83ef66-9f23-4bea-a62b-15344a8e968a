{"version": 3, "file": "AnimationSystem.js", "sourceRoot": "", "sources": ["../../src/rendering/AnimationSystem.ts"], "names": [], "mappings": ";;;AAkBA,MAAa,eAAe;IAKxB;QAHQ,cAAS,GAAW,CAAC,CAAC;QACb,mBAAc,GAAG,IAAI,CAAC,CAAC,yBAAyB;QAG7D,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAEO,uBAAuB;QAC3B,qCAAqC;QACrC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAE9B,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAEhC,mDAAmD;QACnD,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAElC,uDAAuD;QACvD,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAEM,WAAW,CAAC,IAAY,EAAE,MAAc;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,MAAM,EAAE,IAAI,GAAG,EAAE;YACjB,YAAY,EAAE,EAAE;SACnB,CAAC,CAAC;IACP,CAAC;IAEM,YAAY,CAAC,SAAiB,EAAE,SAAiB,EAAE,SAAoB;QAC1E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE;YACxB,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,CAAC;YACd,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,oDAAoD;QACpD,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,aAAa,CAAC,SAAiB,EAAE,SAAiB,EAAE,gBAAwB,IAAI,CAAC,cAAc;QAClG,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,YAAY,EAAE,CAAC;YACf,gDAAgD;YAChD,YAAY,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;YACtC,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC;YAE7B,sCAAsC;YACtC,UAAU,CAAC,GAAG,EAAE;gBACZ,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;YACnC,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,CAAC;QAC7B,CAAC;aAAM,CAAC;YACJ,wCAAwC;YACxC,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,SAAiB;QAC3B,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC;QAE5B,uBAAuB;QACvB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,CAAC,KAAK;gBAAE,SAAS;YAErB,6BAA6B;YAC7B,KAAK,CAAC,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;YACtC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACb,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzC,CAAC;YAED,+BAA+B;YAC/B,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBACd,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3F,CAAC;QACL,CAAC;IACL,CAAC;IAEM,iBAAiB,CAAC,SAAiB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK;YAAE,OAAO;gBACf,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAC9B,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;aACvC,CAAC;QAEF,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK;YAAE,OAAO;gBACf,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAC9B,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;aACvC,CAAC;QAEF,0CAA0C;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAE3E,uDAAuD;QACvD,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,cAAc,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEO,iBAAiB,CAAC,SAAoB,EAAE,IAAY;QACxD,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAE1C,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC5D,MAAM,cAAc,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;YAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAEzF,oBAAoB;YACpB,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAC9B,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,EAC7B,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,EACjC,WAAW,CACd,CAAC;YACN,CAAC;YAED,oBAAoB;YACpB,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CACjC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,EAC7B,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,EACjC,WAAW,CACd,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;IAClC,CAAC;IAEO,cAAc,CAAC,KAAe,EAAE,IAAY;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEO,cAAc,CAAC,KAAe,EAAE,IAAY,EAAE,UAAkB,EAAE,cAAsB;QAC5F,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,aAAa,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC;IACtD,CAAC;IAEO,kBAAkB,CAAC,CAAU,EAAE,CAAU,EAAE,MAAc;QAC7D,OAAO;YACH,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;YAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;YAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;SAChC,CAAC;IACN,CAAC;IAEO,qBAAqB,CAAC,CAAa,EAAE,CAAa,EAAE,MAAc;QACtE,qDAAqD;QACrD,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEpD,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC;QACrD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC;QAE/C,OAAO;YACH,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;SAC1C,CAAC;IACN,CAAC;IAEO,qBAAqB,CACzB,CAA8C,EAC9C,CAA8C,EAC9C,MAAc;QAEd,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;YACjE,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;SACvE,CAAC;IACN,CAAC;CACJ;AA1MD,0CA0MC"}