"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const pako_1 = require("pako");
class ReplayWorker {
    constructor() {
        this.buffer = [];
        this.processedData = null;
        this.metadata = null;
    }
    handleMessage(event) {
        const { type, data } = event.data;
        switch (type) {
            case 'start':
                this.handleStart(data.metadata);
                break;
            case 'compress':
                this.handleCompress(data.frames);
                break;
            case 'finish':
                this.handleFinish(data);
                break;
            case 'load':
                this.handleLoad(data.replayData);
                break;
            case 'seek':
                this.handleSeek(data.tick);
                break;
        }
    }
    handleStart(metadata) {
        this.metadata = metadata;
        this.buffer = [];
        this.processedData = null;
    }
    handleCompress(frames) {
        try {
            // Processa frames em chunks para evitar bloqueio do worker
            const chunkSize = 100;
            for (let i = 0; i < frames.length; i += chunkSize) {
                const chunk = frames.slice(i, i + chunkSize);
                this.processChunk(chunk);
                // Reporta progresso
                self.postMessage({
                    type: 'progress',
                    progress: (i / frames.length) * 100
                });
            }
            this.buffer.push(...frames);
        }
        catch (error) {
            self.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }
    handleFinish(data) {
        try {
            // Finaliza o buffer com os últimos frames
            this.buffer.push(...data.buffer);
            // Prepara dados para compressão
            const replayData = {
                metadata: data.metadata,
                frames: this.buffer,
                events: data.events
            };
            // Comprime dados
            const compressed = this.compressReplayData(replayData);
            // Envia dados comprimidos
            self.postMessage({
                type: 'complete',
                replayData: compressed
            });
            // Limpa buffer
            this.buffer = [];
            this.processedData = null;
        }
        catch (error) {
            self.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }
    handleLoad(replayData) {
        try {
            // Descomprime dados
            const decompressed = this.decompressReplayData(replayData);
            // Armazena dados descomprimidos
            this.metadata = decompressed.metadata;
            this.buffer = decompressed.frames;
            // Notifica carregamento completo
            self.postMessage({
                type: 'loaded',
                metadata: this.metadata
            });
        }
        catch (error) {
            self.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }
    handleSeek(tick) {
        if (!this.buffer.length) {
            self.postMessage({
                type: 'error',
                error: 'No replay data loaded'
            });
            return;
        }
        // Encontra o frame mais próximo do tick solicitado
        const frame = this.findNearestFrame(tick);
        if (frame) {
            self.postMessage({
                type: 'frame',
                frame
            });
        }
    }
    processChunk(frames) {
        // Otimiza frames para armazenamento
        frames.forEach(frame => {
            // Remove dados redundantes
            this.optimizeFrame(frame);
        });
    }
    optimizeFrame(frame) {
        // Remove dados que podem ser interpolados
        frame.playerStates.forEach(state => {
            // Arredonda posições para reduzir tamanho
            state.position.x = Math.round(state.position.x * 100) / 100;
            state.position.y = Math.round(state.position.y * 100) / 100;
            state.position.z = Math.round(state.position.z * 100) / 100;
        });
    }
    compressReplayData(data) {
        // Serializa dados
        const serialized = JSON.stringify(data);
        // Comprime usando pako (zlib)
        return (0, pako_1.deflate)(serialized, { level: 9 });
    }
    decompressReplayData(data) {
        // Descomprime dados
        const decompressed = (0, pako_1.inflate)(new Uint8Array(data), { to: 'string' });
        // Deserializa
        return JSON.parse(decompressed);
    }
    findNearestFrame(tick) {
        return this.buffer.find(frame => frame.tick === tick) || null;
    }
}
// Inicializa worker
const worker = new ReplayWorker();
self.onmessage = (e) => worker.handleMessage(e);
//# sourceMappingURL=ReplayWorker.js.map