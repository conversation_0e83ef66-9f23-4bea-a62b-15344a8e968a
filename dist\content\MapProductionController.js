"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapProductionController = void 0;
const InitialMapProducer_1 = require("../gameplay/InitialMapProducer");
const PerformanceMetrics_1 = require("../rendering/PerformanceMetrics");
class MapProductionController {
    constructor() {
        this.metrics = new PerformanceMetrics_1.PerformanceMetrics();
        this.status = {
            phase: 'Inicializando',
            progress: 0,
            messages: [],
            metrics: {}
        };
        // Configura o InitialMapProducer com callback de progresso
        this.mapProducer = new InitialMapProducer_1.InitialMapProducer({
            audio: {
                sampleRate: 48000,
                channels: 2,
                format: 'ogg'
            }
        }, this.onProgressUpdate.bind(this));
    }
    async startInitialProduction() {
        try {
            this.logStatus('Iniciando produção do mapa base e POIs iniciais');
            // Inicia monitoramento de performance
            this.metrics.startMonitoring();
            // Inicia produção do mapa
            const result = await this.mapProducer.produceInitialMap();
            if (!result.success) {
                throw new Error(`Falha na produção do mapa: ${result.error}`);
            }
            // Valida as métricas finais
            const finalMetrics = result.metrics;
            if (finalMetrics.averageFPS < 999) {
                this.logStatus(`AVISO: FPS abaixo da meta (${finalMetrics.averageFPS}/999)`);
                await this.triggerOptimizationPass();
            }
            this.logStatus('Produção inicial do mapa concluída com sucesso');
            return true;
        }
        catch (error) {
            this.logStatus(`ERRO: ${error.message}`);
            return false;
        }
    }
    async triggerOptimizationPass() {
        this.logStatus('Iniciando passe de otimização adicional');
        // Identifica áreas problemáticas
        const metrics = this.metrics.getMapProductionStats();
        const issues = this.analyzePerformanceIssues(metrics);
        for (const issue of issues) {
            this.logStatus(`Otimizando: ${issue.description}`);
            await this.optimizeIssue(issue);
        }
    }
    analyzePerformanceIssues(metrics) {
        const issues = [];
        if (metrics.drawCalls > 2000) {
            issues.push({
                type: 'drawCalls',
                severity: 'high',
                description: 'Número excessivo de draw calls',
                value: metrics.drawCalls,
                target: 2000
            });
        }
        if (metrics.textureMemory > 2048 * 1024 * 1024) { // 2GB
            issues.push({
                type: 'textureMemory',
                severity: 'high',
                description: 'Uso excessivo de memória de textura',
                value: metrics.textureMemory,
                target: 2048 * 1024 * 1024
            });
        }
        if (metrics.triangleCount > 5000000) { // 5M triângulos
            issues.push({
                type: 'geometry',
                severity: 'medium',
                description: 'Contagem de triângulos muito alta',
                value: metrics.triangleCount,
                target: 5000000
            });
        }
        return issues;
    }
    async optimizeIssue(issue) {
        switch (issue.type) {
            case 'drawCalls':
                await this.optimizeDrawCalls(issue);
                break;
            case 'textureMemory':
                await this.optimizeTextures(issue);
                break;
            case 'geometry':
                await this.optimizeGeometry(issue);
                break;
        }
    }
    async optimizeDrawCalls(issue) {
        this.logStatus('Aplicando otimizações de draw calls:');
        this.logStatus('- Aumentando instanciamento de objetos similares');
        this.logStatus('- Combinando meshes estáticas');
        this.logStatus('- Ajustando distâncias de LOD');
        // Implementação real dependeria de chamadas ao AssetProductionManager
    }
    async optimizeTextures(issue) {
        this.logStatus('Aplicando otimizações de textura:');
        this.logStatus('- Reduzindo resolução de texturas distantes');
        this.logStatus('- Aumentando compressão de texturas');
        this.logStatus('- Combinando texturas em atlas');
        // Implementação real dependeria de chamadas ao TextureCompressor
    }
    async optimizeGeometry(issue) {
        this.logStatus('Aplicando otimizações de geometria:');
        this.logStatus('- Aumentando decimação de meshes');
        this.logStatus('- Ajustando níveis de LOD');
        this.logStatus('- Otimizando occlusion culling');
        // Implementação real dependeria de chamadas ao GeometryOptimizer
    }
    onProgressUpdate(progress) {
        this.status.progress = progress.terrainProgress;
        this.status.phase = progress.currentPhase;
        this.status.metrics = progress.performanceMetrics;
        // Log do progresso atual
        this.logStatus(`${progress.currentPhase}: ${progress.terrainProgress}%`);
        if (progress.poisCompleted >= 0) {
            this.logStatus(`POIs completados: ${progress.poisCompleted}/${progress.totalPOIs}`);
        }
        if (progress.performanceMetrics) {
            this.logStatus(`FPS atual: ${progress.performanceMetrics.averageFPS}`);
            this.logStatus(`Draw Calls: ${progress.performanceMetrics.drawCalls}`);
            this.logStatus(`Uso de VRAM: ${(progress.performanceMetrics.vramUsage / 1024 / 1024).toFixed(2)}MB`);
        }
    }
    logStatus(message) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}`;
        this.status.messages.push(logMessage);
        console.log(logMessage);
    }
    getStatus() {
        return { ...this.status };
    }
    getCurrentMetrics() {
        return this.metrics.getMapProductionStats();
    }
}
exports.MapProductionController = MapProductionController;
//# sourceMappingURL=MapProductionController.js.map