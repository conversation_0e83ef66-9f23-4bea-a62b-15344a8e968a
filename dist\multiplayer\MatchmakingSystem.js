"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MatchmakingSystem = void 0;
const events_1 = require("events");
class MatchmakingSystem extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.searchingPlayers = new Map();
        this.activeMatches = new Map();
        this.searchIntervals = new Map();
        this.config = {
            maxSearchTime: 120000, // 2 minutos
            skillRangeExpansion: 100, // Expande range a cada 30s
            pingThreshold: 100, // ms
            regionPriority: true,
            balanceTeams: true,
            ...config
        };
        console.log('🔍 MatchmakingSystem inicializado');
        this.startMatchmakingLoop();
    }
    async searchForMatch(player) {
        console.log(`🔍 Iniciando busca por partida para ${player.username}`);
        // Adiciona jogador à fila de busca
        this.searchingPlayers.set(player.id, player);
        // Emite evento de início de busca
        this.emit('searchStarted', { playerId: player.id, player });
        // Configura timeout para expansão de critérios
        const searchInterval = setInterval(() => {
            this.expandSearchCriteria(player.id);
        }, 30000); // Expande critérios a cada 30s
        this.searchIntervals.set(player.id, searchInterval);
        // Timeout máximo de busca
        setTimeout(() => {
            if (this.searchingPlayers.has(player.id)) {
                this.cancelSearch(player.id);
                this.emit('searchTimeout', { playerId: player.id });
            }
        }, this.config.maxSearchTime);
    }
    cancelSearch(playerId) {
        const player = this.searchingPlayers.get(playerId);
        if (player) {
            console.log(`❌ Cancelando busca para ${player.username}`);
            this.searchingPlayers.delete(playerId);
            const interval = this.searchIntervals.get(playerId);
            if (interval) {
                clearInterval(interval);
                this.searchIntervals.delete(playerId);
            }
            this.emit('searchCancelled', { playerId, player });
        }
    }
    startMatchmakingLoop() {
        setInterval(() => {
            this.processMatchmaking();
        }, 5000); // Processa a cada 5 segundos
    }
    processMatchmaking() {
        if (this.searchingPlayers.size < 2)
            return;
        const players = Array.from(this.searchingPlayers.values());
        const gameModes = this.groupPlayersByGameMode(players);
        for (const [gameMode, modePlayers] of gameModes) {
            if (modePlayers.length >= this.getMinPlayersForMode(gameMode)) {
                this.createMatch(gameMode, modePlayers);
            }
        }
    }
    groupPlayersByGameMode(players) {
        const groups = new Map();
        for (const player of players) {
            const mode = player.preferences.gameMode;
            if (!groups.has(mode)) {
                groups.set(mode, []);
            }
            groups.get(mode).push(player);
        }
        return groups;
    }
    getMinPlayersForMode(gameMode) {
        const modeConfig = {
            'team_deathmatch': 8,
            'battle_royale': 50,
            'capture_flag': 12,
            'domination': 16,
            'search_destroy': 10
        };
        return modeConfig[gameMode] || 8;
    }
    createMatch(gameMode, availablePlayers) {
        const maxPlayers = this.getMaxPlayersForMode(gameMode);
        const selectedPlayers = this.selectBestPlayers(availablePlayers, maxPlayers);
        if (selectedPlayers.length < this.getMinPlayersForMode(gameMode)) {
            return;
        }
        const match = {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            gameMode,
            map: this.selectMap(gameMode, selectedPlayers),
            players: selectedPlayers,
            maxPlayers,
            region: this.determineRegion(selectedPlayers),
            averagePing: this.calculateAveragePing(selectedPlayers),
            skillRange: this.calculateSkillRange(selectedPlayers),
            status: 'waiting',
            createdAt: new Date(),
            estimatedStartTime: new Date(Date.now() + 30000) // 30s para preparação
        };
        // Remove jogadores da fila de busca
        for (const player of selectedPlayers) {
            this.searchingPlayers.delete(player.id);
            const interval = this.searchIntervals.get(player.id);
            if (interval) {
                clearInterval(interval);
                this.searchIntervals.delete(player.id);
            }
        }
        this.activeMatches.set(match.id, match);
        console.log(`🎮 Partida criada: ${match.id} (${gameMode}, ${selectedPlayers.length} jogadores)`);
        // Emite evento de partida encontrada
        this.emit('matchFound', { match, players: selectedPlayers });
        // Inicia a partida após preparação
        setTimeout(() => {
            this.startMatch(match.id);
        }, 30000);
    }
    selectBestPlayers(players, maxPlayers) {
        // Ordena por skill rating e ping para melhor balanceamento
        const sorted = players.sort((a, b) => {
            const skillDiff = Math.abs(a.skillRating - b.skillRating);
            const pingDiff = Math.abs(a.ping - b.ping);
            return skillDiff + pingDiff * 0.1; // Prioriza skill, mas considera ping
        });
        return sorted.slice(0, maxPlayers);
    }
    getMaxPlayersForMode(gameMode) {
        const modeConfig = {
            'team_deathmatch': 16,
            'battle_royale': 100,
            'capture_flag': 24,
            'domination': 32,
            'search_destroy': 20
        };
        return modeConfig[gameMode] || 16;
    }
    selectMap(gameMode, players) {
        const maps = {
            'team_deathmatch': ['Urban District Alpha', 'Military Base Alpha', 'Industrial Zone Alpha'],
            'battle_royale': ['Full Map 2km x 2km'],
            'capture_flag': ['Urban District Beta', 'Military Base Beta'],
            'domination': ['Industrial Zone Beta', 'Forest Outpost Alpha'],
            'search_destroy': ['Urban District Gamma', 'Forest Outpost Beta']
        };
        const availableMaps = maps[gameMode] || maps.team_deathmatch;
        return availableMaps[Math.floor(Math.random() * availableMaps.length)];
    }
    determineRegion(players) {
        const regionCounts = new Map();
        for (const player of players) {
            const count = regionCounts.get(player.region) || 0;
            regionCounts.set(player.region, count + 1);
        }
        let maxRegion = 'US-East';
        let maxCount = 0;
        for (const [region, count] of regionCounts) {
            if (count > maxCount) {
                maxCount = count;
                maxRegion = region;
            }
        }
        return maxRegion;
    }
    calculateAveragePing(players) {
        const totalPing = players.reduce((sum, player) => sum + player.ping, 0);
        return Math.round(totalPing / players.length);
    }
    calculateSkillRange(players) {
        const skills = players.map(p => p.skillRating);
        return {
            min: Math.min(...skills),
            max: Math.max(...skills)
        };
    }
    expandSearchCriteria(playerId) {
        const player = this.searchingPlayers.get(playerId);
        if (player) {
            // Expande critérios de busca (aumenta ping máximo, flexibiliza skill range)
            player.preferences.maxPing = Math.min(player.preferences.maxPing + 20, 200);
            console.log(`🔍 Expandindo critérios de busca para ${player.username} (ping máximo: ${player.preferences.maxPing}ms)`);
        }
    }
    startMatch(matchId) {
        const match = this.activeMatches.get(matchId);
        if (match && match.status === 'waiting') {
            match.status = 'starting';
            console.log(`🚀 Iniciando partida: ${matchId}`);
            this.emit('matchStarting', { match });
            // Simula início da partida
            setTimeout(() => {
                if (this.activeMatches.has(matchId)) {
                    match.status = 'in_progress';
                    this.emit('matchStarted', { match });
                    console.log(`🎮 Partida em andamento: ${matchId}`);
                }
            }, 10000); // 10s para carregamento
        }
    }
    getActiveMatches() {
        return Array.from(this.activeMatches.values());
    }
    getSearchingPlayersCount() {
        return this.searchingPlayers.size;
    }
    getMatchmakingStats() {
        return {
            searchingPlayers: this.searchingPlayers.size,
            activeMatches: this.activeMatches.size,
            totalPlayersInGame: Array.from(this.activeMatches.values())
                .reduce((total, match) => total + match.players.length, 0)
        };
    }
}
exports.MatchmakingSystem = MatchmakingSystem;
exports.default = MatchmakingSystem;
//# sourceMappingURL=MatchmakingSystem.js.map