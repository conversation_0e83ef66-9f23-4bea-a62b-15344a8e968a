{"version": 3, "file": "UIWorker.js", "sourceRoot": "", "sources": ["../../src/rendering/UIWorker.ts"], "names": [], "mappings": ";;AAQA,MAAM,QAAQ;IAKV;QACI,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAsC,CAAC;QACtF,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;IAClC,CAAC;IAEM,aAAa,CAAC,KAAmB;QACpC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QAE3C,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,aAAa;gBACd,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC1B,MAAM;YACV,KAAK,eAAe;gBAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtC,MAAM;QACd,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAkB;QACxC,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElF,uBAAuB;QACvB,QAAQ,OAAO,CAAC,EAAE,EAAE,CAAC;YACjB,KAAK,WAAW;gBACZ,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBACpC,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACtC,MAAM;YACV,KAAK,eAAe;gBAChB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBACxC,MAAM;QACd,CAAC;QAED,yCAAyC;QACzC,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAE1C,8BAA8B;QAC9B,IAAI,CAAC,WAAW,CAAC;YACb,IAAI,EAAE,eAAe;YACrB,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,KAAK,EAAE,MAAM;SAChB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,IAAuB;QAClE,yBAAyB;QACzB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElF,oCAAoC;QACpC,QAAQ,SAAS,EAAE,CAAC;YAChB,KAAK,WAAW;gBACZ,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,iBAAiB;gBAClB,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM;QACd,CAAC;QAED,oCAAoC;QACpC,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEzC,8BAA8B;QAC9B,IAAI,CAAC,WAAW,CAAC;YACb,IAAI,EAAE,eAAe;YACrB,SAAS;YACT,KAAK,EAAE,MAAM;SAChB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAkB;QAC5C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC;QAEvB,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAElB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAkB;QAC9C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,yBAAyB;QACzB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;QAE7B,qBAAqB;QACrB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAkB;QAChD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,4BAA4B;QAC5B,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAElB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,IAAS;QACnC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC;QAEvB,oBAAoB;QACpB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAElB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,IAAS;QACzC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;QAEjC,4BAA4B;QAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACpE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAS;QACjC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,4BAA4B;QAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,gCAAgC;QAChC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,mBAAmB,CAAC,IAAS;QACjC,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,mBAAmB;QACnB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACnG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAS;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE9B,qBAAqB;QACrB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEhB,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAEzC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEO,eAAe,CAAC,UAAoB;QACxC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,sBAAsB;AACtB,MAAM,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AAC9B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC"}