{"version": 3, "file": "GameAudioSystem.js", "sourceRoot": "", "sources": ["../../src/audio/GameAudioSystem.ts"], "names": [], "mappings": ";;;AAiCA,MAAa,eAAe;IAQxB,YAAY,YAA0B,EAAE,OAA2B;QAL3D,eAAU,GAA2B,IAAI,GAAG,EAAE,CAAC;QAC/C,cAAS,GAA+B,IAAI,GAAG,EAAE,CAAC;QAClD,gBAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;QACrC,0BAAqB,GAAwC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAGtF,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEO,0BAA0B;QAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,EAAE,EAAE,UAAU;YACd,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE;YACxB,EAAE,EAAE,OAAO;YACX,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE;YACvB,EAAE,EAAE,MAAM;YACV,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;SACpB,CAAC,CAAC;IACP,CAAC;IAED,eAAe,CAAC,MAAiB;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED,oBAAoB,CAAC,QAA6C;QAC9D,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,iBAAiB;QACrB,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;QAEzC,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC;QAClC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc,CAAC,IAAe;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,OAAO,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9C,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9C,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEO,YAAY,CAAC,MAAc;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnD,wBAAwB;QACxB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;gBACxB,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;oBACtB,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC9C,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC9C,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;iBACjD,CAAC,CAAC,CAAC,SAAS;aAChB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,MAAc;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,8BAA8B;QAC9B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEO,cAAc;QAClB,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE1C,+CAA+C;QAC/C,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAC5D,+DAA+D;YAC/D,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAED,qDAAqD;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAiB;QAC1C,MAAM,OAAO,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;YAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC;YACtC,OAAO,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,uBAAuB,CAAC,IAAe;QAC3C,MAAM,MAAM,GAAG;YACX,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC9C,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC9C,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;SACjD,CAAC;QAEF,MAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAEnD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,eAAe,CAAC,OAAiB;QACrC,IAAI,WAAkC,CAAC;QACvC,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;gBACzB,WAAW,GAAG,QAAQ,CAAC;gBACvB,WAAW,GAAG,IAAI,CAAC;YACvB,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,gDAAgD;IAChD,qBAAqB,CAAC,EAAU,EAAE,IAAkC;QAChE,MAAM,OAAO,GAAG;YACZ,KAAK,EAAE;gBACH,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,GAAG;gBACpB,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,GAAG;aACf;YACD,MAAM,EAAE;gBACJ,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,GAAG;gBACpB,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,GAAG;aACf;YACD,KAAK,EAAE;gBACH,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,GAAG;gBACpB,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,GAAG;aACf;SACJ,CAAC;QAEF,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAMtB;QACG,mCAAmC;QACnC,MAAM,MAAM,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEvC,MAAM,SAAS,GAAc;YACzB,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE;gBACJ,GAAG,EAAE;oBACD,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;oBAC5C,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;oBAC7C,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;iBAChD;gBACD,GAAG,EAAE;oBACD,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;oBAC5C,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;oBAC7C,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;iBAChD;aACJ;YACD,MAAM,EAAE;gBACJ,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,GAAG;gBACpB,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,GAAG;aACf;YACD,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACvC,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI;aAChB,CAAC,CAAC;SACN,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;QAEzD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAO1B;QACG,mDAAmD;QACnD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;CACJ;AA7QD,0CA6QC"}