"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReplayAnalyzer = void 0;
const worker_threads_1 = require("worker_threads");
class ReplayAnalyzer {
    constructor(replayManager, mlModel, workerCount = 4) {
        this.replayManager = replayManager;
        this.mlModel = mlModel;
        this.analysisQueue = [];
        this.initializeWorkerPool(workerCount);
    }
    initializeWorkerPool(count) {
        this.workerPool = Array(count).fill(0).map(() => new worker_threads_1.Worker('./ReplayAnalysisWorker.js', {
            workerData: {
                mlModelPath: this.mlModel.getPath()
            }
        }));
    }
    async queueReplayForAnalysis(replayId, priority, reason) {
        this.analysisQueue.push({ replayId, priority, reason });
        this.analysisQueue.sort((a, b) => b.priority - a.priority);
        // Inicia análise se houver workers disponíveis
        await this.processQueue();
    }
    async processQueue() {
        while (this.analysisQueue.length > 0) {
            const availableWorker = this.workerPool.find(w => !w.isProcessing);
            if (!availableWorker)
                break;
            const task = this.analysisQueue.shift();
            if (!task)
                break;
            await this.analyzeReplay(task.replayId, availableWorker);
        }
    }
    async analyzeReplay(replayId, worker) {
        const replay = await this.replayManager.loadReplay(replayId);
        const chunks = this.splitReplayIntoChunks(replay);
        const suspiciousEvents = [];
        for (const chunk of chunks) {
            const chunkEvents = await this.analyzeChunk(chunk, worker);
            suspiciousEvents.push(...chunkEvents);
        }
        if (suspiciousEvents.length > 0) {
            await this.generateEvidence(replayId, suspiciousEvents);
        }
    }
    splitReplayIntoChunks(replay) {
        const chunks = [];
        const totalDuration = replay.endTime - replay.startTime;
        let currentTime = replay.startTime;
        while (currentTime < replay.endTime) {
            const chunkEnd = Math.min(currentTime + ReplayAnalyzer.ANALYSIS_CHUNK_SIZE, replay.endTime);
            chunks.push(this.extractChunk(replay, currentTime, chunkEnd));
            currentTime = chunkEnd;
        }
        return chunks;
    }
    async analyzeChunk(chunk, worker) {
        return new Promise((resolve, reject) => {
            worker.postMessage({
                type: 'analyzeChunk',
                data: chunk
            });
            worker.once('message', (events) => {
                resolve(events);
            });
            worker.once('error', reject);
        });
    }
    async generateEvidence(replayId, events) {
        const highlightClips = await this.createHighlightClips(replayId, events);
        for (const event of events) {
            if (event.confidence >= ReplayAnalyzer.HIGH_PRIORITY_THRESHOLD) {
                await this.notifyAntiCheatSystem(event, highlightClips);
            }
        }
    }
    async createHighlightClips(replayId, events) {
        const clips = new Map();
        for (const event of events) {
            const clipStart = Math.max(0, event.timestamp - 5000); // 5 segundos antes
            const clipEnd = event.timestamp + 5000; // 5 segundos depois
            const clip = await this.replayManager.extractClip(replayId, clipStart, clipEnd, event.playerId);
            clips.set(event.timestamp, clip);
        }
        return clips;
    }
    async notifyAntiCheatSystem(event, clips) {
        const evidence = {
            event,
            clip: clips.get(event.timestamp),
            metadata: {
                detectedBy: 'replay_analysis',
                timestamp: new Date().toISOString(),
                confidence: event.confidence
            }
        };
        // Notifica o sistema anti-cheat principal
        await this.mlModel.updateEvidence(event.playerId, evidence);
    }
}
exports.ReplayAnalyzer = ReplayAnalyzer;
ReplayAnalyzer.ANALYSIS_CHUNK_SIZE = 30000; // 30 segundos
ReplayAnalyzer.HIGH_PRIORITY_THRESHOLD = 0.7;
//# sourceMappingURL=ReplayAnalyzer.js.map