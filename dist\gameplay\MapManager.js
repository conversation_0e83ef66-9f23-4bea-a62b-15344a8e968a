"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapManager = void 0;
const MapLayoutManager_1 = require("./MapLayoutManager");
const POIGenerator_1 = require("./POIGenerator");
const AssetStreamingManager_1 = require("../rendering/AssetStreamingManager");
const GameAudioSystem_1 = require("../audio/GameAudioSystem");
class MapManager {
    constructor(audioConfig) {
        this.layoutManager = new MapLayoutManager_1.MapLayoutManager();
        this.poiGenerator = new POIGenerator_1.POIGenerator();
        this.streamingManager = new AssetStreamingManager_1.AssetStreamingManager();
        this.audioSystem = new GameAudioSystem_1.GameAudioSystem(audioConfig);
        this.regions = [];
        this.pois = new Map();
        this.initialize();
    }
    async initialize() {
        // Inicializa o layout base do mapa
        const mapData = this.layoutManager.exportMapData();
        // Inicializa as regiões principais
        await this.initializeRegions(mapData.regions);
        // Gera os POIs para cada região
        await this.generatePOIsForRegions();
        // Configura o streaming de assets
        this.setupAssetStreaming();
        // Configura o sistema de áudio ambiental
        this.setupAudioZones();
    }
    async initializeRegions(regionData) {
        for (const data of regionData) {
            const region = {
                id: data.id,
                name: this.generateRegionName(data.type),
                type: data.type,
                bounds: {
                    min: { x: data.center.x - data.radius, y: 0, z: data.center.z - data.radius },
                    max: { x: data.center.x + data.radius, y: data.heightRange.max, z: data.center.z + data.radius }
                },
                lootDensity: this.calculateLootDensity(data.type),
                verticalityLevel: data.heightRange.max / 100 // Normalizado para 0-1
            };
            this.regions.push(region);
        }
    }
    generateRegionName(type) {
        const prefixes = {
            urban: ['Downtown', 'Plaza', 'District'],
            military: ['Base', 'Outpost', 'Complex'],
            industrial: ['Factory', 'Complex', 'Zone'],
            forest: ['Woods', 'Forest', 'Grove']
        };
        const typeNames = prefixes[type];
        const prefix = typeNames[Math.floor(Math.random() * typeNames.length)];
        return `${prefix} ${Math.floor(Math.random() * 100)}`;
    }
    calculateLootDensity(type) {
        const baseDensities = {
            urban: 0.7,
            military: 0.9,
            industrial: 0.6,
            forest: 0.4
        };
        return baseDensities[type];
    }
    async generatePOIsForRegions() {
        for (const region of this.regions) {
            const numPOIs = this.calculatePOIsForRegion(region);
            for (let i = 0; i < numPOIs; i++) {
                const position = this.findPOIPosition(region);
                const template = this.createPOITemplate(region.type);
                const poi = await this.poiGenerator.generatePOI(region.type, position, template);
                this.pois.set(`${region.id}-poi-${i}`, poi);
            }
        }
    }
    calculatePOIsForRegion(region) {
        const sizeX = region.bounds.max.x - region.bounds.min.x;
        const sizeZ = region.bounds.max.z - region.bounds.min.z;
        const area = sizeX * sizeZ;
        // Base: 1 POI por 250000m² (500m x 500m)
        const basePOIs = Math.ceil(area / 250000);
        // Modificadores por tipo de região
        const modifiers = {
            urban: 2.0,
            military: 1.5,
            industrial: 1.3,
            forest: 0.8
        };
        return Math.ceil(basePOIs * modifiers[region.type]);
    }
    findPOIPosition(region) {
        const margin = 50; // 50m de margem das bordas
        return {
            x: region.bounds.min.x + margin + Math.random() * (region.bounds.max.x - region.bounds.min.x - 2 * margin),
            y: 0,
            z: region.bounds.min.z + margin + Math.random() * (region.bounds.max.z - region.bounds.min.z - 2 * margin)
        };
    }
    createPOITemplate(type) {
        const templates = {
            urban: {
                size: { width: 200, length: 200, height: 50 },
                buildingDensity: 0.6,
                propDensity: 0.3,
                coverPercentage: 0.7,
                verticalityFactor: 0.8
            },
            military: {
                size: { width: 250, length: 250, height: 30 },
                buildingDensity: 0.4,
                propDensity: 0.5,
                coverPercentage: 0.8,
                verticalityFactor: 0.4
            },
            industrial: {
                size: { width: 300, length: 300, height: 40 },
                buildingDensity: 0.5,
                propDensity: 0.4,
                coverPercentage: 0.6,
                verticalityFactor: 0.6
            },
            forest: {
                size: { width: 400, length: 400, height: 35 },
                buildingDensity: 0.2,
                propDensity: 0.7,
                coverPercentage: 0.9,
                verticalityFactor: 0.3
            }
        };
        return templates[type];
    }
    setupAssetStreaming() {
        // Configura zonas de streaming baseadas nos POIs
        for (const [id, poi] of this.pois) {
            this.streamingManager.setupStreamingZone({
                id,
                position: poi.position,
                size: poi.template.size,
                assets: poi.assets,
                priority: this.calculateStreamingPriority(poi)
            });
        }
    }
    calculateStreamingPriority(poi) {
        // Prioridade baseada na importância do POI e densidade de conteúdo
        const importanceFactor = poi.template.buildingDensity + poi.template.propDensity;
        const sizeFactor = (poi.template.size.width * poi.template.size.length) / 40000; // Normalizado para 200x200
        return importanceFactor * 0.7 + sizeFactor * 0.3;
    }
    setupAudioZones() {
        // Configura zonas de áudio para cada POI
        for (const [id, poi] of this.pois) {
            this.audioSystem.configureAmbientZone({
                id,
                position: poi.position,
                size: poi.template.size,
                sounds: poi.assets.ambientSounds,
                reverb: `${poi.type}_reverb`,
                transitionDistance: 50
            });
        }
    }
    // Métodos públicos para interação com o mapa
    getRegionAt(position) {
        return this.regions.find(region => position.x >= region.bounds.min.x &&
            position.x <= region.bounds.max.x &&
            position.z >= region.bounds.min.z &&
            position.z <= region.bounds.max.z) || null;
    }
    getHeightAt(position) {
        return this.layoutManager.getHeightAt(position.x, position.z);
    }
    getNearbyPOIs(position, radius) {
        const nearbyPOIs = [];
        for (const [id, poi] of this.pois) {
            const dx = position.x - poi.position.x;
            const dz = position.z - poi.position.z;
            const distanceSquared = dx * dx + dz * dz;
            if (distanceSquared <= radius * radius) {
                nearbyPOIs.push({ id, ...poi });
            }
        }
        return nearbyPOIs;
    }
    getMapStats() {
        return {
            regions: this.regions.length,
            pois: this.pois.size,
            streamingZones: this.streamingManager.getStreamingZonesCount(),
            audioZones: this.audioSystem.getAudioZonesCount()
        };
    }
}
exports.MapManager = MapManager;
//# sourceMappingURL=MapManager.js.map