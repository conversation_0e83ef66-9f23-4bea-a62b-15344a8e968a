{"version": 3, "file": "ServerManager.js", "sourceRoot": "", "sources": ["../../src/server/ServerManager.ts"], "names": [], "mappings": ";;;AAuBA,MAAa,aAAa;IAmBtB;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QAEnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,EAAE,aAAa,CAAC,0BAA0B,CAAC,CAAC;IACjD,CAAC;IAEM,cAAc,CAAC,MAAc,EAAE,OAAiB;QACnD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe;YAAE,OAAO,IAAI,CAAC;QAElC,MAAM,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,EAAE,CAAC;QAE9C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE;YACzB,QAAQ,EAAE,eAAe,CAAC,EAAE;YAC5B,OAAO,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC;QAEH,eAAe,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAEtD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACpC,IAAI,UAAU,GAA0B,IAAI,CAAC;QAC7C,IAAI,UAAU,GAAG,QAAQ,CAAC;QAE1B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACzC,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM;gBACxB,MAAM,CAAC,MAAM,KAAK,QAAQ;gBAC1B,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,QAAQ;gBAAE,SAAS;YAEpD,IAAI,MAAM,CAAC,WAAW,GAAG,UAAU,EAAE,CAAC;gBAClC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;gBAChC,UAAU,GAAG,MAAM,CAAC;YACxB,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,aAAa,CAAC,QAAgB,EAAE,MAAc;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QAExD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM;YACN,KAAK,EAAE;gBACH,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,CAAC;aACf;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,QAAgB;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAEM,iBAAiB,CAAC,QAAgB,EAAE,KAAmB;QAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QAEzB,kCAAkC;QAClC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;YAC/C,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,sCAAsC;QACtC,mCAAmC;QACnC,iCAAiC;QACjC,8BAA8B;IAClC,CAAC;IAEO,WAAW,CAAC,MAAc;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAEO,sBAAsB;QAC1B,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5C,iCAAiC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAElD,8BAA8B;YAC9B,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC;YAE7B,kCAAkC;YAClC,IAAI,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC1C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,MAAsB;QAC/C,oDAAoD;QACpD,OAAO;YACH,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YACxB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YAC3B,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;YAC/B,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;SACnC,CAAC;IACN,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,wBAAwB;QACxB,MAAM,CAAC,MAAM,GAAG,aAAa,CAAC;QAE9B,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE9B,qDAAqD;QACrD,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAgB;QACnC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;aACvD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAEvD,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,aAAa,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,MAAM,CAAC,CAAC;YAC7E,IAAI,CAAC,SAAS;gBAAE,SAAS;YAEzB,gCAAgC;YAChC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAEnC,kCAAkC;YAClC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACxD,IAAI,UAAU,EAAE,CAAC;oBACb,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,CAAC,GAAG,CAChB,MAAM,CAAC,MAAM,EACb,WAAW,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CACvD,CAAC;QACN,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5C,IAAI,IAAI,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,IAAI,GAAG,aAAa,CAAC,oBAAoB,GAAG,GAAG,EAAE,CAAC;gBACzD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,MAAc;QAChC,wDAAwD;QACxD,MAAM,WAAW,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,EAAE,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YAC1B,EAAE,EAAE,WAAW;YACf,MAAM;YACN,QAAQ,EAAE,aAAa,CAAC,sBAAsB;YAC9C,WAAW,EAAE,CAAC;YACd,MAAM,EAAE,QAAQ;YAChB,WAAW,EAAE;gBACT,GAAG,EAAE,CAAC;gBACN,MAAM,EAAE,CAAC;gBACT,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,eAAe,CAAC,MAAc;QAClC,iDAAiD;QACjD,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACpD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC;QAE7D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,eAAe;iBACjC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAgB;QACnC,0DAA0D;QAC1D,OAAO,CAAC,GAAG,CAAC,UAAU,QAAQ,gBAAgB,CAAC,CAAC;IACpD,CAAC;IAEM,cAAc;QACjB,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAClC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YAC7C,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzE,MAAM;gBACN,IAAI;gBACJ,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;qBACrC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;aAC/C,CAAC,CAAC;SACN,CAAC;IACN,CAAC;;AApRL,sCAqRC;AApR2B,6BAAe,GAAG,GAAG,CAAC;AACtB,oCAAsB,GAAG,GAAG,CAAC;AAC7B,wCAA0B,GAAG,IAAI,CAAC;AAClC,kCAAoB,GAAG,GAAG,CAAC"}