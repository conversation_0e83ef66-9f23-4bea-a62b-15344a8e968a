{"version": 3, "file": "MovementSystem.js", "sourceRoot": "", "sources": ["../../src/gameplay/MovementSystem.ts"], "names": [], "mappings": ";;;AA8BA,MAAa,cAAc;IAOvB;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1C,CAAC;IAEO,eAAe;QACnB,OAAO;YACH,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9B,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAClC,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;SACtC,CAAC;IACN,CAAC;IAEO,gBAAgB;QACpB,OAAO;YACH,SAAS,EAAE,GAAG,EAAS,uBAAuB;YAC9C,QAAQ,EAAE,GAAG,EAAU,uBAAuB;YAC9C,WAAW,EAAE,GAAG,EAAO,uBAAuB;YAC9C,SAAS,EAAE,GAAG,EAAS,gBAAgB;YACvC,UAAU,EAAE,GAAG,EAAQ,uBAAuB;YAC9C,QAAQ,EAAE,CAAC,EAAY,kBAAkB;YACzC,kBAAkB,EAAE,EAAE,EAAE,qBAAqB;YAC7C,eAAe,EAAE,CAAC,EAAK,mBAAmB;YAC1C,OAAO,EAAE,GAAG,EAAW,YAAY;YACnC,aAAa,EAAE,GAAG,EAAK,+BAA+B;YACtD,gBAAgB,EAAE,GAAG,EAAG,sCAAsC;YAC9D,aAAa,EAAE,GAAG,CAAK,+BAA+B;SACzD,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,KAAU,EAAE,SAAiB,EAAE,WAAmB;QAC5D,+BAA+B;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QAExD,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,mBAAmB;QACnB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAE/B,mBAAmB;QACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB,CAAC,KAAU,EAAE,SAAiB,EAAE,WAAmB;QAC1E,oCAAoC;QACpC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IACxC,CAAC;IAEO,kBAAkB,CAAC,KAAU;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IACnF,CAAC;IAEO,oBAAoB,CAAC,KAAU;QACnC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;YAC5B,iCAAiC;YACjC,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,KAAU,EAAE,WAAmB;QACtD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACrB,MAAM,aAAa,GAAG,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC9D,IAAI,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAChD,IAAI,CAAC,SAAS,EAAE,CAAC;YACrB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,KAAU,EAAE,WAAmB;QAC9C,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC;QAC1C,CAAC;IACL,CAAC;IAEO,OAAO,CAAC,WAAmB;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YACnB,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC;IACzD,CAAC;IAEO,IAAI;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;IAChC,CAAC;IAEO,aAAa;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YACnB,IAAI,CAAC,KAAK,CAAC,OAAO;YAClB,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IAC9C,CAAC;IAEO,UAAU;QACd,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC1D,CAAC;IAEO,SAAS;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEO,gBAAgB;QACpB,sCAAsC;QACtC,OAAO,IAAI,CAAC,CAAC,2CAA2C;IAC5D,CAAC;IAEO,YAAY,CAAC,SAAiB;QAClC,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;QAC7D,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAEO,aAAa,CAAC,SAAiB;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;YACpD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC;QACnC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,SAAiB;QACvC,qDAAqD;IACzD,CAAC;IAEO,cAAc,CAAC,SAAiB;QACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;IAC/D,CAAC;IAEO,iBAAiB;QACrB,uCAAuC;IAC3C,CAAC;IAEO,kBAAkB;QACtB,OAAO,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAChD,CAAC;IACN,CAAC;IAEM,QAAQ;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEM,QAAQ,CAAC,QAAiB;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/C,CAAC;;AAvLL,wCAwLC;AAvL2B,8BAAe,GAAG,EAAE,CAAC;AACrB,kCAAmB,GAAG,GAAG,CAAC"}