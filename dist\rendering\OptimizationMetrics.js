"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizationMetricsCollector = void 0;
class OptimizationMetricsCollector {
    constructor() {
        this.metrics = new Map();
    }
    startOperation(id) {
        if (!this.metrics.has(id)) {
            this.metrics.set(id, {
                originalSize: 0,
                optimizedSize: 0,
                compressionRatio: 1,
                processingTimeMs: 0,
                memoryReduction: 0
            });
        }
    }
    endOperation(id, metrics) {
        if (this.metrics.has(id)) {
            this.metrics.set(id, {
                ...this.metrics.get(id),
                ...metrics
            });
        }
    }
    getStartTime(id) {
        const metrics = this.metrics.get(id);
        return metrics ? performance.now() - metrics.processingTimeMs : performance.now();
    }
    getMetrics(id) {
        return this.metrics.get(id);
    }
    clear() {
        this.metrics.clear();
    }
    generateReport() {
        let report = '';
        for (const [id, metrics] of this.metrics.entries()) {
            report += `\nOperação: ${id}\n`;
            report += `- Tamanho original: ${(metrics.originalSize / 1024).toFixed(2)}KB\n`;
            report += `- Tamanho otimizado: ${(metrics.optimizedSize / 1024).toFixed(2)}KB\n`;
            report += `- Taxa de compressão: ${metrics.compressionRatio.toFixed(2)}x\n`;
            report += `- Redução de memória: ${metrics.memoryReduction.toFixed(2)}%\n`;
            report += `- Tempo de processamento: ${metrics.processingTimeMs.toFixed(2)}ms\n`;
            if (metrics.quality !== undefined) {
                report += `- Qualidade: ${metrics.quality}\n`;
            }
            if (metrics.format) {
                report += `- Formato: ${metrics.format}\n`;
            }
            if (metrics.psnr !== undefined) {
                report += `- PSNR: ${metrics.psnr.toFixed(2)}dB\n`;
            }
        }
        return report;
    }
}
exports.OptimizationMetricsCollector = OptimizationMetricsCollector;
//# sourceMappingURL=OptimizationMetrics.js.map