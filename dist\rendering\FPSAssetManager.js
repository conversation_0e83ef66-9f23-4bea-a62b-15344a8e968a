"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FPSAssetManager = void 0;
const AssetOptimizer_1 = require("./AssetOptimizer");
const AnimationSystem_1 = require("./AnimationSystem");
class FPSAssetManager {
    constructor() {
        this.assetOptimizer = new AssetOptimizer_1.AssetOptimizer();
        this.loadedWeapons = new Map();
        this.weaponConfigs = new Map();
        this.animationPool = new Map();
        this.texturePool = new Map();
        this.initializeWeaponConfigs();
    }
    initializeWeaponConfigs() {
        // Configuração do rifle híbrido AK-47/M4A1
        this.weaponConfigs.set('hybrid_rifle', {
            model: 'models/weapons/hybrid_rifle.glb',
            textures: [
                'textures/weapons/hybrid_rifle_albedo.ktx2',
                'textures/weapons/hybrid_rifle_normal.ktx2',
                'textures/weapons/hybrid_rifle_roughness.ktx2',
                'textures/weapons/hybrid_rifle_metallic.ktx2'
            ],
            animations: [
                'animations/hybrid_rifle_idle.glb',
                'animations/hybrid_rifle_fire.glb',
                'animations/hybrid_rifle_reload.glb',
                'animations/hybrid_rifle_inspect.glb'
            ],
            vfx: {
                muzzleFlash: {
                    type: 'rifle',
                    scale: 1.2,
                    duration: 0.1
                },
                shellEjection: {
                    model: 'models/fx/rifle_shell.glb',
                    force: 2.0
                },
                impacts: [
                    { type: 'metal', prefab: 'fx/impact_metal.prefab' },
                    { type: 'concrete', prefab: 'fx/impact_concrete.prefab' },
                    { type: 'wood', prefab: 'fx/impact_wood.prefab' }
                ]
            }
        });
        // Configuração da AWP modernizada
        this.weaponConfigs.set('modern_awp', {
            model: 'models/weapons/modern_awp.glb',
            textures: [
                'textures/weapons/modern_awp_albedo.ktx2',
                'textures/weapons/modern_awp_normal.ktx2',
                'textures/weapons/modern_awp_roughness.ktx2',
                'textures/weapons/modern_awp_metallic.ktx2',
                'textures/weapons/modern_awp_scope.ktx2'
            ],
            animations: [
                'animations/modern_awp/idle.glb',
                'animations/modern_awp/fire.glb',
                'animations/modern_awp/reload.glb',
                'animations/modern_awp/inspect.glb',
                'animations/modern_awp/scope.glb'
            ],
            vfx: {
                muzzleFlash: {
                    scale: 1.5,
                    duration: 0.15,
                    color: { r: 1.0, g: 0.8, b: 0.4, a: 0.9 },
                    intensity: 2.5
                },
                shellEjection: {
                    model: 'models/fx/sniper_shell.glb',
                    force: 2.5,
                    spin: { x: 12, y: 8, z: 18 },
                    lifetime: 2.5
                },
                impacts: [
                    {
                        type: 'penetration',
                        decal: 'textures/fx/impact_penetration.ktx2',
                        particles: 'fx/penetration_spark.json',
                        sound: 'sounds/impact_penetration.ogg'
                    }
                ]
            }
        });
        // Configuração da Desert Eagle
        this.weaponConfigs.set('desert_eagle', {
            model: 'models/weapons/desert_eagle.glb',
            textures: [
                'textures/weapons/deagle_albedo.ktx2',
                'textures/weapons/deagle_normal.ktx2',
                'textures/weapons/deagle_roughness.ktx2',
                'textures/weapons/deagle_metallic.ktx2'
            ],
            animations: [
                'animations/deagle/idle.glb',
                'animations/deagle/fire.glb',
                'animations/deagle/reload.glb',
                'animations/deagle/inspect.glb'
            ],
            vfx: {
                muzzleFlash: {
                    scale: 1.3,
                    duration: 0.12,
                    color: { r: 1.0, g: 0.7, b: 0.3, a: 0.85 },
                    intensity: 2.2
                },
                shellEjection: {
                    model: 'models/fx/pistol_shell.glb',
                    force: 1.8,
                    spin: { x: 8, y: 4, z: 12 },
                    lifetime: 2.0
                },
                impacts: [
                    {
                        type: 'heavy',
                        decal: 'textures/fx/impact_heavy.ktx2',
                        particles: 'fx/heavy_impact.json',
                        sound: 'sounds/impact_heavy.ogg'
                    }
                ]
            }
        });
    }
    async loadWeapon(weaponId) {
        const config = this.weaponConfigs.get(weaponId);
        if (!config) {
            throw new Error(`Configuração não encontrada para a arma: ${weaponId}`);
        }
        // Carrega e otimiza o modelo
        const model = await this.loadModel(config.model);
        const optimizedModel = this.assetOptimizer.optimizeWeaponModel(model, {
            maxPolygons: 15000,
            maxBones: 32,
            maxUVChannels: 2,
            lodLevels: [75, 50, 25]
        });
        // Carrega e otimiza texturas
        const textures = await Promise.all(config.textures.map(texturePath => this.loadTexture(texturePath)));
        this.assetOptimizer.optimizeTextures(textures, {
            maxSize: 4096,
            compressionFormat: 'BC7',
            generateMips: true,
            srgb: true
        });
        // Carrega animações para o pool
        const animations = await Promise.all(config.animations.map(animPath => this.loadAnimation(animPath)));
        this.animationPool.set(weaponId, animations);
        // Armazena arma otimizada
        this.loadedWeapons.set(weaponId, {
            model: optimizedModel,
            textures,
            animations,
            vfx: config.vfx
        });
    }
    getWeaponMetrics(weaponId) {
        const weapon = this.loadedWeapons.get(weaponId);
        if (!weapon) {
            throw new Error(`Arma não carregada: ${weaponId}`);
        }
        return {
            fps: this.measureFPS(),
            frameTime: this.measureFrameTime(),
            gpuMemory: this.calculateGPUMemory(weapon),
            drawCalls: this.countDrawCalls(weapon),
            triangles: this.countTriangles(weapon)
        };
    }
    async loadModel(path) {
        // TODO: Implementar carregamento real de modelo
        return null;
    }
    async loadTexture(path) {
        // TODO: Implementar carregamento real de textura
        return null;
    }
    async loadAnimation(path) {
        // TODO: Implementar carregamento real de animação
        return null;
    }
    measureFPS() {
        // TODO: Implementar medição real de FPS
        return 0;
    }
    measureFrameTime() {
        // TODO: Implementar medição real de tempo de frame
        return 0;
    }
    calculateGPUMemory(weapon) {
        // TODO: Implementar cálculo real de memória GPU
        return 0;
    }
    countDrawCalls(weapon) {
        // TODO: Implementar contagem real de draw calls
        return 0;
    }
    countTriangles(weapon) {
        // TODO: Implementar contagem real de triângulos
        return 0;
    }
    initializeWeaponAnimations(weaponId) {
        const animationSystem = new AnimationSystem_1.AnimationSystem();
        switch (weaponId) {
            case 'hybrid_rifle':
                this.setupHybridRifleAnimations(animationSystem);
                break;
            case 'modern_awp':
                this.setupModernAWPAnimations(animationSystem);
                break;
            case 'desert_eagle':
                this.setupDesertEagleAnimations(animationSystem);
                break;
        }
    }
    setupHybridRifleAnimations(animationSystem) {
        // Idle
        const idleAnim = {
            name: 'hybrid_rifle_idle',
            duration: 3.0,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateBreathingPositions(),
                    rotations: this.generateBreathingRotations(),
                    scales: [],
                    times: this.generateTimeArray(3.0, 90)
                }
            ]
        };
        // Fire
        const fireAnim = {
            name: 'hybrid_rifle_fire',
            duration: 0.2,
            frameRate: 60,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateRecoilPositions(0.1, 0.05, 0.02),
                    rotations: this.generateRecoilRotations(5, 2),
                    scales: [],
                    times: this.generateTimeArray(0.2, 12)
                }
            ]
        };
        // Reload
        const reloadAnim = {
            name: 'hybrid_rifle_reload',
            duration: 2.5,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateReloadPositions(),
                    rotations: this.generateReloadRotations(),
                    scales: [],
                    times: this.generateTimeArray(2.5, 75)
                },
                {
                    targetBone: 'magazine',
                    positions: this.generateMagazinePositions(),
                    rotations: this.generateMagazineRotations(),
                    scales: [],
                    times: this.generateTimeArray(2.5, 75)
                }
            ]
        };
        // Adiciona animações às camadas apropriadas
        animationSystem.addAnimation('base', 'idle', idleAnim);
        animationSystem.addAnimation('action', 'fire', fireAnim);
        animationSystem.addAnimation('action', 'reload', reloadAnim);
    }
    setupModernAWPAnimations(animationSystem) {
        // Idle mais estável devido ao peso da arma
        const idleAnim = {
            name: 'modern_awp_idle',
            duration: 4.0,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateBreathingPositions(0.0015, 0.0008), // Movimentos mais sutis
                    rotations: this.generateBreathingRotations(0.15, 0.08), // Rotações mais suaves
                    scales: [],
                    times: this.generateTimeArray(4.0, 120)
                }
            ]
        };
        // Fire com recuo mais forte
        const fireAnim = {
            name: 'modern_awp_fire',
            duration: 0.35,
            frameRate: 60,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateRecoilPositions(0.15, 0.08, 0.01), // Recuo mais forte
                    rotations: this.generateRecoilRotations(8, 1), // Mais pitch, menos yaw
                    scales: [],
                    times: this.generateTimeArray(0.35, 21)
                }
            ]
        };
        // Reload mais lenta e deliberada
        const reloadAnim = {
            name: 'modern_awp_reload',
            duration: 3.2,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateReloadPositions(true), // true para reload de bolt-action
                    rotations: this.generateReloadRotations(true),
                    scales: [],
                    times: this.generateTimeArray(3.2, 96)
                },
                {
                    targetBone: 'magazine',
                    positions: this.generateMagazinePositions(true),
                    rotations: this.generateMagazineRotations(true),
                    scales: [],
                    times: this.generateTimeArray(3.2, 96)
                },
                {
                    targetBone: 'bolt',
                    positions: this.generateBoltPositions(),
                    rotations: this.generateBoltRotations(),
                    scales: [],
                    times: this.generateTimeArray(3.2, 96)
                }
            ]
        };
        animationSystem.addAnimation('base', 'idle', idleAnim);
        animationSystem.addAnimation('action', 'fire', fireAnim);
        animationSystem.addAnimation('action', 'reload', reloadAnim);
    }
    setupDesertEagleAnimations(animationSystem) {
        // Idle com movimento mais rápido devido ao peso menor
        const idleAnim = {
            name: 'desert_eagle_idle',
            duration: 2.5,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateBreathingPositions(0.003, 0.002), // Movimentos mais pronunciados
                    rotations: this.generateBreathingRotations(0.3, 0.15), // Rotações mais visíveis
                    scales: [],
                    times: this.generateTimeArray(2.5, 75)
                }
            ]
        };
        // Fire com recuo forte mas rápido
        const fireAnim = {
            name: 'desert_eagle_fire',
            duration: 0.25,
            frameRate: 60,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateRecoilPositions(0.08, 0.1, 0.03), // Mais movimento vertical
                    rotations: this.generateRecoilRotations(10, 4), // Recuo angular forte
                    scales: [],
                    times: this.generateTimeArray(0.25, 15)
                }
            ]
        };
        // Reload rápida
        const reloadAnim = {
            name: 'desert_eagle_reload',
            duration: 1.8,
            frameRate: 30,
            channels: [
                {
                    targetBone: 'weapon_root',
                    positions: this.generateReloadPositions(false), // false para reload de pistola
                    rotations: this.generateReloadRotations(false),
                    scales: [],
                    times: this.generateTimeArray(1.8, 54)
                },
                {
                    targetBone: 'magazine',
                    positions: this.generateMagazinePositions(false),
                    rotations: this.generateMagazineRotations(false),
                    scales: [],
                    times: this.generateTimeArray(1.8, 54)
                },
                {
                    targetBone: 'slide',
                    positions: this.generateSlidePositions(),
                    rotations: this.generateSlideRotations(),
                    scales: [],
                    times: this.generateTimeArray(1.8, 54)
                }
            ]
        };
        animationSystem.addAnimation('base', 'idle', idleAnim);
        animationSystem.addAnimation('action', 'fire', fireAnim);
        animationSystem.addAnimation('action', 'reload', reloadAnim);
    }
    generateBreathingPositions(amplitudeX = 0.002, amplitudeY = 0.001) {
        const positions = [];
        for (let i = 0; i < 90; i++) {
            const t = i / 89;
            positions.push({
                x: Math.sin(t * Math.PI * 2) * amplitudeX,
                y: Math.cos(t * Math.PI * 2) * amplitudeY,
                z: 0
            });
        }
        return positions;
    }
    generateBreathingRotations(pitchAmplitude = 0.5, yawAmplitude = 0.25) {
        const rotations = [];
        for (let i = 0; i < 90; i++) {
            const t = i / 89;
            const angle = Math.sin(t * Math.PI * 2);
            rotations.push(this.eulerToQuaternion(angle * pitchAmplitude * 0.2, angle * yawAmplitude * 0.1, 0));
        }
        return rotations;
    }
    generateReloadPositions(isBoltAction = false) {
        const positions = [];
        const frames = isBoltAction ? 96 : 54;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let x = 0, y = 0, z = 0;
            if (t < 0.3) { // Início do reload
                y = -Math.sin(t / 0.3 * Math.PI) * 0.1;
                z = -Math.sin(t / 0.3 * Math.PI) * 0.05;
            }
            else if (t < 0.7) { // Troca de carregador
                const phase = (t - 0.3) / 0.4;
                y = -0.1 + Math.sin(phase * Math.PI) * 0.05;
                x = Math.sin(phase * Math.PI) * (isBoltAction ? 0.08 : 0.05);
            }
            else { // Finalização
                const phase = (t - 0.7) / 0.3;
                y = -Math.cos(phase * Math.PI * 0.5) * 0.05;
            }
            positions.push({ x, y, z });
        }
        return positions;
    }
    generateReloadRotations(isBoltAction = false) {
        const rotations = [];
        const frames = isBoltAction ? 96 : 54;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let pitch = 0, yaw = 0, roll = 0;
            if (t < 0.3) { // Início
                pitch = -Math.sin(t / 0.3 * Math.PI) * 15;
                roll = Math.sin(t / 0.3 * Math.PI) * (isBoltAction ? 10 : 5);
            }
            else if (t < 0.7) { // Meio
                const phase = (t - 0.3) / 0.4;
                pitch = -15 + Math.sin(phase * Math.PI) * 5;
                yaw = Math.sin(phase * Math.PI) * (isBoltAction ? 20 : 10);
            }
            else { // Fim
                const phase = (t - 0.7) / 0.3;
                pitch = -Math.cos(phase * Math.PI * 0.5) * 10;
            }
            rotations.push(this.eulerToQuaternion(pitch * Math.PI / 180, yaw * Math.PI / 180, roll * Math.PI / 180));
        }
        return rotations;
    }
    generateMagazinePositions(isBoltAction = false) {
        const positions = [];
        const frames = isBoltAction ? 96 : 54;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let x = 0, y = 0, z = 0;
            if (t < 0.2) { // Magazine out
                y = -Math.sin(t / 0.2 * Math.PI) * 0.2;
            }
            else if (t < 0.8) { // Magazine movement
                const phase = (t - 0.2) / 0.6;
                y = -0.2;
                z = Math.sin(phase * Math.PI) * -0.1;
            }
            else { // Magazine in
                const phase = (t - 0.8) / 0.2;
                y = -0.2 * (1 - phase);
            }
            positions.push({ x, y, z });
        }
        return positions;
    }
    generateMagazineRotations(isBoltAction = false) {
        const rotations = [];
        const frames = isBoltAction ? 96 : 54;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let pitch = 0, yaw = 0, roll = 0;
            if (t < 0.2) { // Magazine out
                pitch = Math.sin(t / 0.2 * Math.PI) * 30;
            }
            else if (t < 0.8) { // Magazine movement
                const phase = (t - 0.2) / 0.6;
                pitch = 30;
                roll = Math.sin(phase * Math.PI) * (isBoltAction ? 45 : 30);
            }
            else { // Magazine in
                const phase = (t - 0.8) / 0.2;
                pitch = 30 * (1 - phase);
            }
            rotations.push(this.eulerToQuaternion(pitch * Math.PI / 180, yaw * Math.PI / 180, roll * Math.PI / 180));
        }
        return rotations;
    }
    generateBoltPositions() {
        const positions = [];
        const frames = 96;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let x = 0, y = 0, z = 0;
            if (t > 0.7 && t < 0.85) { // Bolt back
                const phase = (t - 0.7) / 0.15;
                z = -Math.sin(phase * Math.PI) * 0.1;
            }
            else if (t >= 0.85) { // Bolt forward
                const phase = (t - 0.85) / 0.15;
                z = -0.1 * (1 - phase);
            }
            positions.push({ x, y, z });
        }
        return positions;
    }
    generateBoltRotations() {
        const rotations = [];
        const frames = 96;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let roll = 0;
            if (t > 0.7 && t < 0.85) { // Bolt rotation back
                const phase = (t - 0.7) / 0.15;
                roll = Math.sin(phase * Math.PI) * 45;
            }
            else if (t >= 0.85) { // Bolt rotation forward
                const phase = (t - 0.85) / 0.15;
                roll = 45 * (1 - phase);
            }
            rotations.push(this.eulerToQuaternion(0, 0, roll * Math.PI / 180));
        }
        return rotations;
    }
    generateSlidePositions() {
        const positions = [];
        const frames = 54;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            let x = 0, y = 0, z = 0;
            if (t > 0.8 && t < 0.9) { // Slide back
                const phase = (t - 0.8) / 0.1;
                z = -Math.sin(phase * Math.PI) * 0.08;
            }
            else if (t >= 0.9) { // Slide forward
                const phase = (t - 0.9) / 0.1;
                z = -0.08 * (1 - phase);
            }
            positions.push({ x, y, z });
        }
        return positions;
    }
    generateSlideRotations() {
        const rotations = [];
        const frames = 54;
        for (let i = 0; i < frames; i++) {
            rotations.push(this.eulerToQuaternion(0, 0, 0)); // Slide não rotaciona
        }
        return rotations;
    }
    eulerToQuaternion(x, y, z) {
        const c1 = Math.cos(x / 2);
        const c2 = Math.cos(y / 2);
        const c3 = Math.cos(z / 2);
        const s1 = Math.sin(x / 2);
        const s2 = Math.sin(y / 2);
        const s3 = Math.sin(z / 2);
        return {
            w: c1 * c2 * c3 - s1 * s2 * s3,
            x: s1 * c2 * c3 + c1 * s2 * s3,
            y: c1 * s2 * c3 - s1 * c2 * s3,
            z: c1 * c2 * s3 + s1 * s2 * c3
        };
    }
    easeOutQuad(t) {
        return 1 - (1 - t) * (1 - t);
    }
    easeInOutQuad(t) {
        return t < 0.5
            ? 2 * t * t
            : 1 - Math.pow(-2 * t + 2, 2) / 2;
    }
    generateTimeArray(duration, frames) {
        const times = [];
        for (let i = 0; i < frames; i++) {
            times.push((i / (frames - 1)) * duration);
        }
        return times;
    }
    generateRecoilPositions(back, up, side) {
        const positions = [];
        const frames = 15;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            const forward = this.easeOutQuad(t); // Recuo para trás
            const upward = Math.sin(t * Math.PI); // Movimento para cima
            const lateral = Math.sin(t * Math.PI * 2); // Movimento lateral
            positions.push({
                x: side * lateral * (1 - forward), // Movimento lateral diminui conforme a arma retorna
                y: up * upward, // Movimento vertical segue curva senoidal
                z: -back * (1 - forward) // Recuo para trás com easing
            });
        }
        return positions;
    }
    generateRecoilRotations(pitch, yaw) {
        const rotations = [];
        const frames = 15;
        for (let i = 0; i < frames; i++) {
            const t = i / (frames - 1);
            const recoilEase = this.easeOutQuad(t);
            const recoveryEase = this.easeInOutQuad(t);
            // Recuo principal
            const currentPitch = -pitch * (1 - recoilEase);
            // Movimento lateral aleatório
            const currentYaw = yaw * Math.sin(t * Math.PI * 2) * (1 - recoveryEase);
            // Pequena rotação no eixo Z para efeito visual
            const roll = Math.sin(t * Math.PI * 4) * 0.5 * (1 - recoveryEase);
            rotations.push(this.eulerToQuaternion(currentPitch * Math.PI / 180, currentYaw * Math.PI / 180, roll * Math.PI / 180));
        }
        return rotations;
    }
}
exports.FPSAssetManager = FPSAssetManager;
//# sourceMappingURL=FPSAssetManager.js.map