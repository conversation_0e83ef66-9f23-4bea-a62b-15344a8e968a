{"version": 3, "file": "InitialMapProducer.js", "sourceRoot": "", "sources": ["../../src/gameplay/InitialMapProducer.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AACtD,iDAA8C;AAC9C,yDAAsD;AACtD,gFAA6E;AAC7E,8EAA2E;AAC3E,8DAA2D;AAC3D,wEAAqE;AAiBrE,MAAa,kBAAkB;IAU3B,YACI,MAAW,EACX,gBAA2D;QAE3D,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC;YACzC,IAAI,EAAE,IAAI,EAAE,MAAM;YAClB,UAAU,EAAE,CAAC,EAAE,iBAAiB;YAChC,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,CAAC;YACZ,eAAe,EAAE,CAAC;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,iDAAuB,EAAE,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,IAAI,+CAAsB,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,iCAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,GAAG,IAAI,uCAAkB,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC;YACD,IAAI,CAAC,cAAc,CAAC;gBAChB,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,CAAC;gBACZ,YAAY,EAAE,4BAA4B;gBAC1C,kBAAkB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAElE,sBAAsB;YACtB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAExC,qBAAqB;YACrB,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvC,IAAI,CAAC,cAAc,CAAC;wBAChB,eAAe,EAAE,GAAG;wBACpB,aAAa,EAAE,aAAa;wBAC5B,SAAS;wBACT,YAAY,EAAE,eAAe,aAAa,GAAG,CAAC,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,GAAG;wBAC9E,kBAAkB,EAAE,IAAI;qBAC3B,CAAC,CAAC;oBAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAErD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAC3C,MAAM,CAAC,IAAI,EACX,QAAQ,EACR,QAAQ,CACX,CAAC;oBAEF,MAAM,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;wBAChD,MAAM,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;wBACtD,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,QAAQ;wBAC7B,aAAa,EAAE,GAAG,CAAC,MAAM,CAAC,aAAa;qBAC1C,CAAC,CAAC;oBAEH,aAAa,EAAE,CAAC;gBACpB,CAAC;YACL,CAAC;YAED,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAE5C,+BAA+B;YAC/B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;aAChD,CAAC;QAEN,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;aACtE,CAAC;QACN,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,OAAO;YACH;gBACI,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;gBAClC,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBAChC,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBACjC,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;gBAClC,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,CAAC;aACd;SACJ,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,OAA8B;QAC3D,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,YAAY,EAAE,sBAAsB;YACpC,kBAAkB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAErE,oDAAoD;QACpD,MAAM,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;YAChD,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,aAAa,EAAE,EAAE;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,YAAY,EAAE,wBAAwB;YACtC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;SAC3D,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAA8B;QAC5D,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAElE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,IAAI,CAAC,cAAc,CAAC;oBAChB,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,aAAa;oBAC5B,SAAS;oBACT,YAAY,EAAE,eAAe,aAAa,GAAG,CAAC,IAAI,SAAS,EAAE;oBAC7D,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;iBAC3D,CAAC,CAAC;gBAEH,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAErD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAC3C,MAAM,CAAC,IAAI,EACX,WAAW,EACX,QAAQ,CACX,CAAC;gBAEF,yBAAyB;gBACzB,MAAM,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;oBAChD,MAAM,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;oBACtD,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,QAAQ;oBAC7B,aAAa,EAAE,GAAG,CAAC,MAAM,CAAC,aAAa;iBAC1C,CAAC,CAAC;gBAEH,aAAa,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,MAA2B,EAAE,KAAa;QACnE,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,2CAA2C;QAEjF,OAAO;YACH,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC/C,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAClD,CAAC;IACN,CAAC;IAEM,iBAAiB,CAAC,IAAoD;QACzE,MAAM,SAAS,GAAG;YACd,KAAK,EAAE;gBACH,IAAI,EAAE,OAAgB;gBACtB,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,QAAQ,EAAE;gBACN,IAAI,EAAE,UAAmB;gBACzB,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,YAAqB;gBAC3B,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,MAAM,EAAE;gBACJ,IAAI,EAAE,QAAiB;gBACvB,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;SACK,CAAC;QAEX,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,OAA8B;QAC/D,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,CAAC,CAAC;YACjB,SAAS,EAAE,CAAC,CAAC;YACb,YAAY,EAAE,8BAA8B;YAC5C,kBAAkB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,0CAA0C;YAC1C,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC;gBAClC,EAAE,EAAE,UAAU,MAAM,CAAC,IAAI,EAAE;gBAC3B,QAAQ,EAAE,MAAM,CAAC,MAAM;gBACvB,IAAI,EAAE;oBACF,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;oBACxB,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;oBACzB,MAAM,EAAE,IAAI,CAAC,qDAAqD;iBACrE;gBACD,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjD,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,SAAS;gBAC/B,kBAAkB,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,6BAA6B;aACxE,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,CAAC,CAAC;YACjB,SAAS,EAAE,CAAC,CAAC;YACb,YAAY,EAAE,6BAA6B;YAC3C,kBAAkB,EAAE,IAAI;SAC3B,CAAC,CAAC;IACP,CAAC;IAEO,uBAAuB,CAAC,IAAoD;QAChF,MAAM,SAAS,GAAG;YACd,KAAK,EAAE;gBACH,mBAAmB;gBACnB,oBAAoB;gBACpB,gBAAgB;gBAChB,yBAAyB;aAC5B;YACD,QAAQ,EAAE;gBACN,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,eAAe;aAClB;YACD,UAAU,EAAE;gBACR,mBAAmB;gBACnB,gBAAgB;gBAChB,aAAa;gBACb,eAAe;aAClB;YACD,MAAM,EAAE;gBACJ,qBAAqB;gBACrB,oBAAoB;gBACpB,eAAe;gBACf,eAAe;aAClB;SACJ,CAAC;QAEF,OAAO,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC5B,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,CAAC,CAAC;YACjB,SAAS,EAAE,CAAC,CAAC;YACb,YAAY,EAAE,oCAAoC;YAClD,kBAAkB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,8BAA8B;QAC9B,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QAEzC,8CAA8C;QAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,kBAAkB;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC1D,MAAM,SAAS,GAAG,GAAG,CAAC;QAEtB,mCAAmC;QACnC,IAAI,YAAY,CAAC,UAAU,GAAG,SAAS,GAAG,IAAI,EAAE,CAAC,CAAC,mBAAmB;YACjE,MAAM,IAAI,KAAK,CACX,+BAA+B,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;gBACxE,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CACnD,CAAC;QACN,CAAC;QAED,IAAI,CAAC,cAAc,CAAC;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,CAAC,CAAC;YACjB,SAAS,EAAE,CAAC,CAAC;YACb,YAAY,EAAE,wCAAwC;YACtD,kBAAkB,EAAE,YAAY;SACnC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,QAA+B;QAClD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;CACJ;AA7VD,gDA6VC"}