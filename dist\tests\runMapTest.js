"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const MapProductionTest_1 = require("./MapProductionTest");
async function runMapProductionTest() {
    console.log('Iniciando cenário de teste do pipeline de produção do mapa...\n');
    const test = new MapProductionTest_1.MapProductionTest();
    const result = await test.runTest();
    if (result.success) {
        console.log('\nCenário de teste concluído com sucesso!');
        console.log('Resultados esperados:');
        console.log('- Terreno base gerado (500x500m)');
        console.log('- 1 POI urbano criado com assets dummy');
        console.log('- Zonas de áudio ambiental configuradas');
        console.log('- Otimizações básicas aplicadas');
        console.log('- FPS mantido acima de 950 (95% do target de 999)');
        process.exit(0);
    }
    else {
        console.error('\nFalha no cenário de teste:', result.error);
        process.exit(1);
    }
}
runMapProductionTest().catch(error => {
    console.error('Erro fatal durante o teste:', error);
    process.exit(1);
});
//# sourceMappingURL=runMapTest.js.map