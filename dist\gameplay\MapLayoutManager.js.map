{"version": 3, "file": "MapLayoutManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/MapLayoutManager.ts"], "names": [], "mappings": ";;;AA2BA,MAAa,gBAAgB;IAOzB;QAHiB,aAAQ,GAAG,IAAI,CAAC,CAAC,gBAAgB;QACjC,yBAAoB,GAAG,GAAG,CAAC,CAAC,iBAAiB;QAG1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC5D,IAAI,CAAC,SAAS,GAAG;YACb,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,SAAS;YACjB,IAAI,EAAE,IAAI,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC;YAC7C,UAAU,EAAE,IAAI,CAAC,oBAAoB;SACxC,CAAC;IACN,CAAC;IAEO,iBAAiB;QACrB,uCAAuC;QACvC,MAAM,WAAW,GAAiB;YAC9B;gBACI,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;gBAClC,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;aACnC;YACD;gBACI,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBAChC,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;aACnC;YACD;gBACI,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBACjC,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;aACnC;YACD;gBACI,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;gBAClC,MAAM,EAAE,GAAG;gBACX,WAAW,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;aACrC;SACJ,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;QAC3B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAEO,yBAAyB;QAC7B,uCAAuC;QACvC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,qBAAqB,CAAC,MAAkB;QAC5C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QAE/C,0DAA0D;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEpE,+CAA+C;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,KAAK,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjD,MAAM,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC;gBAC3B,MAAM,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC;gBAE3B,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;oBAC5C,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;oBAChD,SAAS;gBACb,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1C,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;oBAC3B,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC,CAAC;oBACnD,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG;wBAC1B,CAAC,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC;4BACnC,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBAEjC,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC;oBACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;gBACxC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEO,wBAAwB;QAC5B,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,CAAC,CAAC;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,IAAI,KAAK,GAAG,CAAC,CAAC;gBAEd,KAAK,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;oBAChD,KAAK,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;wBAChD,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;wBACvB,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;wBAEvB,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK;4BAC9C,OAAO,IAAI,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;4BAClD,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,OAAO,CAAC;4BACvD,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAClC,KAAK,EAAE,CAAC;wBACZ,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC3C,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY,CAAC;IACvC,CAAC;IAEO,aAAa;QACjB,2CAA2C;QAC3C,MAAM,QAAQ,GAAkB;YAC5B;gBACI,EAAE,EAAE,gBAAgB;gBACpB,QAAQ,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;gBACpC,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,EAAE,EAAE,gBAAgB;gBACpB,QAAQ,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBAClC,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,EAAE,EAAE,sBAAsB;gBAC1B,QAAQ,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBACnC,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,CAAC;aACd;SACJ,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED,WAAW,CAAC,MAAc,EAAE,MAAc;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACzD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEzD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;YAClC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,WAAW,CAAC,QAAiB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC9B,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YACxC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YACxC,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAC1C,OAAO,eAAe,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5D,CAAC,CAAC,IAAI,IAAI,CAAC;IACf,CAAC;IAED,iBAAiB,CAAC,QAAiB,EAAE,MAAc;QAC/C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC/B,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxC,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAC1C,MAAM,YAAY,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1C,OAAO,eAAe,IAAI,YAAY,GAAG,YAAY,CAAC;QAC1D,CAAC,CAAC,CAAC;IACP,CAAC;IAED,aAAa;QACT,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,CAAC,oBAAoB;SACxC,CAAC;IACN,CAAC;CACJ;AA3MD,4CA2MC"}