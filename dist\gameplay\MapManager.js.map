{"version": 3, "file": "MapManager.js", "sourceRoot": "", "sources": ["../../src/gameplay/MapManager.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AACtD,iDAA8C;AAC9C,8EAA2E;AAC3E,8DAA2D;AAgB3D,MAAa,UAAU;IAQnB,YAAY,WAAgB;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACpD,IAAI,CAAC,WAAW,GAAG,IAAI,iCAAe,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QAEnD,mCAAmC;QACnC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE9C,gCAAgC;QAChC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,kCAAkC;QAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,yCAAyC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,UAAiB;QAC7C,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAc;gBACtB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAkB,CAAC;gBACtD,IAAI,EAAE,IAAI,CAAC,IAAkB;gBAC7B,MAAM,EAAE;oBACJ,GAAG,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;oBAC7E,GAAG,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;iBACnG;gBACD,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAkB,CAAC;gBAC/D,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC,uBAAuB;aACvE,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,IAAgB;QACvC,MAAM,QAAQ,GAAiC;YAC3C,KAAK,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;YACxC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;YACxC,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;YAC1C,MAAM,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;SACvC,CAAC;QAEF,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACvE,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;IAC1D,CAAC;IAEO,oBAAoB,CAAC,IAAgB;QACzC,MAAM,aAAa,GAA+B;YAC9C,KAAK,EAAE,GAAG;YACV,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,GAAG;SACd,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAErD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAC3C,MAAM,CAAC,IAAI,EACX,QAAQ,EACR,QAAQ,CACX,CAAC;gBAEF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,MAAiB;QAC5C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;QAE3B,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;QAE1C,mCAAmC;QACnC,MAAM,SAAS,GAA+B;YAC1C,KAAK,EAAE,GAAG;YACV,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,GAAG;SACd,CAAC;QAEF,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,eAAe,CAAC,MAAiB;QACrC,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;QAE9C,OAAO;YACH,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YAC1G,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;SAC7G,CAAC;IACN,CAAC;IAEO,iBAAiB,CAAC,IAAgB;QACtC,MAAM,SAAS,GAA4B;YACvC,KAAK,EAAE;gBACH,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,QAAQ,EAAE;gBACN,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;YACD,MAAM,EAAE;gBACJ,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7C,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,GAAG;aACzB;SACJ,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,mBAAmB;QACvB,iDAAiD;QACjD,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;gBACrC,EAAE;gBACF,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI;gBACvB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC;aACjD,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,0BAA0B,CAAC,GAAQ;QACvC,mEAAmE;QACnE,MAAM,gBAAgB,GAAG,GAAG,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC;QACjF,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,2BAA2B;QAE5G,OAAO,gBAAgB,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC;IACrD,CAAC;IAEO,eAAe;QACnB,yCAAyC;QACzC,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC;gBAClC,EAAE;gBACF,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI;gBACvB,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,aAAa;gBAChC,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS;gBAC5B,kBAAkB,EAAE,EAAE;aACzB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,6CAA6C;IAE7C,WAAW,CAAC,QAAiB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAC9B,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CACpC,IAAI,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,QAAiB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,aAAa,CAAC,QAAiB,EAAE,MAAc;QAC3C,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YACvC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YACvC,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAE1C,IAAI,eAAe,IAAI,MAAM,GAAG,MAAM,EAAE,CAAC;gBACrC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,WAAW;QACP,OAAO;YACH,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC5B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YACpB,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAC9D,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;SACpD,CAAC;IACN,CAAC;CACJ;AA3OD,gCA2OC"}