"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimationSystem = void 0;
class AnimationSystem {
    constructor() {
        this.frameTime = 0;
        this.BLEND_DURATION = 0.15; // 150ms para blend suave
        this.layers = new Map();
        this.initializeDefaultLayers();
    }
    initializeDefaultLayers() {
        // Camada base para movimento da arma
        this.createLayer('base', 1.0);
        // Camada para ações (tiro, reload)
        this.createLayer('action', 1.0);
        // Camada para movimento do jogador (andar, correr)
        this.createLayer('movement', 0.8);
        // Camada para efeitos adicionais (respiração, cansaço)
        this.createLayer('additive', 0.5);
    }
    createLayer(name, weight) {
        this.layers.set(name, {
            name,
            weight,
            states: new Map(),
            currentState: ''
        });
    }
    addAnimation(layerName, stateName, animation) {
        const layer = this.layers.get(layerName);
        if (!layer)
            return;
        layer.states.set(stateName, {
            current: animation,
            blend: null,
            blendFactor: 0,
            time: 0,
            speed: 1.0,
            loop: true
        });
        // Define primeiro estado se nenhum estiver definido
        if (!layer.currentState) {
            layer.currentState = stateName;
        }
    }
    playAnimation(layerName, stateName, blendDuration = this.BLEND_DURATION) {
        const layer = this.layers.get(layerName);
        if (!layer)
            return;
        const newState = layer.states.get(stateName);
        if (!newState)
            return;
        const currentState = layer.states.get(layer.currentState);
        if (currentState) {
            // Configura blend da animação atual para a nova
            currentState.blend = newState.current;
            currentState.blendFactor = 0;
            // Agenda troca de estado após o blend
            setTimeout(() => {
                layer.currentState = stateName;
            }, blendDuration * 1000);
        }
        else {
            // Se não há estado atual, define direto
            layer.currentState = stateName;
        }
    }
    update(deltaTime) {
        this.frameTime += deltaTime;
        // Atualiza cada camada
        for (const layer of this.layers.values()) {
            const state = layer.states.get(layer.currentState);
            if (!state)
                continue;
            // Atualiza tempo da animação
            state.time += deltaTime * state.speed;
            if (state.loop) {
                state.time %= state.current.duration;
            }
            // Atualiza blend se necessário
            if (state.blend) {
                state.blendFactor = Math.min(1.0, state.blendFactor + deltaTime / this.BLEND_DURATION);
            }
        }
    }
    getLayerTransform(layerName) {
        const layer = this.layers.get(layerName);
        if (!layer)
            return {
                position: { x: 0, y: 0, z: 0 },
                rotation: { x: 0, y: 0, z: 0, w: 1 }
            };
        const state = layer.states.get(layer.currentState);
        if (!state)
            return {
                position: { x: 0, y: 0, z: 0 },
                rotation: { x: 0, y: 0, z: 0, w: 1 }
            };
        // Calcula transformação da animação atual
        const currentTransform = this.evaluateAnimation(state.current, state.time);
        // Se houver blend, interpola com a animação de destino
        if (state.blend && state.blendFactor > 0) {
            const blendTransform = this.evaluateAnimation(state.blend, state.time);
            return this.interpolateTransforms(currentTransform, blendTransform, state.blendFactor);
        }
        return currentTransform;
    }
    evaluateAnimation(animation, time) {
        let position = { x: 0, y: 0, z: 0 };
        let rotation = { x: 0, y: 0, z: 0, w: 1 };
        for (const channel of animation.channels) {
            const frameIndex = this.findFrameIndex(channel.times, time);
            const nextFrameIndex = (frameIndex + 1) % channel.times.length;
            const frameFactor = this.getFrameFactor(channel.times, time, frameIndex, nextFrameIndex);
            // Interpola posição
            if (channel.positions && channel.positions.length > 0) {
                position = this.interpolateVector3(channel.positions[frameIndex], channel.positions[nextFrameIndex], frameFactor);
            }
            // Interpola rotação
            if (channel.rotations && channel.rotations.length > 0) {
                rotation = this.interpolateQuaternion(channel.rotations[frameIndex], channel.rotations[nextFrameIndex], frameFactor);
            }
        }
        return { position, rotation };
    }
    findFrameIndex(times, time) {
        for (let i = 0; i < times.length; i++) {
            if (times[i] > time)
                return Math.max(0, i - 1);
        }
        return times.length - 1;
    }
    getFrameFactor(times, time, frameIndex, nextFrameIndex) {
        const frameDuration = times[nextFrameIndex] - times[frameIndex];
        if (frameDuration === 0)
            return 0;
        return (time - times[frameIndex]) / frameDuration;
    }
    interpolateVector3(a, b, factor) {
        return {
            x: a.x + (b.x - a.x) * factor,
            y: a.y + (b.y - a.y) * factor,
            z: a.z + (b.z - a.z) * factor
        };
    }
    interpolateQuaternion(a, b, factor) {
        // Interpolação esférica (SLERP) para rotações suaves
        const dot = a.x * b.x + a.y * b.y + a.z * b.z + a.w * b.w;
        const theta = Math.acos(Math.min(Math.abs(dot), 1));
        if (theta === 0)
            return a;
        const sinTheta = Math.sin(theta);
        const wa = Math.sin((1 - factor) * theta) / sinTheta;
        const wb = Math.sin(factor * theta) / sinTheta;
        return {
            x: wa * a.x + wb * b.x * Math.sign(dot),
            y: wa * a.y + wb * b.y * Math.sign(dot),
            z: wa * a.z + wb * b.z * Math.sign(dot),
            w: wa * a.w + wb * b.w * Math.sign(dot)
        };
    }
    interpolateTransforms(a, b, factor) {
        return {
            position: this.interpolateVector3(a.position, b.position, factor),
            rotation: this.interpolateQuaternion(a.rotation, b.rotation, factor)
        };
    }
}
exports.AnimationSystem = AnimationSystem;
//# sourceMappingURL=AnimationSystem.js.map