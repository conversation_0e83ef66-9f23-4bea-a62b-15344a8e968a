{"version": 3, "file": "AssetStreamingManager.js", "sourceRoot": "", "sources": ["../../src/rendering/AssetStreamingManager.ts"], "names": [], "mappings": ";;;AAyBA,MAAa,qBAAqB;IAQ9B;QAJiB,kBAAa,GAAG,IAAI,CAAC,CAAC,mCAAmC;QACzD,yBAAoB,GAAG,CAAC,CAAC;QAClC,iBAAY,GAAG,CAAC,CAAC;QAGrB,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;IAClC,CAAC;IAED,kBAAkB,CAAC,MAMlB;QACG,MAAM,IAAI,GAAkB;YACxB,GAAG,MAAM;YACT,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI,GAAG,EAAE;SAC1B,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,cAAuB;QAChC,sDAAsD;QACtD,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvE,MAAM,cAAc,GAAG,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC;YAEtD,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;iBAAM,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,IAAmB;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE9B,6CAA6C;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM;aAC7B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC;aACrC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE7C,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChD,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,IAAmB;QAC5C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEjC,iCAAiC;QACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,KAAgB,EAAE,IAAmB;QACzD,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAAE,OAAO;QAE5C,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC;YACD,+DAA+D;YAC/D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY;YAEpE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACpC,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,YAAY,EAAE,CAAC;QACxB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAgB,EAAE,IAAmB;QAC3D,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,4CAA4C;IAChD,CAAC;IAEO,iBAAiB,CAAC,CAAU,EAAE,CAAU;QAC5C,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;YAAE,OAAO;QAE3D,kEAAkE;QAClE,MAAM,4BAA4B,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;aAC5D,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aAC7B,MAAM,CAAC,CAAC,IAAI,EAAyB,EAAE;YACpC,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEP,IAAI,4BAA4B,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEtD,8BAA8B;QAC9B,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAErE,uBAAuB;QACvB,KAAK,MAAM,IAAI,IAAI,4BAA4B,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;gBAAE,MAAM;YAE1D,iCAAiC;YACjC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CACnC,CAAC;YAEF,wBAAwB;YACxB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAEtD,sCAAsC;YACtC,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAChC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;oBAAE,MAAM;gBAC1D,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAED,sBAAsB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED,mBAAmB;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACjC,CAAC;IAED,oBAAoB;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAClC,CAAC;IAED,iBAAiB;QACb,OAAO;YACH,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAClC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACpC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;SAC1C,CAAC;IACN,CAAC;IAEO,mBAAmB;QACvB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC;QAC9B,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ;AAvKD,sDAuKC"}