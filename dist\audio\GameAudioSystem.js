"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameAudioSystem = void 0;
class GameAudioSystem {
    constructor(audioManager, metrics) {
        this.audioZones = new Map();
        this.materials = new Map();
        this.activeZones = new Set();
        this.currentPlayerPosition = { x: 0, y: 0, z: 0 };
        this.audioManager = audioManager;
        this.performanceMetrics = metrics;
        this.initializeDefaultMaterials();
    }
    initializeDefaultMaterials() {
        this.materials.set('concrete', {
            id: 'concrete',
            absorption: 0.7,
            reflection: 0.6,
            transmission: 0.2
        });
        this.materials.set('metal', {
            id: 'metal',
            absorption: 0.3,
            reflection: 0.9,
            transmission: 0.1
        });
        this.materials.set('wood', {
            id: 'wood',
            absorption: 0.5,
            reflection: 0.4,
            transmission: 0.3
        });
    }
    createAudioZone(config) {
        this.audioZones.set(config.id, config);
        if (this.isPlayerInZone(config)) {
            this.activateZone(config.id);
        }
    }
    updatePlayerPosition(position) {
        this.currentPlayerPosition = position;
        this.updateActiveZones();
    }
    updateActiveZones() {
        const newActiveZones = new Set();
        for (const [zoneId, zone] of this.audioZones) {
            if (this.isPlayerInZone(zone)) {
                newActiveZones.add(zoneId);
                if (!this.activeZones.has(zoneId)) {
                    this.activateZone(zoneId);
                }
            }
        }
        // Desativa zonas que o jogador saiu
        for (const activeZoneId of this.activeZones) {
            if (!newActiveZones.has(activeZoneId)) {
                this.deactivateZone(activeZoneId);
            }
        }
        this.activeZones = newActiveZones;
        this.updateAudioMix();
    }
    isPlayerInZone(zone) {
        const pos = this.currentPlayerPosition;
        const bounds = zone.bounds;
        return pos.x >= bounds.min.x && pos.x <= bounds.max.x &&
            pos.y >= bounds.min.y && pos.y <= bounds.max.y &&
            pos.z >= bounds.min.z && pos.z <= bounds.max.z;
    }
    activateZone(zoneId) {
        const zone = this.audioZones.get(zoneId);
        if (!zone)
            return;
        // Configura reverberação
        this.audioManager.setReverbParameters(zone.reverb);
        // Inicia sons ambientes
        for (const sound of zone.ambientSounds) {
            this.audioManager.playSound({
                id: sound.id,
                volume: sound.volume,
                loop: sound.loop,
                spatial: sound.spatial,
                position: sound.spatial ? {
                    x: (zone.bounds.min.x + zone.bounds.max.x) / 2,
                    y: (zone.bounds.min.y + zone.bounds.max.y) / 2,
                    z: (zone.bounds.min.z + zone.bounds.max.z) / 2
                } : undefined
            });
        }
    }
    deactivateZone(zoneId) {
        const zone = this.audioZones.get(zoneId);
        if (!zone)
            return;
        // Para sons ambientes da zona
        for (const sound of zone.ambientSounds) {
            this.audioManager.stopSound(sound.id);
        }
    }
    updateAudioMix() {
        // Calcula mix baseado em zonas ativas
        const activeZonesArray = Array.from(this.activeZones);
        if (activeZonesArray.length === 0)
            return;
        // Se estiver em múltiplas zonas, faz crossfade
        if (activeZonesArray.length > 1) {
            const weights = this.calculateZoneWeights(activeZonesArray);
            // Simula crossfade (método seria implementado no AudioManager)
            console.log('Aplicando crossfade entre zonas:', weights);
        }
        // Atualiza reverberação baseado na zona mais próxima
        const nearestZone = this.findNearestZone(activeZonesArray);
        if (nearestZone) {
            this.audioManager.setReverbParameters(nearestZone.reverb);
        }
    }
    calculateZoneWeights(zoneIds) {
        const weights = new Map();
        const totalDistance = zoneIds.reduce((sum, id) => {
            const zone = this.audioZones.get(id);
            return sum + this.getDistanceToZoneCenter(zone);
        }, 0);
        for (const id of zoneIds) {
            const zone = this.audioZones.get(id);
            const distance = this.getDistanceToZoneCenter(zone);
            weights.set(id, 1 - (distance / totalDistance));
        }
        return weights;
    }
    getDistanceToZoneCenter(zone) {
        const center = {
            x: (zone.bounds.min.x + zone.bounds.max.x) / 2,
            y: (zone.bounds.min.y + zone.bounds.max.y) / 2,
            z: (zone.bounds.min.z + zone.bounds.max.z) / 2
        };
        const dx = this.currentPlayerPosition.x - center.x;
        const dy = this.currentPlayerPosition.y - center.y;
        const dz = this.currentPlayerPosition.z - center.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    findNearestZone(zoneIds) {
        let nearestZone;
        let minDistance = Infinity;
        for (const id of zoneIds) {
            const zone = this.audioZones.get(id);
            const distance = this.getDistanceToZoneCenter(zone);
            if (distance < minDistance) {
                minDistance = distance;
                nearestZone = zone;
            }
        }
        return nearestZone;
    }
    // Métodos auxiliares para configuração de áudio
    configureInteriorZone(id, size) {
        const presets = {
            small: {
                decay: 0.8,
                preDelay: 20,
                earlyReflections: 0.7,
                lateReflections: 0.5,
                roomSize: 0.3,
                damping: 0.6
            },
            medium: {
                decay: 1.2,
                preDelay: 30,
                earlyReflections: 0.6,
                lateReflections: 0.6,
                roomSize: 0.5,
                damping: 0.5
            },
            large: {
                decay: 2.0,
                preDelay: 50,
                earlyReflections: 0.5,
                lateReflections: 0.7,
                roomSize: 0.8,
                damping: 0.4
            }
        };
        return presets[size];
    }
    async setupAmbientZone(config) {
        // Cria uma zona de áudio ambiental
        const zoneId = `ambient_${Date.now()}`;
        const audioZone = {
            id: zoneId,
            type: 'exterior',
            bounds: {
                min: {
                    x: config.position.x - config.size.width / 2,
                    y: config.position.y - config.size.height / 2,
                    z: config.position.z - config.size.length / 2
                },
                max: {
                    x: config.position.x + config.size.width / 2,
                    y: config.position.y + config.size.height / 2,
                    z: config.position.z + config.size.length / 2
                }
            },
            reverb: {
                decay: 1.0,
                preDelay: 30,
                earlyReflections: 0.5,
                lateReflections: 0.6,
                roomSize: 0.7,
                damping: 0.4
            },
            ambientSounds: config.sounds.map(sound => ({
                id: sound,
                volume: 0.3,
                loop: true,
                spatial: true
            }))
        };
        this.createAudioZone(audioZone);
        console.log(`Zona de áudio ambiental criada: ${zoneId}`);
        return zoneId;
    }
    async configureAmbientZone(config) {
        // Alias para setupAmbientZone para compatibilidade
        return this.setupAmbientZone(config);
    }
}
exports.GameAudioSystem = GameAudioSystem;
//# sourceMappingURL=GameAudioSystem.js.map