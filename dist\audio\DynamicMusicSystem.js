"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DynamicMusicSystem = void 0;
class DynamicMusicSystem {
    constructor(audioSystem, metrics) {
        this.TRANSITION_TIME = 4.0; // 4 segundos para transição
        this.UPDATE_INTERVAL = 0.5; // 500ms entre atualizações
        this.audioSystem = audioSystem;
        this.metrics = metrics;
        this.segments = new Map();
        this.activeSegments = new Set();
        this.transitionTimer = 0;
        this.currentState = {
            intensity: 0,
            playerCount: 0,
            zoneDistance: 1000,
            healthPercent: 100,
            isEndgame: false
        };
        this.initializeMusicSegments();
        this.startUpdateLoop();
    }
    initializeMusicSegments() {
        // Música de Exploração
        this.segments.set('explore_ambient', {
            id: 'explore_ambient',
            path: 'audio/music/dynamic/explore_ambient.ogg',
            intensity: { min: 0, max: 0.3 },
            layers: ['base', 'ambient', 'details'],
            bpm: 85,
            key: 'Am',
            loopPoints: { start: 0, end: 32 }
        });
        // Música de Tensão
        this.segments.set('tension', {
            id: 'tension',
            path: 'audio/music/dynamic/tension.ogg',
            intensity: { min: 0.3, max: 0.6 },
            layers: ['base', 'tension', 'percussion'],
            bpm: 100,
            key: 'Cm',
            loopPoints: { start: 0, end: 32 }
        });
        // Música de Combate
        this.segments.set('combat', {
            id: 'combat',
            path: 'audio/music/dynamic/combat.ogg',
            intensity: { min: 0.6, max: 0.8 },
            layers: ['base', 'combat', 'percussion', 'leads'],
            bpm: 128,
            key: 'Em',
            loopPoints: { start: 0, end: 32 }
        });
        // Música de Endgame
        this.segments.set('endgame', {
            id: 'endgame',
            path: 'audio/music/dynamic/endgame.ogg',
            intensity: { min: 0.8, max: 1.0 },
            layers: ['base', 'tension', 'combat', 'percussion', 'leads'],
            bpm: 140,
            key: 'Dm',
            loopPoints: { start: 0, end: 32 }
        });
        // Camadas adicionais dinâmicas
        this.segments.set('zone_danger', {
            id: 'zone_danger',
            path: 'audio/music/dynamic/zone_danger.ogg',
            intensity: { min: 0.4, max: 1.0 },
            layers: ['danger'],
            bpm: 120,
            key: 'universal',
            loopPoints: { start: 0, end: 16 }
        });
        this.segments.set('low_health', {
            id: 'low_health',
            path: 'audio/music/dynamic/low_health.ogg',
            intensity: { min: 0.6, max: 1.0 },
            layers: ['heartbeat'],
            bpm: 120,
            key: 'universal',
            loopPoints: { start: 0, end: 8 }
        });
    }
    startUpdateLoop() {
        setInterval(() => this.updateMusic(), this.UPDATE_INTERVAL * 1000);
    }
    updateGameState(newState) {
        this.currentState = {
            ...this.currentState,
            ...newState
        };
    }
    updateMusic() {
        const targetSegments = this.determineTargetSegments();
        // Verifica se precisa de transição
        if (!this.areSetsEqual(targetSegments, this.activeSegments)) {
            this.transitionToNewSegments(targetSegments);
        }
        // Atualiza volumes baseado no estado
        this.updateLayerVolumes();
    }
    determineTargetSegments() {
        const targets = new Set();
        // Seleciona segmento base pela intensidade
        if (this.currentState.isEndgame) {
            targets.add('endgame');
        }
        else if (this.currentState.intensity >= 0.6) {
            targets.add('combat');
        }
        else if (this.currentState.intensity >= 0.3) {
            targets.add('tension');
        }
        else {
            targets.add('explore_ambient');
        }
        // Adiciona camadas condicionais
        if (this.currentState.zoneDistance < 200) {
            targets.add('zone_danger');
        }
        if (this.currentState.healthPercent < 30) {
            targets.add('low_health');
        }
        return targets;
    }
    async transitionToNewSegments(targetSegments) {
        // Fade out segmentos que serão removidos
        for (const segmentId of this.activeSegments) {
            if (!targetSegments.has(segmentId)) {
                await this.audioSystem.fadeOutMusic(segmentId, this.TRANSITION_TIME);
            }
        }
        // Fade in novos segmentos
        for (const segmentId of targetSegments) {
            if (!this.activeSegments.has(segmentId)) {
                const segment = this.segments.get(segmentId);
                await this.audioSystem.playMusic(segmentId, {
                    fadeInTime: this.TRANSITION_TIME,
                    volume: this.calculateSegmentVolume(segment),
                    loop: true,
                    loopStart: segment.loopPoints.start,
                    loopEnd: segment.loopPoints.end
                });
            }
        }
        this.activeSegments = new Set(targetSegments);
    }
    updateLayerVolumes() {
        for (const segmentId of this.activeSegments) {
            const segment = this.segments.get(segmentId);
            const volume = this.calculateSegmentVolume(segment);
            this.audioSystem.setMusicVolume(segmentId, volume);
        }
    }
    calculateSegmentVolume(segment) {
        let baseVolume = 1.0;
        // Ajusta volume baseado na intensidade
        if (segment.intensity) {
            const intensityFactor = Math.min(1, Math.max(0, (this.currentState.intensity - segment.intensity.min) /
                (segment.intensity.max - segment.intensity.min)));
            baseVolume *= intensityFactor;
        }
        // Ajustes específicos por tipo
        switch (segment.id) {
            case 'zone_danger':
                baseVolume *= Math.max(0, 1 - (this.currentState.zoneDistance / 200));
                break;
            case 'low_health':
                baseVolume *= Math.max(0, 1 - (this.currentState.healthPercent / 30));
                break;
        }
        return baseVolume;
    }
    areSetsEqual(a, b) {
        return a.size === b.size && [...a].every(value => b.has(value));
    }
    setGlobalIntensity(intensity) {
        this.updateGameState({ intensity: Math.max(0, Math.min(1, intensity)) });
    }
    triggerCombatTransition() {
        this.setGlobalIntensity(0.7);
    }
    triggerEndgameTransition() {
        this.updateGameState({
            isEndgame: true,
            intensity: 0.9
        });
    }
}
exports.DynamicMusicSystem = DynamicMusicSystem;
//# sourceMappingURL=DynamicMusicSystem.js.map