{"version": 3, "file": "LatencyCompensation.js", "sourceRoot": "", "sources": ["../../src/netcode/LatencyCompensation.ts"], "names": [], "mappings": ";;;AAEA,2CAA2C;AAC3C,MAAM,wBAAwB,GAAG,IAAI,CAAC,CAAC,qCAAqC;AAC5E,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,uDAAuD;AAMjF,MAAa,mBAAmB;IAG5B;QAH0C,iBAAY,GAA0C,IAAI,GAAG,EAAE,CAAC;QAClG,gBAAW,GAA+B,IAAI,GAAG,EAAE,CAAC;QAGxD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,iBAAiB;QACrB,kDAAkD;IACtD,CAAC;IAEM,cAAc,CAAC,QAAgB,EAAE,KAAkB,EAAE,SAAiB;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QAEjD,4CAA4C;QAC5C,MAAM,gBAAgB,GAA2B,EAAE,GAAG,KAAK,EAAE,SAAS,EAAE,CAAC;QACzE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/B,yBAAyB;QACzB,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC;YAClB,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,wBAAwB,EAAE,CAAC;YACjE,OAAO,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,SAAiB;QAC/B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAuB,CAAC;QAEpD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC5C,uDAAuD;YACvD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACxD,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,KAAK,CAAC;gBAC/C,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACxB,CAAC;IAAY,gBAAgB,CAAC,OAAiC,EAAE,SAAiB;QAC9E,qDAAqD;QACrD,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/B,OAAO,IAAI,IAAI,KAAK,EAAE,CAAC;YACnB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAE3B,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAChC,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,KAAK,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC;gBAC9B,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;YACpB,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;YAChE,OAAO,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnE,CAAC;IAEM,kBAAkB,CAAC,YAAyB,EAAE,KAAkB,EAAE,SAAiB;QACtF,uCAAuC;QACvC,MAAM,cAAc,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;QAE3C,mBAAmB;QACnB,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,uBAAuB;QAC5C,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACtE,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QAEtE,oCAAoC;QACpC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5C,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,GAAG,SAAS,CAAC,CAAC,YAAY;YAC3D,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;QACvE,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,UAAU,CAAC,QAAiB;QAChC,sDAAsD;QACtD,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEM,cAAc,CAAC,cAA2B,EAAE,WAAwB;QACvE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,6BAA6B;QACpD,MAAM,UAAU,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC;QAEtC,qDAAqD;QACrD,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG,SAAS,EAAE,CAAC;YACnE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACpF,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACxF,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,kBAAkB,CAAC,MAAmB,EAAE,MAAmB;QAC/D,wDAAwD;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzE,OAAO,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;IACnC,CAAC;IAEO,iBAAiB,CAAC,EAAW,EAAE,EAAW;QAC9C,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,IAAI,CAAC,EAAW,EAAE,EAAW,EAAE,CAAS;QAC5C,OAAO;YACH,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YAC3B,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YAC3B,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;SAC9B,CAAC;IACN,CAAC;CACJ;AAjID,kDAiIC"}