{"version": 3, "file": "TextureCompressor.js", "sourceRoot": "", "sources": ["../../src/rendering/TextureCompressor.ts"], "names": [], "mappings": ";;;AAAA,2CAAyC;AA+BzC,MAAa,iBAAiB;IAO1B,YAAY,OAA2B;QAL/B,eAAU,GAAgC,IAAI,CAAC;QAMnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,UAAU;QACnB,2CAA2C;QAC3C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE/C,IAAI,CAAC,UAAU,GAAG,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE;YACvD,GAAG,EAAE;gBACD,MAAM,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;gBAChD,+BAA+B,EAAE,GAAG,EAAE,GAAE,CAAC;aAC5C;SACJ,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,YAAY,GAAG;YAChB,iDAAiD;YACjD,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY;YACrD,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU;YACjD,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe;SAC9D,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,eAAe,CACxB,SAAoB,EACpB,OAAqB;QAErB,MAAM,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,qBAAqB;QACrB,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAC5C,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,CAAC,CAAC,OAAO;SACZ,CAAC;QAEF,qBAAqB;QACrB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEpE,yBAAyB;QACzB,MAAM,eAAe,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACtD,MAAM,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC;QAEjD,wBAAwB;QACxB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAErE,qCAAqC;QACrC,MAAM,mBAAmB,GAAG,IAAI,CAAC,2BAA2B,CACxD,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,OAAO,CAAC,MAAM,CACjB,CAAC;QAEF,MAAM,OAAO,GAA8B;YACvC,YAAY;YACZ,cAAc;YACd,gBAAgB,EAAE,YAAY,GAAG,cAAc;YAC/C,eAAe;YACf,eAAe,EAAE,CAAC,EAAE,6CAA6C;YACjE,iBAAiB;YACjB,mBAAmB;YACnB,OAAO;SACV,CAAC;QAEF,4BAA4B;QAC5B,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAEvC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,gBAAgB,CACzB,cAA0B,EAC1B,YAAqC;QAErC,MAAM,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,6CAA6C;QAC7C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CACpD,cAAc,EACd,YAAY,CACf,CAAC;QAEF,MAAM,eAAe,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEtD,wCAAwC;QACxC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;QAErE,OAAO,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,aAAa,CACvB,SAAoB,EACpB,OAAqB;QAErB,sCAAsC;QACtC,MAAM,aAAa,GAAG;YAClB,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,SAAS,EAAE,OAAO,CAAC,gBAAgB;YACnC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,QAAQ,EAAE,OAAO,CAAC,SAAS;YAC3B,YAAY,EAAE,OAAO,CAAC,oBAAoB;YAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;SACjC,CAAC;QAEF,8BAA8B;QAC9B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CACjC,SAAS,CAAC,IAAI,EACd,aAAa,CAChB,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,cAAc,CACxB,QAAmB,EACnB,UAAsB;QAEtB,sCAAsC;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QAC5C,CAAC;QAED,+CAA+C;QAC/C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAE7D,gCAAgC;QAChC,MAAM,OAAO,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;YACpD,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;YACpD,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;SAC9D,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEzC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAgB;QAC3C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;aACxC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aACzC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,cAA0B;QACtD,yCAAyC;QACzC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;IACrE,CAAC;IAEO,aAAa,CAAC,QAAoB,EAAE,OAAmB;QAC3D,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACtC,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC;QACvB,CAAC;QACD,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC;QAEvB,IAAI,GAAG,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC/B,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;IAEO,aAAa,CAAC,QAAoB,EAAE,OAAmB;QAC3D,qCAAqC;QACrC,6CAA6C;QAC7C,OAAO,IAAI,CAAC,CAAC,gBAAgB;IACjC,CAAC;IAEO,eAAe,CAAC,QAAoB,EAAE,OAAmB;QAC7D,kCAAkC;QAClC,+CAA+C;QAC/C,OAAO,IAAI,CAAC,CAAC,kCAAkC;IACnD,CAAC;IAEO,iBAAiB,CACrB,KAAa,EACb,MAAc,EACd,aAAqB;QAErB,uCAAuC;QACvC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,GAAG,KAAK,CAAC;QACd,IAAI,CAAC,GAAG,MAAM,CAAC;QAEf,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;YAC/B,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,2BAA2B,CAC/B,KAAa,EACb,MAAc,EACd,MAA8B;QAE9B,8CAA8C;QAC9C,MAAM,SAAS,GAAG,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QACrE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3C,OAAO,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;IAClD,CAAC;IAEO,wBAAwB,CAAC,OAAkC;QAC/D,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,yBAAyB,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC/E,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,wBAAwB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,wBAAwB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,EACzC,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAChE,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC7E,CAAC;CACJ;AA1OD,8CA0OC"}